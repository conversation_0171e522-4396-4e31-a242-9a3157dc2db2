

**Besprechungsprotokoll**

**Thema:** Verfeinerung der Variablenbehandlung, Dateivariablen und Struktur von Bulk-Jobs im Verarbeitungssystem

**Teilnehmer:** Zwei Entwickler (Entwickler 1 leitet die Erklärung, Entwickler 2 stellt klärende Fragen)

**Zentrale Diskussionspunkte:**

1.  **Allgemeine Variablenbehandlung:**
    *   Variablen, die einem Task bereitgestellt werden, sind ein *gemergter* (zusammengeführter) Satz aus verschiedenen Hierarchieebenen (z.B. Kampagne, Template, Datensatz).
    *   Es gibt keine strikte *technische* Notwendigkeit auf der Kern-Verarbeitungsebene, zwischen datensatzspezifischen Variablen und anderen geerbten Variablen/Einstellungen zu unterscheiden.
    *   Die ursprüngliche Idee einer separaten Struktur für "Datensatzvariablen" ist wahrscheinlich unnötig.

2.  **Benennung und Struktur von Dateivariablen:**
    *   Eine klare Namenskonvention für Dateivariablen ist für die Klarheit erforderlich (nicht unbedingt aus technischen Gründen, sondern für menschliches Verständnis und Wartung).
    *   Vorgeschlagene Konvention: `input_file_<typ>`, `output_file_<typ>`, unter Verwendung von `_a_` für Arrays (z.B. `input_a_<typ>`).
    *   Der `<typ>` sollte beschreibend sein (z.B. `tix_imu`, `rinex_rover`, `tix_for_kml`).
    *   Das `data`-Feld für eine Dateivariable kann sein:
        *   Ein einfacher **String**: Repräsentiert den vollständigen Pfad zur Datei (Abwärtskompatibilität).
        *   Ein **JSON-Objekt**: Dies ist die bevorzugte neue Struktur.
            *   Obligatorisches Feld: `filename` (kann den vollständigen Pfad enthalten, wenn `path` weggelassen wird).
            *   Optionales Feld: `path` (Verzeichnispfad, wird mit `filename` verkettet, falls vorhanden).
            *   Zusätzliche Schlüssel-Wert-Paare: Repräsentieren **dateispezifische Variablen** (z.B. Antennenreferenzpunkt-Koordinaten für eine RINEX-Datei). Diese werden *nicht* global gemerged, sondern bleiben mit diesem spezifischen Dateiobjekt verbunden.

3.  **Implementierung von Dateivariablen:**
    *   Der Kernel muss beide Formate (String und JSON-Objekt) innerhalb des `data`-Feldes für Dateivariablen handhaben können. Wenn es nur ein String ist, kann der Kernel es intern als Objekt behandeln, bei dem nur `filename` gefüllt ist und andere dateispezifische Variablen leer sind.
    *   Die GUI/Datenbank muss die Speicherung dieser dateispezifischen Variablen, die mit einer Datei verbunden sind, unterstützen.
    *   Es wird vorgeschlagen, *immer* die JSON-Objekt-Struktur aus der GUI zu generieren, um Konsistenz zu gewährleisten, auch wenn nur `filename` gefüllt ist. Die String-Format-Unterstützung im Kernel dient hauptsächlich der Abwärtskompatibilität oder einfacheren Entwicklungsfällen.

4.  **Bulk-Jobs:**
    *   Bulk-Jobs operieren auf den Ergebnissen *mehrerer* zuvor verarbeiteter einzelner Tasks/Datensätze.
    *   Die für einen Bulk-Job benötigten Ergebnisse werden über eine dedizierte Variable übergeben, wahrscheinlich `TaskResult` genannt.
    *   Das `data`-Feld der `TaskResult`-Variable wird ein **Array von Objekten** sein.
    *   Jedes Objekt im Array repräsentiert einen abgeschlossenen Task/Datensatz und enthält:
        *   *Alle* Variablen (gemerged aus allen Ebenen), die dem ursprünglichen Task zur Verfügung standen.
        *   *Zuzüglich* neu generierter Informationen, hauptsächlich die **Ausgabedateien**, die von diesem Task erstellt wurden (unter Verwendung der `output_file_<typ>`-Konvention).
    *   Diese Struktur vermeidet komplexe Unterstrukturierungen innerhalb des `TaskResult`-Objekts selbst. Jedes Element des Arrays ist eine flache Sammlung von Schlüssel-Wert-Paaren, die den Zustand und die Ergebnisse eines Tasks repräsentieren.

5.  **Variablenstruktur (Allgemein - Offener Punkt):**
    *   Die aktuelle Struktur `[{ "name": "varname", "data": ..., "links": ... }]` wurde diskutiert im Vergleich zu einem direkteren JSON-Ansatz `{ "varname": { "data": ..., "links": ... } }` oder sogar nur `{ "varname": ... }` für einfache Variablen.
    *   Der direkte JSON-Ansatz ist sauberer für den programmatischen Zugriff (`vars.varname` vs. Iterieren und Prüfen von `name`).
    *   Die Änderung der fundamentalen Struktur bricht jedoch die Abwärtskompatibilität und erfordert signifikantes Refactoring.
    *   Eine Entscheidung über das Refactoring wurde *nicht* finalisiert, aber die Präferenz scheint in Richtung der saubereren JSON-Struktur zu gehen, falls dies zukünftig machbar ist. Vorerst bleibt die bestehende Name/Data/Links-Struktur für allgemeine Variablen bestehen.

6.  **Template `required`-Feld:**
    *   Templates sollten benötigte Dateien (sowohl Eingabe- als auch Ausgabetypen) explizit in einem `required`-Abschnitt (z.B. `required_files`) auflisten.
    *   Dies erlaubt der GUI vorab zu prüfen, ob alle notwendigen Daten verfügbar sind, und nur die benötigten Dateien abzurufen, was die Effizienz verbessert.
    *   Potenziell könnte dies auf Nicht-Datei-Variablen (`required_vars`) erweitert werden für Vorabprüfungen in der GUI, was die Fehlerbehandlung vor der Kernel-Ausführung verbessert.

7.  **Spezifische Variablenbehandlung (`init_heading_degrees`):**
    *   Variablen, die über mehrere Dimensionen verlinkt sind (wie `init_heading_degrees` mit `Forward` und `Dataset/F`), sind komplex.
    *   Entscheidung: Solche Variablen in separate Variablen aufteilen (z.B. `init_heading_forward_degrees`, `init_heading_reverse_degrees`), die jeweils nur mit einer Dimension (z.B. `F`) verknüpft sind.

8.  **ATX-Datei-Handhabung:**
    *   Derzeit über einen hartcodierten Pfad in der GUI/manuellen Einrichtung gehandhabt.
    *   Vorschlag: Wie jede andere Eingabedatei-Variable behandeln, verwaltet innerhalb der Variablenstruktur des Systems.

**Getroffene Entscheidungen:**

1.  Namenskonventionen für Dateien übernommen (`input_file_<typ>`, `output_file_<typ>`, etc.).
2.  Das `data`-Feld für Dateivariablen unterstützt sowohl String (Pfad) als auch JSON-Objekt-Formate; das Objektformat wird zukünftig bevorzugt.
3.  Dateispezifische Variablen befinden sich innerhalb des `data`-Objekts der Datei und werden nicht global gemerged.
4.  Die Variable `init_heading_degrees` wird basierend auf ihren Verknüpfungen (`forward`/`reverse`) aufgeteilt.
5.  Bulk-Jobs erhalten Eingaben über eine `TaskResult`-Variable, die ein Array von flachen Objekten enthält, welche frühere Task-Zustände/Ergebnisse repräsentieren.
6.  Templates verwenden ein `required_files`-Feld (oder ähnlich), um notwendige Dateien für den GUI-Abruf aufzulisten.
7.  Die GUI sollte nur das `data`-Feld von Variablen füllen, die im Template definiert sind; das `links`-Feld sollte respektiert und nicht überschrieben werden.

**Nächste Schritte:**

1.  **Entwickler 1 (Kernel):**
    *   Kernel aktualisieren, um sowohl String- als auch JSON-Objekt-Formate für das `data`-Feld von Dateivariablen zu parsen.
    *   Template `job_tightly.json` aktualisieren, um die neue Dateivariablen-Benennung, Struktur für dateispezifische Variablen (z.B. ARP), geteilte `init_heading` und `required_files` widerzuspiegeln.
    *   Handhabung der `TaskResult`-Variablenstruktur für Bulk-Jobs implementieren.
    *   Erwägen, die `ATX`-Datei zu einer Standard-Eingabevariable zu machen.
2.  **Entwickler 2 (GUI/DB):**
    *   Speicherung/Abruf für dateispezifische Variablen implementieren, die mit Dateieinträgen verbunden sind.
    *   GUI aktualisieren, um die JSON-Objekt-Struktur für das `data`-Feld von Dateivariablen zu generieren.
    *   Sicherstellen, dass die GUI-Merge-Logik das `links`-Feld aus dem Template *erhält* und nur das `data`-Feld modifiziert/bereitstellt.
    *   Logik implementieren, um `required_files` aus dem Template zu lesen und entsprechende Dateien abzurufen.
    *   (Aktuelle Implementierung prüfen) Verifizieren, dass die `links`-Handhabung beim Mergen korrekt ist.

**Offene Punkte:**

*   Entscheidung über ein potenzielles zukünftiges Refactoring der allgemeinen Variablenstruktur (Name/Data/Links vs. direktes JSON Schlüssel-Wert).