# AlgoNav Positioning Cloud API Documentation

## Introduction

Welcome to the AlgoNav Positioning Cloud API documentation. This API allows you to interact with the AlgoNav Cloud system, managing job templates, datasets, files, jobs, and batches.

## Authentication

All API requests require an API key for authentication. Include the API key in the header of each request:

§§§
X-API-Key: YOUR_API_KEY_HERE
§§§

## Base URL

All API requests should be made to:

§§§
https://api.algonav.de/v1
§§§

## Endpoints

### Global Job Templates

#### List Global Job Templates

§§§
GET /global-job-templates
§§§

Response:
§§§json
[
  {
    "id": 1,
    "name": "Template 1",
    "description": "Description of Template 1",
    "template_data": {}
  },
  ...
]
§§§

#### Get a Global Job Template

§§§
GET /global-job-templates/{id}
§§§

Response:
§§§json
{
  "id": 1,
  "name": "Template 1",
  "description": "Description of Template 1",
  "template_data": {}
}
§§§

#### Create a Global Job Template

§§§
POST /global-job-templates
§§§

Request Body:
§§§json
{
  "name": "New Template",
  "description": "Description of New Template",
  "template_data": {}
}
§§§

#### Update a Global Job Template

§§§
PUT /global-job-templates/{id}
§§§

Request Body:
§§§json
{
  "name": "Updated Template",
  "description": "Updated Description",
  "template_data": {}
}
§§§

#### Delete a Global Job Template

§§§
DELETE /global-job-templates/{id}
§§§

### Datasets

#### List Datasets

§§§
GET /datasets
§§§

#### Get a Dataset

§§§
GET /datasets/{id}
§§§

#### Create a Dataset

§§§
POST /datasets
§§§

Request Body:
§§§json
{
  "name": "New Dataset",
  "description": "Description of New Dataset",
  "variable_overrides": {}
}
§§§

#### Update a Dataset

§§§
PUT /datasets/{id}
§§§

#### Delete a Dataset

§§§
DELETE /datasets/{id}
§§§

#### List Files in a Dataset

§§§
GET /datasets/{id}/files
§§§

#### Add File to Dataset

§§§
POST /datasets/{dataset_id}/files/{file_id}
§§§

Request Body:
§§§json
{
  "file_type": "gnss_rinex"
}
§§§

#### Remove File from Dataset

§§§
DELETE /datasets/{dataset_id}/files/{file_id}
§§§

### Files

#### List Files

§§§
GET /files
§§§

#### Get a File

§§§
GET /files/{id}
§§§

#### Upload a File

§§§
POST /files
§§§

Use multipart/form-data to upload the file.

#### Delete a File

§§§
DELETE /files/{id}
§§§

### Jobs

#### List Jobs

§§§
GET /jobs
§§§

#### Get a Job

§§§
GET /jobs/{id}
§§§

#### Create a Job

§§§
POST /jobs
§§§

Request Body:
§§§json
{
  "name": "New Job",
  "description": "Description of New Job",
  "global_job_template_id": 1,
  "dataset_id": 1,
  "batch_id": null,
  "job_json": {},
  "status": "pending"
}
§§§

Note: `global_job_template_id` and `dataset_id` are required. `batch_id` is optional.

#### Update a Job

§§§
PUT /jobs/{id}
§§§

#### Delete a Job

§§§
DELETE /jobs/{id}
§§§

### Batches

#### List Batches

§§§
GET /batches
§§§

#### Get a Batch

§§§
GET /batches/{id}
§§§

#### Create a Batch

§§§
POST /batches
§§§

Request Body:
§§§json
{
  "name": "New Batch",
  "description": "Description of New Batch",
  "status": "pending"
}
§§§

#### Update a Batch

§§§
PUT /batches/{id}
§§§

#### Delete a Batch

§§§
DELETE /batches/{id}
§§§

#### List Jobs in a Batch

§§§
GET /batches/{id}/jobs
§§§

#### Add Job to Batch

§§§
POST /batches/{batch_id}/jobs/{job_id}
§§§

#### Remove Job from Batch

§§§
DELETE /batches/{batch_id}/jobs/{job_id}
§§§

## Error Handling

The API uses conventional HTTP response codes to indicate the success or failure of an API request. In general:

- 2xx range indicate success
- 4xx range indicate an error that failed given the information provided (e.g., a required parameter was omitted, etc.)
- 5xx range indicate an error with our servers

### Error Response Format

§§§json
{
  "error": {
    "code": "error_code",
    "message": "A human-readable error message"
  }
}
§§§

## Rate Limiting

The API implements rate limiting to prevent abuse. The current rate limit is 100 requests per minute per API key. If you exceed this limit, you'll receive a 429 Too Many Requests response.

## Versioning

The API is versioned, and the current version is v1. We recommend specifying a version in the URL to ensure your integration doesn't break when we release new versions.

## Support

If you have any questions or need assistance, please contact our support <NAME_EMAIL>.