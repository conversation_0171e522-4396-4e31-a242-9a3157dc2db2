import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const POST = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { name, description, parentCategoryId, variableOverrides } = await request.json();

    const { data, error } = await supabase
        .from('CATEGORIES')
        .insert({ 
            user_id: userId, 
            name, 
            description, 
            parent_category_id: parentCategoryId, 
            variable_overrides: variableOverrides 
        })
        .select();

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
});

export const GET = withAuth(async (userId) => {
    const supabase = createClient();
    const { data, error } = await supabase
        .from('CATEGORIES')
        .select('*')
        .eq('user_id', userId);

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
});

export const PUT = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { id, name, description, parentCategoryId, variableOverrides } = await request.json();

    const { data, error } = await supabase
        .from('CATEGORIES')
        .update({ 
            name, 
            description, 
            parent_category_id: parentCategoryId, 
            variable_overrides: variableOverrides 
        })
        .eq('id', id)
        .eq('user_id', userId)
        .select();

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
});

export const DELETE = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const id = searchParams.get('id');

    const { error } = await supabase
        .from('CATEGORIES')
        .delete()
        .eq('id', id)
        .eq('user_id', userId);

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true });
});
