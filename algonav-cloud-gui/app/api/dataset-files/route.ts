import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const datasetId = searchParams.get('datasetId');

    if (!datasetId) {
        return NextResponse.json({ error: 'Dataset ID is required' }, { status: 400 });
    }

    const { data, error } = await supabase
        .from('dataset_files')
        .select(`
            file_id,
            file_type,
            files:file_id (
                id,
                file_name,
                file_path,
                file_size,
                content_type
            )
        `)
        .eq('dataset_id', datasetId);

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
});
