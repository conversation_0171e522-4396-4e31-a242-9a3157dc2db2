import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const limitParam = searchParams.get('limit');
    const limit = limitParam ? parseInt(limitParam) : 5;

    // Fetch recent datasets ordered by creation timestamp (newest first)
    const { data: datasetsData, error } = await supabase
        .from('datasets')
        .select('id, name, description, created_at, updated_at')
        .eq('user_id', userId)
        .order('created_at', { ascending: false })
        .limit(limit);

    if (error) {
        console.error("Error fetching recent datasets:", error);
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data: datasetsData || [] });
});
