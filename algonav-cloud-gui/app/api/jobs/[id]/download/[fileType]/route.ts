import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";
import JSZip from 'jszip';

export const runtime = 'nodejs';

export const GET = withAuth(async (userId, request, { params }) => {
    const supabase = createClient();
    const jobId = Array.isArray(params.id) ? params.id[0] : params.id; // Renamed batchId to jobId
    const fileType = Array.isArray(params.fileType) ? params.fileType[0] : params.fileType;

    if (!jobId || !fileType) { // Renamed batchId to jobId
        return NextResponse.json({ error: 'Invalid parameters' }, { status: 400 });
    }

    // First get all tasks in the job (formerly jobs in batch)
    const { data: tasks, error: tasksError } = await supabase // Renamed jobs to tasks, jobsError to tasksError
        .from('tasks') // Renamed jobs to tasks
        .select('id')
        .eq('job_id', jobId) // Renamed batch_id to job_id, batchId to jobId
        .eq('user_id', userId)
        .is('bulk_job_type', null); // Filter out bulk jobs

    if (tasksError) { // Renamed jobsError to tasksError
        return NextResponse.json({ error: tasksError.message }, { status: 500 });
    }

    if (!tasks || tasks.length === 0) { // Renamed jobs to tasks
        return NextResponse.json({ error: 'No tasks found in job' }, { status: 404 }); // Renamed jobs to tasks, batch to job
    }

    // Get all results for all tasks in the job with the specified file type
    const taskIds = tasks.map(task => task.id); // Renamed jobIds to taskIds, jobs to tasks, job to task
    const { data: results, error: resultsError } = await supabase
        .from('task_results') // This table name is correct
        .select('*')
        .in('task_id', taskIds) // Renamed job_id to task_id, jobIds to taskIds
        .eq('file_type', fileType)
        .eq('visible', true);

    if (resultsError) {
        return NextResponse.json({ error: resultsError.message }, { status: 500 });
    }

    if (!results || results.length === 0) {
        return NextResponse.json({ error: 'No results found for this file type' }, { status: 404 });
    }

    const zip = new JSZip();
    for (const result of results) {
        try {
            let storagePath = result.file_path;
            if (storagePath.startsWith('/')) {
                storagePath = storagePath.substring(1);
            }

            const { data, error: downloadError } = await supabase.storage
                .from(process.env.NEXT_PUBLIC_RESULT_BUCKET_NAME || 'job-results')
                .download(storagePath);

            if (downloadError) {
                console.error(`Error downloading file ${result.file_name}:`, downloadError);
                continue;
            }

            let fileData;
            if (typeof data.arrayBuffer === 'function') {
                const arrayBuffer = await data.arrayBuffer();
                fileData = new Uint8Array(arrayBuffer);
            } else {
                fileData = data;
            }

            // Organize files by job ID to maintain structure
            const taskFolder = `task_${result.task_id}`; // Renamed jobFolder to taskFolder, result.job_id to result.task_id
            zip.file(`${taskFolder}/${result.file_name}`, fileData); // Used taskFolder
        } catch (error) {
            console.error(`Error processing file ${result.file_name}:`, error);
        }
    }

    const zipContent = await zip.generateAsync({ type: 'nodebuffer' });

    // Use a descriptive name for the zip file based on the file type
    const zipFileName = `job_${jobId}_${fileType}_files.zip`; // Renamed batch to job, batchId to jobId

    return new NextResponse(zipContent, {
        headers: {
            'Content-Type': 'application/zip',
            'Content-Disposition': `attachment; filename="${zipFileName}"`
        }
    });
});