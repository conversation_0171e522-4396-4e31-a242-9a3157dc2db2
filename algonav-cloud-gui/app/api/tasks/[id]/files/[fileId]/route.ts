import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const GET = withAuth(async (userId: string, request: Request, context: { params: Record<string, string | string[]> }) => {
    const supabase = createClient();
    const taskId = Array.isArray(context.params.id) ? context.params.id[0] : context.params.id; // Renamed jobId to taskId
    const fileId = Array.isArray(context.params.fileId) ? context.params.fileId[0] : context.params.fileId;

    if (!taskId || !fileId) { // Renamed jobId to taskId
        console.error('Missing taskId or fileId', { taskId, fileId }); // Renamed jobId to taskId
        return NextResponse.json({ error: 'Invalid parameters' }, { status: 400 });
    }

    // Get the job result record
    const { data: jobResult, error: jobError } = await supabase
        .from('task_results')
        .select(`
            id,
            task_id,
            file_name,
            file_path,
            content_type,
            file_type
        `)
        .eq('id', fileId)
        .single();

    if (jobError) {
        console.error('Job result fetch error:', jobError);
        return NextResponse.json({ error: 'File not found', details: jobError.message }, { status: 404 });
    }

    if (!jobResult) {
        console.error('Job result not found for ID:', fileId);
        return NextResponse.json({ error: 'File not found' }, { status: 404 });
    }

    console.log('Processing download request for:', {
        fileId,
        fileName: jobResult.file_name,
        filePath: jobResult.file_path,
        fileType: jobResult.file_type,
        contentType: jobResult.content_type
    });

    // Verify that the task belongs to the user
    const { data: task, error: taskAccessError } = await supabase // Renamed job to task, jobAccessError to taskAccessError
        .from('tasks') // Renamed jobs to tasks
        .select('id, job_id') // Renamed batch_id to job_id in tasks table
        .eq('id', jobResult.task_id) // jobResult.job_id is now jobResult.task_id
        .eq('user_id', userId)
        .single();

    if (taskAccessError || !task) { // Renamed jobAccessError to taskAccessError, job to task
        console.error('Task access error:', taskAccessError); // Updated error message
        return NextResponse.json({ error: 'Unauthorized' }, { status: 403 });
    }

    // Transform the file path to match storage structure
    let storagePath = jobResult.file_path;
    if (storagePath.startsWith('/')) {
        storagePath = storagePath.substring(1);
    }

    // Download the file
    const { data, error: downloadError } = await supabase
        .storage
        .from(process.env.NEXT_PUBLIC_RESULT_BUCKET_NAME || 'job-results')
        .download(storagePath);

    if (downloadError) {
        console.error('Download failed:', downloadError);
        return NextResponse.json({ 
            error: 'Failed to access file',
            details: downloadError.message
        }, { status: 500 });
    }

    // Use simple Content-Disposition with the original filename
    const headers = new Headers({
        'Content-Type': jobResult.content_type || 'application/octet-stream',
        'Content-Disposition': `attachment; filename=${jobResult.file_name}`
    });

    console.log('Sending file with headers:', {
        filename: jobResult.file_name,
        contentType: jobResult.content_type,
        contentDisposition: headers.get('Content-Disposition')
    });

    return new NextResponse(data, { headers });
});