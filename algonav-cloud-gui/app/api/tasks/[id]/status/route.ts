import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from '@/utils/supabase/server';

/**
 * GET endpoint to retrieve the status of a job
 */
export const GET = withAuth(async (userId, request, { params }) => {
  const taskId = params?.id; // Renamed jobId to taskId

  if (!taskId) { // Renamed jobId to taskId
    return NextResponse.json(
      { error: 'Task ID is required' }, // Updated error message
      { status: 400 }
    );
  }

  const supabase = createClient();
  
  // Get the task status
  const { data: task, error } = await supabase // Renamed job to task
    .from('tasks') // Renamed jobs to tasks
    .select('id, status, result')
    .eq('id', taskId) // Renamed jobId to taskId
    .eq('user_id', userId)
    .single();

  if (error) {
    return NextResponse.json(
      { error: 'Failed to fetch task status' }, // Updated error message
      { status: 500 }
    );
  }

  if (!task) { // Renamed job to task
    return NextResponse.json(
      { error: 'Task not found' }, // Updated error message
      { status: 404 }
    );
  }

  // Map database status to API response status
  let status;
  switch (task.status) { // Renamed job.status to task.status
    case 'pending':
    case 'creating':
    case 'queued':
      status = 'creating';
      break;
    case 'Processing':
      status = 'processing';
      break;
    case 'Completed':
      status = 'complete';
      break;
    case 'Failed':
    case 'error':
      status = 'error';
      break;
    default:
      status = 'processing';
  }

  // Extract error message from result if it exists and task status is error
  let errorMessage = null;
  if ((status === 'error') && task.result) { // Renamed job.result to task.result
    try {
      // If result is already parsed as JSON
      if (typeof task.result === 'object' && task.result.error_message) { // Renamed job.result to task.result
        errorMessage = task.result.error_message; // Renamed job.result to task.result
      }
      // If result is a string that needs to be parsed
      else if (typeof task.result === 'string') { // Renamed job.result to task.result
        const resultObj = JSON.parse(task.result); // Renamed job.result to task.result
        errorMessage = resultObj.error_message || null;
      }
    } catch (e) {
      console.error('Error parsing task result:', e); // Updated error message
    }
  }

  return NextResponse.json({
    status,
    error: errorMessage
  });
});
