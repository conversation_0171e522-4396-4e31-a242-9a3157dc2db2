import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

export const POST = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { name, description, globalJobTemplateId, datasetId, jobJson, status } = await request.json();

    const { data, error } = await supabase
        .from('tasks')
        .insert({ 
            user_id: userId, 
            name, 
            description, 
            global_job_template_id: globalJobTemplateId, 
            dataset_id: datasetId, 
            job_json: jobJson, 
            status 
        })
        .select();

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
});

export const GET = withAuth(async (userId, request) => {
    const supabase = createClient();
    const { searchParams } = new URL(request.url);
    const jobId = searchParams.get('jobId'); // Renamed batchId to jobId

    const query = supabase
        .from('tasks') // Renamed jobs to tasks
        .select(`
            *,
            bulk_job_type,
            dataset:datasets(name),
            task_results(
                id,
                file_name,
                file_type,
                visible,
                required,
                content_type
            )
        `)
        .eq('user_id', userId)
        .order('created_at', { ascending: false });
            
    if (jobId) { // Renamed batchId to jobId
        query.eq('job_id', jobId); // Renamed batch_id to job_id, batchId to jobId
    }
    console.log("jobId for debugging:", jobId);

    const { data, error } = await query;

    if (error) {
        return NextResponse.json({ error: error.message }, { status: 500 });
    }

    return NextResponse.json({ success: true, data });
});
