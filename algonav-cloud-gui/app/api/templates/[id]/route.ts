import { withAuth } from '@/lib/api/withAuth';
import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";
import { Template } from '@/components/template/types/template';


export const GET = withAuth(async (userId, request, { params }) => {
  const supabase = createClient();
  const { id } = params;

  const { data, error } = await supabase
    .from('global_job_templates')
    .select('*')
    .eq('id', id)
    .single();

  if (error) {
    return NextResponse.json({ error: error.message }, { status: 500 });
  }

  // Transform the data to match our Template type
  const template: Template = {
    id: data.id.toString(),
    name: data.name,
    description: data.description,
    template_data: data.template_data,
    vars: Array.isArray(data.vars?.vars) ? data.vars.vars : []
  };

  return NextResponse.json({ success: true, data: template });
});

export const PUT = withAuth(async (userId, request, { params }) => {
  const supabase = createClient();
  const { id } = params;
  const updatedTemplate = await request.json();

  // Extract only the fields that belong in global_job_templates
  const { vars, template_data, ...templateData } = updatedTemplate;

  // Update and fetch in a single operation
  const { data: updatedData, error: updateError } = await supabase
    .from('global_job_templates')
    .update({
      vars: { vars },                // Direkt das vars Feld aktualisieren
      commented_vars: null,          // commented_vars auf null setzen
      template_data: template_data,  // Direkt template_data aktualisieren
      commented_json: null           // commented_json auf null setzen
    })
    .eq('id', id)
    .select('*')
    .single();

  if (updateError) {
    return NextResponse.json({ error: updateError.message }, { status: 500 });
  }

  // Transform the response data
  const template: Template = {
    id: updatedData.id.toString(),
    name: updatedData.name,
    description: updatedData.description,
    template_data: updatedData.template_data,
    vars: Array.isArray(updatedData.vars?.vars) ? updatedData.vars.vars : []
  };

  return NextResponse.json({ success: true, data: template });
});
