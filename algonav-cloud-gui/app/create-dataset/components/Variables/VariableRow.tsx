import { Box, TextField, Button } from '@mui/material';
import { Variable } from '../../types';

interface VariableRowProps {
    variable: Variable;
    index: number;
    onVariableChange: (index: number, field: 'name' | 'value', value: string) => void;
    onVariableDelete: (index: number) => void;
}

export function VariableRow({
    variable,
    index,
    onVariableChange,
    onVariableDelete
}: VariableRowProps) {
    return (
        <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
            <TextField
                label="Variable Name"
                value={variable.name}
                onChange={(e) => onVariableChange(index, 'name', e.target.value)}
                size="small"
            />
            <TextField
                label="Value"
                value={variable.value}
                onChange={(e) => onVariableChange(index, 'value', e.target.value)}
                error={!variable.isValid}
                helperText={!variable.isValid ? "Invalid value format" : ""}
                size="small"
                fullWidth
            />
            <Button
                onClick={() => onVariableDelete(index)}
                color="error"
                variant="outlined"
            >
                Delete
            </Button>
        </Box>
    );
}
