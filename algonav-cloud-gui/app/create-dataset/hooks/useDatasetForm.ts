import { useState, useEffect } from 'react';
import { createClient } from "@/utils/supabase/client";
import { Variable, SelectedFile } from '../types';
import { validateVariableValue } from '../utils';
import { parseVariableInput, formatVariableForDisplay } from '../lib/variableHandler';

export function useDatasetForm(datasetId: string | null, user: any) {
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [files, setFiles] = useState([]);
    const [selectedFiles, setSelectedFiles] = useState<SelectedFile[]>([]);
    const [variables, setVariables] = useState<Variable[]>([]);
    const [isEditing, setIsEditing] = useState(false);
    const [hasValidationError, setHasValidationError] = useState(false);
    const [error, setError] = useState<string | null>(null);
    const supabase = createClient();

    useEffect(() => {
        async function fetchData() {
            if (datasetId) {
                setIsEditing(true);
                await fetchDatasetDetails(datasetId);
            }
            fetchFiles();
        }
        if (user) {
            fetchData();
        }
    }, [datasetId, user]);

    const fetchDatasetDetails = async (id: string) => {
        const response = await fetch(`/api/datasets/${id}`);
        if (response.ok) {
            const { data } = await response.json();
            setName(data.name);
            setDescription(data.description);

            if (data.variable_overrides?.vars) {
                const varsArray = data.variable_overrides.vars.map(
                    (variable: any) => ({
                        name: variable.name,
                        value: formatVariableForDisplay(variable.data),
                        isValid: true
                    })
                );
                setVariables(varsArray);
            }

            const filesResponse = await fetch(`/api/dataset-files?datasetId=${id}`);
            if (filesResponse.ok) {
                const filesData = await filesResponse.json();
                setSelectedFiles(filesData.data.map((file: any) => ({
                    path: file.files.file_path,
                    type: file.file_type
                })));
            }
        }
    };

    const fetchFiles = async () => {
        const response = await fetch('/api/file');
        if (response.ok) {
            const data = await response.json();
            setFiles(data.files);
        }
    };

    const validateForm = (): string | null => {
        if (!name.trim()) {
            return 'Dataset name is required';
        }

        if (selectedFiles.length === 0) {
            return 'Please select at least one file';
        }

        const hasInvalidVariables = variables.some(v => !v.isValid);
        if (hasInvalidVariables) {
            return 'Please fix invalid variable values before saving';
        }

        return null;
    };

    const handleSaveDataset = async () => {
        const validationError = validateForm();
        if (validationError) {
            setError(validationError);
            return null;
        }

        try {
            const varsForApi = variables
                .map(({ name, value }) => ({
                    name,
                    data: parseVariableInput(value),
                    links: []
                }))
                .filter(v => v.name.trim());

            const endpoint = isEditing ? `/api/datasets/${datasetId}` : '/api/datasets';
            const method = isEditing ? 'PUT' : 'POST';

            const response = await fetch(endpoint, {
                method,
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify({
                    name,
                    description,
                    filePaths: selectedFiles.map(file => file.path),
                    fileTypes: selectedFiles.map(file => file.type),
                    variableOverrides: {
                        vars: varsForApi
                    }
                }),
            });

            if (!response.ok) {
                const data = await response.json().catch(() => null);
                throw new Error(data?.message || 'Failed to save dataset');
            }

            return response;
        } catch (err) {
            setError(err instanceof Error ? err.message : 'An unexpected error occurred');
            return null;
        }
    };

    return {
        user,
        name,
        setName,
        description,
        setDescription,
        files,
        selectedFiles,
        setSelectedFiles,
        variables,
        setVariables,
        isEditing,
        hasValidationError,
        setHasValidationError,
        handleSaveDataset,
        validateVariableValue,
        error,
        setError,
        validateForm,
    };
}