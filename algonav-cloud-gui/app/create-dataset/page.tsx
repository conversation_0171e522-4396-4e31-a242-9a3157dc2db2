"use client";

import { Suspense } from 'react';
import { Box, Grid, Button, Typography, CircularProgress } from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';
import { FileSelectionTree } from '@/components/FileSelectionTree';
import { BasicInfo } from './components/DatasetForm/BasicInfo';
import { VariablesSection } from './components/Variables/VariablesSection';
import { SelectedFilesPanel } from './components/FileSelection/SelectedFilesPanel';
import { useDatasetForm } from './hooks/useDatasetForm';
import { FormContainer } from './styles';
import { useAuth } from '@/lib/auth/useAuth';
import { parseVariableInput } from './lib/variableHandler';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { Variable } from './types';

// Component that uses useSearchParams - needs to be wrapped in Suspense
function CreateOrEditDatasetContent() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const datasetId = searchParams.get('id');
    const { user, loading } = useAuth();

    const {
        name,
        setName,
        description,
        setDescription,
        files,
        selectedFiles,
        setSelectedFiles,
        variables,
        setVariables,
        isEditing,
        hasValidationError,
        setHasValidationError,
        handleSaveDataset,
        error,
        setError,
    } = useDatasetForm(datasetId, user);
    const handleVariableChange = (index: number, field: 'name' | 'value', newValue: string) => {
        const newVariables = [...variables];
        const variable = { ...newVariables[index] };

        variable[field] = newValue;
        if (field === 'value') {
            try {
                parseVariableInput(newValue);
                variable.isValid = true;
            } catch (error) {
                variable.isValid = false;
                // Remove the setError call here - we only want the inline validation
            }
        }

        newVariables[index] = variable;
        setVariables(newVariables);
        setHasValidationError(false);
    };

    const handleVariableDelete = (index: number) => {
        setVariables(variables.filter((_, i) => i !== index));
        setHasValidationError(false);
    };


    const handleAddVariable = () => {
        setVariables([...variables, { name: '', value: '', isValid: true }]);
    };

    const handleFileTypeChange = (file: { path: string }, type: string) => {
        setSelectedFiles(prev => prev.map(f =>
            f.path === file.path ? { ...f, type } : f
        ));
    };

    const onSave = async () => {
        const response = await handleSaveDataset();
        if (response?.ok) {
            router.push('/datasets');
        }
    };

    if (loading) return null;
    if (!user) return null;

    return (
        <PageContainer>
        <FormContainer>
            <Grid container spacing={3}>
                <Grid item xs={12}>
                    <Typography variant="h4" gutterBottom>
                        {isEditing ? 'Edit Dataset' : 'Create Dataset'}
                    </Typography>

                    <BasicInfo
                        name={name}
                        description={description}
                        onNameChange={setName}
                        onDescriptionChange={setDescription}
                    />

                    <VariablesSection
                        variables={variables}
                        hasValidationError={hasValidationError}
                        onVariableChange={handleVariableChange}
                        onVariableDelete={handleVariableDelete}
                        onAddVariable={handleAddVariable}
                    />

                    <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                        <FileSelectionTree
                            files={files}
                            selectedFiles={selectedFiles}
                            setSelectedFiles={setSelectedFiles}
                        />
                        <SelectedFilesPanel
                            selectedFiles={selectedFiles}
                            onFileTypeChange={handleFileTypeChange}
                        />
                    </Box>

                    <Button
                        variant="contained"
                        onClick={onSave}
                        sx={{ mt: 2 }}
                    >
                        {isEditing ? 'Update Dataset' : 'Create Dataset'}
                    </Button>
                </Grid>
            </Grid>
            <ErrorDialog
                    open={!!error}
                    onClose={() => setError(null)}
                    error={error}
                    title={`${isEditing ? 'Edit' : 'Create'} Dataset Error`}
                />
        </FormContainer>
        </PageContainer>
    );
}

// Loading fallback component
function CreateDatasetFallback() {
    return (
        <PageContainer>
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '300px'
                }}
            >
                <CircularProgress />
            </Box>
        </PageContainer>
    );
}

// Main export component with Suspense boundary
export default function CreateOrEditDataset() {
    return (
        <Suspense fallback={<CreateDatasetFallback />}>
            <CreateOrEditDatasetContent />
        </Suspense>
    );
}
