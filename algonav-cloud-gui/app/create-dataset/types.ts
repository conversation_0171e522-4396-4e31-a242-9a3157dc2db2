export interface Variable {
  name: string;
  value: string;
  isValid: boolean;
}

export interface SelectedFile {
  path: string;
  type: string;
}

export interface DatasetFormData {
  name: string;
  description: string;
  variables: Variable[];
  selectedFiles: SelectedFile[];
}

export const FILE_TYPES = ["inputfileA_clk","inputfileA_sp3","inputfile_tix_imu","inputfile_rinex_rover","inputfileA_rinex_base"] as const;
export type FileType = typeof FILE_TYPES[number];
