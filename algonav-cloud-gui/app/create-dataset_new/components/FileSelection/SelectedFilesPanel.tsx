import { Paper, Typography, Box, FormControl, InputLabel, Select, MenuItem, Tooltip } from '@mui/material';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import { SelectedFile, FILE_TYPES } from '../../types';
import { FileItem, FileNameTypography } from '../../styles';

interface SelectedFilesPanelProps {
    selectedFiles: SelectedFile[];
    onFileTypeChange: (file: SelectedFile, type: string) => void;
}

export function SelectedFilesPanel({ 
    selectedFiles, 
    onFileTypeChange 
}: SelectedFilesPanelProps) {
    return (
        <Paper sx={{ width: 350, p: 2 }}>
            <Typography variant="h6" gutterBottom>Selected Files</Typography>
            {selectedFiles.length === 0 ? (
                <Typography variant="body2">No files selected</Typography>
            ) : (
                selectedFiles.map((file) => (
                    <Box key={file.path} sx={{ mb: 2 }}>
                        <FileItem>
                            <InsertDriveFileIcon />
                            <Tooltip title={file.path} placement="top">
                                <FileNameTypography variant="body2">
                                    {file.path.split('/').pop()}
                                </FileNameTypography>
                            </Tooltip>
                        </FileItem>
                        <FormControl fullWidth size="small" sx={{ mt: 1, width: '90%' }}>
                            <InputLabel>File Type</InputLabel>
                            <Select
                                value={file.type || ''}
                                onChange={(e) => onFileTypeChange(file, e.target.value)}
                            >
                                <MenuItem value=""><em>None</em></MenuItem>
                                {FILE_TYPES.map(type => (
                                    <MenuItem key={type} value={type}>{type}</MenuItem>
                                ))}
                            </Select>
                        </FormControl>
                    </Box>
                ))
            )}
        </Paper>
    );
}
