export function parseVariableInput(value: string): any {
    const trimmedValue = value.trim();
    
    if (trimmedValue === '') return '';

    // Check if input starts with { or [ to identify JSON
    if (trimmedValue.startsWith('{') || trimmedValue.startsWith('[')) {
        try {
            return JSON.parse(trimmedValue);
        } catch {
            throw new Error('Invalid JSON format');
        }
    }

    // Handle primitive values
    if (trimmedValue.toLowerCase() === 'true') return true;
    if (trimmedValue.toLowerCase() === 'false') return false;

    const numberValue = Number(trimmedValue);
    if (!isNaN(numberValue) && trimmedValue !== '') {
        return numberValue;
    }

    // Return plain string for non-JSON values
    return trimmedValue;
}
export function formatVariableForDisplay(value: any): string {
    if (typeof value === 'string') {
        return value;
    }
    return JSON.stringify(value);
}

export function formatVariableForApi(value: any): any {
    if (typeof value === 'string') {
        return value;
    }
    return value;
}
