"use client";

import { useState, useEffect, Suspense } from 'react';
import { Box, Grid, Button, Typography, CircularProgress } from '@mui/material';
import { useRouter, useSearchParams } from 'next/navigation';
import { FileSelectionTree } from '@/components/FileSelectionTree';
import { BasicInfo } from './components/DatasetForm/BasicInfo';
import { VariablesSection } from './components/Variables/VariablesSection';
import { SelectedFilesPanel } from './components/FileSelection/SelectedFilesPanel';
import { FormContainer } from './styles';
import { useAuthStore } from '@/lib/stores/authStore';
import { parseVariableInput } from './lib/variableHandler';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { useCreateDataset, useUpdateDataset, useDataset,useDatasetFiles } from '@/lib/hooks/useDatasets';
import { useFiles } from '@/lib/hooks/useFiles';
import { Variable, SelectedFile } from './types';
import { formatVariablesForApi } from './utils';
import { formatVariableForDisplay } from './lib/variableHandler';

// Component that uses useSearchParams - needs to be wrapped in Suspense
function CreateOrEditDatasetContent() {
    const router = useRouter();
    const searchParams = useSearchParams();
    const datasetId = searchParams.get('id');
    const user = useAuthStore(state => state.user);
    const [error, setError] = useState<string | null>(null);

    // State
    const [name, setName] = useState('');
    const [description, setDescription] = useState('');
    const [selectedFiles, setSelectedFiles] = useState<SelectedFile[]>([]);
    const [variables, setVariables] = useState<Variable[]>([]);
    const [hasValidationError, setHasValidationError] = useState(false);

    // Queries
    const { data: filesData, error: filesError } = useFiles();
    const { data: datasetData, error: datasetError } = useDataset(datasetId, {
        enabled: !!datasetId
    });
    const { data: datasetFilesData, error: datasetFilesError } = useDatasetFiles(datasetId);

    useEffect(() => {
        if (filesError) {
            setError(filesError.message || 'Failed to load files');
        }
        if (datasetError) {
            setError(datasetError.message || 'Failed to load dataset');
        }
        if (datasetFilesError) {
            setError(datasetFilesError.message || 'Failed to load dataset files');
        }
    }, [filesError, datasetError, datasetFilesError]);

    // Modify the useEffect to include file data
    useEffect(() => {
        if (datasetData?.data) {
            const dataset = datasetData.data;
            setName(dataset.name);
            setDescription(dataset.description);

            if (dataset.variable_overrides?.vars) {
                const varsArray = dataset.variable_overrides.vars.map((variable: any) => ({
                    name: variable.name,
                    value: formatVariableForDisplay(variable.data),
                    isValid: true
                }));
                setVariables(varsArray);
            }
        }

        if (datasetFilesData?.data) {
            const filesArray = datasetFilesData.data.map((file: any) => ({
                path: file.files.file_path,
                type: file.file_type
            }));
            setSelectedFiles(filesArray);
        }
    }, [datasetData, datasetFilesData]);

    // Mutations
    const createDataset = useCreateDataset();
    const updateDataset = useUpdateDataset();

    const validateForm = () => {
        if (!name.trim()) {
            throw new Error('Dataset name is required');
        }

        if (selectedFiles.length === 0) {
            throw new Error('Please select at least one file');
        }

        const hasInvalidVariables = variables.some(v => !v.isValid);
        if (hasInvalidVariables) {
            throw new Error('Please fix invalid variable values before saving');
        }
    };

    const handleVariableChange = (index: number, field: 'name' | 'value', newValue: string) => {
        const newVariables = [...variables];
        const variable = { ...newVariables[index] };

        variable[field] = newValue;
        if (field === 'value') {
            try {
                parseVariableInput(newValue);
                variable.isValid = true;
            } catch (error) {
                variable.isValid = false;
            }
        }

        newVariables[index] = variable;
        setVariables(newVariables);
        setHasValidationError(false);
    };

    const handleVariableDelete = (index: number) => {
        setVariables(variables.filter((_, i) => i !== index));
        setHasValidationError(false);
    };

    const handleAddVariable = () => {
        setVariables([...variables, { name: '', value: '', isValid: true }]);
    };

    const handleFileTypeChange = (file: { path: string }, type: string) => {
        setSelectedFiles(prev => prev.map(f =>
            f.path === file.path ? { ...f, type } : f
        ));
    };

    const onSave = async () => {
        try {
            validateForm();

            const payload = {
                name,
                description,
                filePaths: selectedFiles.map(file => file.path),
                fileTypes: selectedFiles.map(file => file.type),
                variableOverrides: {
                    vars: formatVariablesForApi(variables)
                }
            };

            if (datasetId) {
                await updateDataset.mutateAsync({ id: datasetId, ...payload });
            } else {
                await createDataset.mutateAsync(payload);
            }

            router.push('/datasets');
        } catch (err) {
            setError(err instanceof Error ? err.message : 'Failed to save dataset');
        }
    };

    if (!user) return null;

    return (
        <PageContainer>
            <FormContainer>
                <Grid container spacing={3}>
                    <Grid item xs={12}>
                        <Typography variant="h4" gutterBottom>
                            {datasetId ? 'Edit Dataset' : 'Create Dataset'}
                        </Typography>

                        <BasicInfo
                            name={name}
                            description={description}
                            onNameChange={setName}
                            onDescriptionChange={setDescription}
                        />

                        <VariablesSection
                            variables={variables}
                            hasValidationError={hasValidationError}
                            onVariableChange={handleVariableChange}
                            onVariableDelete={handleVariableDelete}
                            onAddVariable={handleAddVariable}
                        />

                        <Box sx={{ display: 'flex', gap: 2, mt: 2 }}>
                            <FileSelectionTree
                                files={filesData?.files || []}
                                selectedFiles={selectedFiles}
                                setSelectedFiles={setSelectedFiles}
                            />
                            <SelectedFilesPanel
                                selectedFiles={selectedFiles}
                                onFileTypeChange={handleFileTypeChange}
                            />
                        </Box>

                        <Button
                            variant="contained"
                            onClick={onSave}
                            sx={{ mt: 2 }}
                        >
                            {datasetId ? 'Update Dataset' : 'Create Dataset'}
                        </Button>
                    </Grid>
                </Grid>
                <ErrorDialog
                    open={!!error}
                    onClose={() => setError(null)}
                    error={error}
                    title={`${datasetId ? 'Edit' : 'Create'} Dataset Error`}
                />
            </FormContainer>
        </PageContainer>
    );
}

// Loading fallback component
function CreateDatasetFallback() {
    return (
        <PageContainer>
            <Box
                sx={{
                    display: 'flex',
                    justifyContent: 'center',
                    alignItems: 'center',
                    height: '300px'
                }}
            >
                <CircularProgress />
            </Box>
        </PageContainer>
    );
}

// Main export component with Suspense boundary
export default function CreateOrEditDataset() {
    return (
        <Suspense fallback={<CreateDatasetFallback />}>
            <CreateOrEditDatasetContent />
        </Suspense>
    );
}
