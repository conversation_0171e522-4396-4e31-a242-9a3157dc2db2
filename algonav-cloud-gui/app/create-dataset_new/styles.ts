import { styled } from '@mui/material/styles';
import { Box, Typography, Paper } from '@mui/material';

export const FileItem = styled(Box)(({ theme }) => ({
    display: 'flex',
    alignItems: 'center',
    marginBottom: theme.spacing(2),
}));

export const FileNameTypography = styled(Typography)({
    marginLeft: '8px',
    flex: 1,
    whiteSpace: 'nowrap',
    overflow: 'hidden',
    textOverflow: 'ellipsis',
});

export const StyledContainer = styled(Box)({
    display: 'flex',
    gap: '20px',
    paddingTop: '20px',
    paddingBottom: '20px',
});

export const StyledPaper = styled(Paper)({
    padding: '16px',
    flex: 1,
});

export const FormContainer = styled(Box)({
    maxWidth: 1200,
    margin: '0 auto',
    padding: '24px',
});
