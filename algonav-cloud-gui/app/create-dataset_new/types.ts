export interface Variable {
  name: string;
  value: string;
  isValid: boolean;
}

export interface SelectedFile {
  path: string;
  type: string;
}

export interface DatasetFormData {
  name: string;
  description: string;
  variables: Variable[];
  selectedFiles: SelectedFile[];
}

export const FILE_TYPES = ['CLOCKFILES', 'SP3FILES', 'RNXFILE'] as const;
export type FileType = typeof FILE_TYPES[number];
