"use client"

import React, { useState, useEffect } from 'react';
import { Typography } from '@mui/material';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import FileUploader from '@/components/FileUploader';
import { FileList } from '@/components/FileList';
import { useFiles, useDeleteFile } from '@/lib/hooks/useFiles';
import { useQueryClient } from '@tanstack/react-query';

export default function FilesPage() {
    const { data: filesData, isLoading, error: fetchError } = useFiles();
    const deleteFile = useDeleteFile();
    const queryClient = useQueryClient();
    const [error, setError] = useState<string | null>(null);

    useEffect(() => {
        if (fetchError) {
            setError(fetchError.message || 'Failed to load files');
        }
    }, [fetchError]);

    const handleDelete = async (fullPath: string) => {
        try {
            await deleteFile.mutateAsync(fullPath);
        } catch (error) {
            setError(error instanceof Error ? error.message : 'Failed to delete file');
        }
    };

    const handleUploadSuccess = () => {
        queryClient.invalidateQueries({ queryKey: ['files'] });
    };

    if (isLoading) return <div>Loading...</div>;

    return (
        <PageContainer>
            <Typography variant="h4" gutterBottom>
                Your Files
            </Typography>
            <FileUploader
                storageEndpoint="/api/file"
                onUploadSuccess={handleUploadSuccess}
            />
            <FileList
                files={filesData?.files || []}
                onDelete={handleDelete}
            />
            <ErrorDialog
                open={!!error}
                onClose={() => setError(null)}
                error={error}
                title="File Operation Error"
            />
        </PageContainer>
    );
}