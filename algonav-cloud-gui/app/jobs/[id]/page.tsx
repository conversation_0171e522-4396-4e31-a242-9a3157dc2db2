"use client"

import React, { useState, useEffect } from 'react';
import { Grid } from '@mui/material';
import Job from '@/components/Job';
import { PageContainer } from '@/components/layout/PageContainer';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { useJob } from '@/lib/hooks/useJobs'; // Updated import
import { useTasks } from '@/lib/hooks/useTasks'; // Updated import

export default function JobPage({ params }: { params: { id: string } }) {
    const [error, setError] = useState<string | null>(null);
    const { data: jobData, error: jobError, isLoading: jobLoading } = useJob(params.id); // Updated hook and variables
    const { data: tasksData, error: tasksError, isLoading: tasksLoading } = useTasks(params.id); // Updated hook and variables

    useEffect(() => {
        if (jobError) { // Updated variable
            setError(jobError.message || 'Failed to load job details');
        }
        if (tasksError) { // Updated variable
            setError(tasksError.message || 'Failed to load tasks');
        }
    }, [jobError, tasksError]); // Updated dependencies

    if (jobLoading || tasksLoading) return <div>Loading...</div>; // Updated variables

    return (
        <PageContainer>
            <Grid container spacing={3}>
                <Grid item xs={12}>
                    <Job 
                        job={jobData?.data}
                        tasks={tasksData?.data || []}
                    />
                </Grid>
            </Grid>
            <ErrorDialog
                open={!!error}
                onClose={() => setError(null)}
                error={error}
                title="Job Error"
            />
        </PageContainer>
    );
}
