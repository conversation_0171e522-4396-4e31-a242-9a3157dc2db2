"use client"

import React, { useState, useEffect } from 'react';
import { PageContainer } from '@/components/layout/PageContainer';
import { CreateJob } from '@/components/createJob/CreateJob';
import { ErrorDialog } from '@/components/dialogs/ErrorDialog';
import { useDatasets } from '@/lib/hooks/useDatasets';
import { useTemplates } from '@/lib/hooks/useTemplates';

export default function CreateJobPage() {
    const { data: datasetsData, error: datasetsError } = useDatasets();
    const { data: templatesData, error: templatesError } = useTemplates();
    const [error, setError] = useState<string | null>(null);

    console.log('Templates data:', templatesData); // Debug log

    useEffect(() => {
        if (datasetsError) {
            setError(datasetsError.message || 'Failed to load datasets');
        }
        if (templatesError) {
            setError(templatesError.message || 'Failed to load templates');
        }
    }, [datasetsError, templatesError]);

    return (
        <PageContainer>
            <CreateJob 
                templates={templatesData?.data || []} 
                datasets={datasetsData?.data || []} 
            />
            <ErrorDialog
                open={!!error}
                onClose={() => setError(null)}
                error={error}
                title="Data Loading Error"
            />
        </PageContainer>
    );
}
