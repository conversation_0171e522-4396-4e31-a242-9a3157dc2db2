import { Inter } from 'next/font/google';
import ThemeRegistry from '../theme/ThemeRegistry'
import Header from '@/components/Header';
import Sidebar from '@/components/Sidebar';
import { Box, Container } from '@mui/material';
import { AuthProvider } from "@/components/providers/AuthProvider"
import { QueryProvider } from './providers/QueryProvider'
const inter = Inter({ subsets: ['latin'] });

export const metadata = {
    title: 'AlgoNav Cloud',
    description: 'KNSS Post-Processing App',
}

export default function RootLayout({
    children,
}: {
    children: React.ReactNode
}) {
    return (
      <html lang="en">
        <body className={inter.className}>
          <ThemeRegistry fontFamily={inter.style.fontFamily}>
            <AuthProvider>
              <QueryProvider>
                <Box sx={{ display: 'flex' }}>
                  <Header />
                  <Sidebar />
                    <Container
                    maxWidth="xl"
                    sx={{
                      flexGrow: 1,
                      px: 0,
                      py: 0,
                      m: 0,
                      overflow: 'auto'
                    }}
                    >
                    {children}
                    </Container>
                </Box>
              </QueryProvider>
            </AuthProvider>
          </ThemeRegistry>
        </body>
      </html>
    );
}
