"use client"

import { useState, useCallback, useEffect } from 'react';
import { Box, Typography, Paper, Button } from '@mui/material';
import { PageContainer } from '@/components/layout/PageContainer';
import LogConfigInput from '@/components/inputs/LogConfigInput';
import { LogConfigValue } from '@/components/inputs/types/LogConfigTypes';

export default function LogConfigTest() {
  const [logConfig, setLogConfig] = useState<LogConfigValue>({
    label: 'AlgoNav\'s Tighly-Coupling solution',
    trigger: 'RTS_TIME', // Default trigger
    dt: 0.1, // Default for RTS_TIME
    // sensor and step will be undefined initially or based on trigger
    fields: []
  });

  const [jsonOutput, setJsonOutput] = useState('');

  // Log initial state
  useEffect(() => {
    console.log("Initial logConfig state:", logConfig);
  }, []); // Empty dependency array ensures this runs only on mount

  // Use useCallback to prevent unnecessary re-renders
  const handleChange = useCallback((newValue: any) => {
    console.log("handleChange received newValue:", newValue);
    setLogConfig(prevConfig => {
      // Only update if the value has actually changed
      if (JSON.stringify(prevConfig) !== JSON.stringify(newValue)) {
        // Log state before it's updated by the component's onChange
        console.log("Current logConfig state (before update by LogConfigInput):", prevConfig);
        console.log("Updating logConfig to newValue from LogConfigInput:", newValue);
        return newValue;
      }
      return prevConfig;
    });
    // Note: Logging logConfig immediately after setLogConfig might show the old state
    // due to asynchronous nature. For more accurate logging of state *after* update,
    // use a useEffect hook or log newValue as done above.
  }, []);

  const handleShowJson = () => {
    console.log("handleShowJson: Current logConfig state:", logConfig);
    setJsonOutput(JSON.stringify(logConfig, null, 2));
  };

  // --- Test Functions for Programmatic Changes ---
  const switchToRtsPredictor = () => {
    const newState: LogConfigValue = {
      ...logConfig, // keep other fields like label, fields
      trigger: 'RTS_PREDICTOR',
      step: 5,
      // Remove dt and sensor for RTS_PREDICTOR
    };
    delete newState.dt;
    delete newState.sensor;
    setLogConfig(newState);
    console.log("Programmatically switched to RTS_PREDICTOR. New state:", newState);
  };

  const switchToRtsTime = () => {
    const newState: LogConfigValue = {
      ...logConfig,
      trigger: 'RTS_TIME',
      dt: 0.05, // 20 Hz
    };
    delete newState.step;
    delete newState.sensor;
    setLogConfig(newState);
    console.log("Programmatically switched to RTS_TIME. New state:", newState);
  };

  const switchToRtsMeas = () => {
    const newState: LogConfigValue = {
      ...logConfig,
      trigger: 'RTS_MEAS',
      sensor: 'dgnssX', // Example sensor
      step: 10,
    };
    delete newState.dt;
    setLogConfig(newState);
    console.log("Programmatically switched to RTS_MEAS. New state:", newState);
  };

  // Mock available log groups and sensors for testing
  const availableLogGroups = [
    {"name": "time", "label": "Time", "logprefix": ""},
    {"name": "position", "label": "Position", "logprefix": ""},
    {"name": "velocity", "label": "Velocity", "logprefix": ""},
    {"name": "attitude", "label": "Attitude", "logprefix": ""},
    {"name": "imu_n", "label": "IMU", "logprefix": "IMUN"}
  ];

  const availableSensors = [
    {"name": "sensor_DGNSSX", "label": "Differential GNSS", "logprefix": "dgnssX"},
    {"name": "sensor_V", "label": "Vehicle Motion Constraints", "logprefix": "cons"}
  ];

  return (
    <PageContainer>
      <Typography variant="h4" component="h1" gutterBottom>
        Log Configuration Test
      </Typography>

      {/* Buttons for programmatic state changes */}
      <Box sx={{ mb: 2, display: 'flex', gap: 2 }}>
        <Button variant="outlined" onClick={switchToRtsTime}>
          Set RTS_TIME (dt=0.05)
        </Button>
        <Button variant="outlined" onClick={switchToRtsMeas}>
          Set RTS_MEAS (sensor=dgnssX, step=10)
        </Button>
        <Button variant="outlined" onClick={switchToRtsPredictor}>
          Set RTS_PREDICTOR (step=5)
        </Button>
      </Box>

      <Paper sx={{ p: 3, mb: 4 }}>
        <LogConfigInput
          value={logConfig} // Ensure logConfig is passed here
          onChange={handleChange}
          name="log_config"
          gui={{
            label: "Log Configuration (Interact Below)",
            tooltip: "Configure log output settings",
            availableLogGroups,
            availableSensors
          }}
          template_data={{ // Pass template_data as well if your component uses it
            available_log_groups: availableLogGroups,
            available_sensors: availableSensors
          }}
        />
      </Paper>

      <Box sx={{ mb: 2 }}>
        <Button
          variant="contained"
          onClick={handleShowJson}
        >
          Show Current State as JSON
        </Button>
      </Box>

      {jsonOutput && (
        <Paper sx={{ p: 3, mt: 2 }}>
          <Typography variant="h6" gutterBottom>
            JSON Output (from "Show Current State" button)
          </Typography>
          <pre style={{
            backgroundColor: '#f5f5f5',
            padding: '16px',
            borderRadius: '4px',
            overflow: 'auto',
            maxHeight: '400px'
          }}>
            {jsonOutput}
          </pre>
        </Paper>
      )}
    </PageContainer>
  );
}
