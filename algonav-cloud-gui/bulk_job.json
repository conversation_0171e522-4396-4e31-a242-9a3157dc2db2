{

    "filetypes_required" : ["tix4kml"],


    "vars": [
    { "name": "OUTPUTFOLDER"     ,   "data": "./bulk_outputs"                             },
    { "name": "OUTPUTFILENAME"   ,   "data": "multi.kml"                                  },

    { "name": "KML_NAME"         ,   "data": "tightly-coupled DGNSS+IMU Solution"         },

    { "name": "JOB_METAINFO"     ,   "data": {
            "processed_timestamp": 232354325943875.0                        
        }
    },

    { "name": "TASK_RESULT"      ,   "links": ["TR"], "data": [
        {
            "dataset": {
                "name": "268a"
            },
            "output_files_by_type": {
                "tix4kml": "./demo_outputs/268a.TIX"
            }
        },
        {
            "dataset": {
                "name": "268b"
            },
            "output_files_by_type": {
                "tix4kml": "./demo_outputs/268b.TIX"
            }
        }
    ]
    }
    ],
    
    "process": [
    {
        "name"    : "Read-RTS4KML-from-TIX",
        "app"     : "MOD_SDC_FROM_TIX",
        "args"    : {
        "filename": "$TASK_RESULT.output_files_by_type.tix4kml$",
        "output"  : "~>TC_RTS4KML"
        }
    },


    {
        "name"    : "RTS_into_MultiKML",
        "app"     : "MOD_SDC2KML",
        "args"    : {
        "enable"  : "$DEV_OUTPUTS$",
        "colin"        : "<~~TC_RTS4KML@TR",
        "output_files" : [
            {
            "path"    : "$OUTPUTFOLDER$",
            "filename": "$OUTPUTFILENAME$",
            "type"    : "multi_kml",
            "visible" : false,
            "required": true
            }
        ],          
        "kml_name"                          : "$KML_NAME$",  //"tightly-coupled DGNSS+IMU Solution",
        "document_include"                  : "<open>1</open><gx:balloonVisibility>0</gx:balloonVisibility>",
        "placemark_include"                 : "",
        "placemark_name"                    : "",
        "placemark_name_multi"              : "$[]TASK_RESULT.dataset.name$",    // will internally create a json-array with all entries TASK_RESULT.dataset.name (over all collected inputs, in this case "@TR": task results)
        "multicolors"                       : ["FF0062FC", "FFFF0000", "FF00FF00", "FF0000FF", "FFFFFF00", "FFFF00FF", "FF00FFFF", "FFFFFFFF"],
        "placemark_description"             : "",
        "static"                            : false,
        "add_links_to_placemark_name"       : true,
        "add_links_to_placemark_description": false,
        "linestyle_width"         : 1,
        "folders"                 : [],
        "linestyle_color"         : "FF00FF00",
        "color_mode"              : "normal",   // "random",   // "random" or "normal"(default)
        "linestring_extrude"      : 0,
        "linestring_tessellate"   : 0,
        "linestring_altitudeMode" : "clampToGround"   //"absolute"
        }
    } 
    ],

    


    // main DAC-Process settings (if any)
    "settings": {  }
      
}
    