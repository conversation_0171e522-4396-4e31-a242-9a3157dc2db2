"use client"
import React, { useState, useEffect } from 'react';
import { 
  Paper, 
  Checkbox, 
  Tooltip, 
  TableBody,
  Typography,
  Button
} from '@mui/material';
import {
  StyledTableContainer,
  StyledTable,
  StyledTableHead,
  StyledHeaderCell,
  StyledTableCell,
  StyledTableRow,
} from './common/TablePresets';

const Datasets = ({ datasets, onCheckedChange }) => {
  const [checkedDatasets, setCheckedDatasets] = useState({});

  const handleCheckboxChange = (dataset) => {
    const newCheckedDatasets = { 
      ...checkedDatasets, 
      [dataset.id]: { 
        checked: !checkedDatasets[dataset.id]?.checked, 
        dataset: dataset 
      } 
    };
    setCheckedDatasets(newCheckedDatasets);
    onCheckedChange(newCheckedDatasets);
  };

  useEffect(() => {
    const initialCheckedState = datasets.reduce((acc, dataset) => {
      acc[dataset.id] = { checked: false, dataset: dataset };
      return acc;
    }, {});
    setCheckedDatasets(initialCheckedState);
  }, [datasets]);

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <StyledTableContainer>
        <StyledTable>
          <StyledTableHead>
            <StyledTableRow>
              <StyledHeaderCell padding="checkbox">Select</StyledHeaderCell>
              <StyledHeaderCell>Dataset Name</StyledHeaderCell>
              <StyledHeaderCell>Description</StyledHeaderCell>
            </StyledTableRow>
          </StyledTableHead>
          <TableBody>
            {datasets.map((dataset) => (
              <StyledTableRow 
                key={dataset.id}
                onClick={() => handleCheckboxChange(dataset)}
                sx={{ cursor: 'pointer' }}
              >
                <StyledTableCell padding="checkbox">
                  <Checkbox
                    checked={!!checkedDatasets[dataset.id]?.checked}
                    onChange={() => handleCheckboxChange(dataset)}
                  />
                </StyledTableCell>
                <StyledTableCell>
                  <Tooltip title={dataset.name} placement="top-start">
                    <span>{dataset.name}</span>
                  </Tooltip>
                </StyledTableCell>
                <StyledTableCell>
                  <Tooltip title={dataset.description} placement="top-start">
                    <span>{dataset.description}</span>
                  </Tooltip>
                </StyledTableCell>
              </StyledTableRow>
            ))}
          </TableBody>
        </StyledTable>
      </StyledTableContainer>
    </Paper>
  );
};

export default Datasets;