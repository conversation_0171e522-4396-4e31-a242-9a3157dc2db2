import React, { useEffect } from 'react';
import { SimpleTreeView, TreeItem } from '@mui/x-tree-view';
import { Checkbox, Typography, Box, Paper } from '@mui/material';
import InsertDriveFileIcon from '@mui/icons-material/InsertDriveFile';
import FolderIcon from '@mui/icons-material/Folder';
import { styled } from '@mui/system';

const StyledContainer = styled(Box)({
  display: 'flex',
  gap: '20px',
  padding: '20px',
  width: '800px',
});

const StyledPaper = styled(Paper)({
  padding: '16px',
  flex: 1,
});

const StyledTreeView = styled(SimpleTreeView)({
  height: '400px',
  width: '100%',
  overflowY: 'auto',
  border: '1px solid #e0e0e0',
  borderRadius: '4px',
  padding: '8px',
});

const StyledTreeItem = styled(TreeItem)(({ theme }) => ({
  '& .MuiTreeItem-content': {
    padding: '4px 0',
    '&:hover': {
      backgroundColor: theme.palette.action.hover,
    },
  },
  '& .MuiTreeItem-label': {
    padding: '0 4px',
  },
}));

const FileTreeItemContent = styled(Box)({
  display: 'flex',
  alignItems: 'center',
  '& .MuiCheckbox-root': {
    padding: '0 4px',
  },
  '& .MuiSvgIcon-root': {
    marginRight: '4px',
  },
});

const buildFileTree = (files, selectedFiles) => {
    const tree = {};
    files.forEach(file => {
      const parts = file.fullPath.split('/').slice(1);
      let currentLevel = tree;
      parts.forEach((part, index) => {
        if (!currentLevel[part]) {
          currentLevel[part] = index === parts.length - 1 
            ? { ...file, selected: selectedFiles.some(sf => sf.path === file.fullPath) } 
            : {};
        }
        currentLevel = currentLevel[part];
      });
    });
    return tree;
};

const renderTree = (nodes, path = '', selectedFiles, handleFileSelect) => (
  Object.entries(nodes).map(([key, value]) => {
    const isFile = value.hasOwnProperty('metadata');
    const currentPath = `${path}${key}`;
    
    return (
      <StyledTreeItem
        itemId={currentPath}
        key={currentPath}
        label={
          <FileTreeItemContent>
            {isFile && (
              <Checkbox
                checked={selectedFiles.some(file => file.path === value.fullPath)}
                onChange={() => handleFileSelect(value)}
                onClick={(e) => e.stopPropagation()}
                size="small"
              />
            )}
            {isFile ? <InsertDriveFileIcon fontSize="small" /> : <FolderIcon fontSize="small" />}
            <Typography variant="body2">{key}</Typography>
          </FileTreeItemContent>
        }
      >
        {!isFile && renderTree(value, `${currentPath}/`, selectedFiles, handleFileSelect)}
      </StyledTreeItem>
    );
  })
);

export function FileSelectionTree({ files, selectedFiles, setSelectedFiles }) {
  const fileTree = buildFileTree(files, selectedFiles);

  const handleFileSelect = (file) => {
    setSelectedFiles(prev => 
      prev.some(f => f.path === file.fullPath)
        ? prev.filter(f => f.path !== file.fullPath)
        : [...prev, { id: file.id, path: file.fullPath, type: file.type || null }]
    );
  };

  useEffect(() => {
    // This effect ensures the tree is re-rendered when selectedFiles changes
  }, [selectedFiles]);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>File Selection</Typography>
      <StyledTreeView>
        {renderTree(fileTree, '', selectedFiles, handleFileSelect)}
      </StyledTreeView>
    </Box>
  );
}