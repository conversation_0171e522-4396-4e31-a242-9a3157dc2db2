import React, { useState, useEffect } from 'react';
import { Dashboard } from '@uppy/react';
import '@uppy/core/dist/style.min.css';
import '@uppy/dashboard/dist/style.min.css';
import { Button, Box } from '@mui/material';
import { styled } from '@mui/material/styles';
import UploadIcon from '@mui/icons-material/Upload';
import { useUppy } from '../lib/hooks/useUppy';
import { createClient } from '../utils/supabase/client';

const StyledDashboard = styled(Dashboard)(({ theme }) => ({
  '& .uppy-Root': {
    fontFamily: `${theme.typography.fontFamily} !important`,
  },
  '& .uppy-Dashboard-inner': {
    backgroundColor: theme.palette.grey[100],
    height: '200px',
  },
  '& .uppy-Dashboard-dropFilesHint': {
    color: theme.palette.text.primary,
  },
  '& .uppy-Dashboard-browse': {
    color: theme.palette.action.active,
  },
  '& .uppy-Dashboard-Item-name': {
    color: theme.palette.text.primary,
  },
  '& .uppy-Dashboard-Item-statusSize': {
    color: theme.palette.text.secondary,
  }
}));

export default function FileUploader({ storageEndpoint, onUploadSuccess }) {
  const [hasFiles, setHasFiles] = useState(false);
  const [session, setSession] = useState(null);
  const supabase = createClient();

  useEffect(() => {
    supabase.auth.getSession().then(({ data: { session } }) => {
      setSession(session);
    });

    const {
      data: { subscription },
    } = supabase.auth.onAuthStateChange((_event, session) => {
      setSession(session);
    });

    return () => subscription.unsubscribe();
  }, []);

  const { uppy } = useUppy(storageEndpoint, onUploadSuccess, session?.access_token);
  useEffect(() => {
    if (uppy) {
      const updateHasFiles = () => {
        setHasFiles(uppy.getFiles().length > 0);
      };

      uppy.on('file-added', updateHasFiles);
      uppy.on('file-removed', updateHasFiles);

      return () => {
        uppy.off('file-added', updateHasFiles);
        uppy.off('file-removed', updateHasFiles);
      };
    }
  }, [uppy]);

  const handleUpload = () => {
    if (uppy.getFiles().length) {
      uppy.upload().then((result) => {
        console.log('Upload complete:', result.successful.length, 'files');
        setHasFiles(false);
      });
    }
  };

  return (
    <Box>
      <Box mb={2}>
        {uppy && (
          <StyledDashboard
            uppy={uppy}
            height="250px"
            width="100%"
            disableStatusBar={false}
            showProgressDetails={true}
            hideUploadButton={true}
            fileManagerSelectionType="both"
            disableThumbnailGenerator={true}
            thumbnailWidth={10}
          />
        )}
      </Box>
      <Button 
        variant="contained" 
        color="primary"
        startIcon={<UploadIcon />}
        onClick={handleUpload}
        disabled={!hasFiles}
      >
        Upload Files
      </Button>
    </Box>
  );
}