// components/Footer.tsx
import React from 'react';
import {Box, Container, Grid, Typography, Link } from '@mui/material';
import Image from 'next/image';

const Footer = () => {
  return (
    <Box component="footer" sx={{ bgcolor: 'primary.main', color: 'white', py: 6 }}>
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          <Grid item xs={12} md={4}>
            <Box sx={{ mb: 2 }}>
           
    
              <Image
                src="/footer_logo.png"
                alt="AlgoNav Logo"
                width={0}
                height={0}
                sizes="100vw"
                style={{ width: '30%', height: 'auto' }}
              />
           
            </Box>
            
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="h6" gutterBottom>
              Links
            </Typography>
            <Link href="https://algonav.de/" color="inherit" display="block">Home</Link>
            <Link href="https://algonav.de/legal-notice/" color="inherit" display="block">Legal Notice</Link>
            <Link href="https://algonav.de/privacy-policy-2/" color="inherit" display="block">Privacy Policy</Link>
            <Link href="https://algonav.de/impressum/" color="inherit" display="block">Impressum</Link>
            <Link href="https://algonav.de/datenschutzerklarung/" color="inherit" display="block">Datenschutzerklärung</Link>
          </Grid>
          <Grid item xs={12} md={4}>
            <Typography variant="h6" gutterBottom>
              Contact
            </Typography>
            <Typography>
              <Link href="mailto:<EMAIL>" color="inherit"><EMAIL></Link>
            </Typography>
            <Typography>
              Address:<br />
              AlgoNav UG (haftungsbeschränkt)<br />
              Parkstr. 82<br />
              64289 Darmstadt, Germany
            </Typography>
          </Grid>
        </Grid>
        <Box mt={5}>
          <Typography variant="body2" align="center">
            © {new Date().getFullYear()} AlgoNav
          </Typography>
        </Box>
      </Container>
    </Box>
  );
};

export default Footer;
