// components/Header.tsx
import React from 'react';
import { AppBar, Too<PERSON>bar, Typography, Button, Box } from '@mui/material';
import Image from 'next/image';

const Header = () => {
  return (
    <AppBar position="static" color="primary">
      <Toolbar>
        <Box sx={{ flexGrow: 1, display: 'flex', alignItems: 'center' }}>
          <Image 
           src="/header_logo.png"
           alt="AlgoNav Logo" 
           width={128} 
           height={40} 
           />
        </Box>
        <Button href="https://algonav.de" color="inherit">Back to Website</Button>
      </Toolbar>
    </AppBar>
  );
};

export default Header;
