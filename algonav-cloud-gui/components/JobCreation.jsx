import React, { useState } from "react";
import { Grid, Button } from "@mui/material";
import JobTemplates from "./JobTemplates";
import Datasets from "./Datasets";
import ProcessData from "./ProcessData";

const JobCreation = ({ templates, datasets }) => {
  const [checkedTemplates, setCheckedTemplates] = useState({});
  const [checkedDatasets, setCheckedDatasets] = useState({});

  const handleTemplatesChange = (newCheckedTemplates) => {
    setCheckedTemplates(newCheckedTemplates);
  };

  const handleDatasetsChange = (newCheckedDatasets) => {
    setCheckedDatasets(newCheckedDatasets);
  };

  return (
    <Grid container spacing={3}>
      <Grid item xs={12} md={6}>
        <JobTemplates
          templates={templates}
          onCheckedChange={handleTemplatesChange}
        />
      </Grid>
      <Grid item xs={12} md={6}>
        <Datasets datasets={datasets} onCheckedChange={handleDatasetsChange} />
      </Grid>
      <Grid item xs={12}>
        <ProcessData
          checkedTemplates={checkedTemplates}
          checkedDatasets={checkedDatasets}
        />
      </Grid>
    </Grid>
  );
};

export default JobCreation;
