"use client"
import React, { useState, useEffect } from 'react';
import { 
  Paper, 
  Radio, 
  Tooltip, 
  TableBody,
  Typography
} from '@mui/material';
import {
  StyledTableContainer,
  StyledTable,
  StyledTableHead,
  StyledHeaderCell,
  StyledTableCell,
  StyledTableRow,
} from './common/TablePresets';

const JobTemplates = ({ templates, onCheckedChange }) => {
  const [selectedTemplate, setSelectedTemplate] = useState(null);

  const handleRadioChange = (template) => () => {
    setSelectedTemplate(template);
    onCheckedChange({ [template.id]: { checked: true, template: template } });
  };

  useEffect(() => {
    setSelectedTemplate(null);
    onCheckedChange({});
  }, [templates]);

  return (
    <Paper sx={{ p: 2, mb: 2 }}>
      <Typography variant="h6" gutterBottom>
        Job Templates
      </Typography>
      <StyledTableContainer>
        <StyledTable>
          <StyledTableHead>
            <StyledTableRow>
              <StyledHeaderCell>Template Name</StyledHeaderCell>
              <StyledHeaderCell>Description</StyledHeaderCell>
            </StyledTableRow>
          </StyledTableHead>
          <TableBody>
            {templates.map((template) => (
              <StyledTableRow 
                key={template.id}
                onClick={() => handleRadioChange(template)()}
                sx={{ cursor: 'pointer' }}
              >
                <StyledTableCell sx={{ maxWidth: 200, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                  <Tooltip title={template.name} placement="top-start">
                    <div style={{ display: 'flex', alignItems: 'center' }}>
                      <Radio
                        checked={selectedTemplate?.id === template.id}
                        onChange={handleRadioChange(template)}
                        value={template.id}
                      />
                      <Tooltip title={template.name}>
                        <span>{template.name}</span>
                      </Tooltip>
                    </div>
                  </Tooltip>
                </StyledTableCell>
                <StyledTableCell sx={{ maxWidth: 300, whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}>
                  <Tooltip title={template.description} placement="top-start">
                    <span>{template.description}</span>
                  </Tooltip>
                </StyledTableCell>
              </StyledTableRow>
            ))}
          </TableBody>
        </StyledTable>
      </StyledTableContainer>
    </Paper>
  );
};

export default JobTemplates;