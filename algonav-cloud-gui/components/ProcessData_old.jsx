import React from 'react';
import { Button } from '@mui/material';
import { processData } from '@/services/dataProcessingService.ts';

const ProcessData = ({ checkedTemplates, checkedDatasets }) => {
  const handleProcessData = async () => {
    try {
      // Extrahieren Sie das template-Objekt aus dem ersten (und einzigen) Eintrag in checkedTemplates
      const selectedTemplate = Object.values(checkedTemplates)[0]?.template ? [Object.values(checkedTemplates)[0].template] : [];
      
      // Extrahieren Sie die dataset-Objekte aus den ausgewählten Datasets
      const selectedDatasets = Object.values(checkedDatasets)
        .filter(d => d.checked)
        .map(d => d.dataset);
      
      const { jobs, batch } = await processData(selectedTemplate, selectedDatasets);
      // Handle successful processing, e.g., show a success message or update UI
      console.log('Data processed successfully', { jobs, batch });
    } catch (error) {
      // Handle errors, e.g., show an error message to the user
      console.error('Error processing data:', error);
    }
  };

  return (
    <Button 
      variant="contained" 
      color="primary" 
      onClick={handleProcessData}
      disabled={Object.values(checkedTemplates).length === 0 || Object.values(checkedDatasets).filter(d => d.checked).length === 0}
    >
      Process Data
    </Button>
  );
};

export default ProcessData;