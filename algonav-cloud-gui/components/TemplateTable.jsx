import React, { useState } from 'react';
import { 
  Paper, 
  TablePagination, 
  IconButton, 
  Collapse, 
  Box, 
  Typography, 
  Button,
  TableBody,
  TableContainer,
  Table,
  TableHead,
  TableCell,
  TableRow
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import KeyboardArrowDownIcon from '@mui/icons-material/KeyboardArrowDown';
import KeyboardArrowUpIcon from '@mui/icons-material/KeyboardArrowUp';
import {
  StyledTableContainer,
  StyledTable,
  StyledTableHead,
  StyledHeaderCell,
  StyledTableCell,
  StyledTableRow,
  tableButtonStyles,
} from './common/TablePresets';

function Row({ template, onDeleteTemplate }) {
  const [open, setOpen] = useState(false);

  return (
    <React.Fragment>
      <StyledTableRow>
        <StyledTableCell>
          <IconButton
            aria-label="expand row"
            size="small"
            onClick={() => setOpen(!open)}
          >
            {open ? <KeyboardArrowUpIcon /> : <KeyboardArrowDownIcon />}
          </IconButton>
        </StyledTableCell>
        <StyledTableCell component="th" scope="row">{template.name}</StyledTableCell>
        <StyledTableCell>{template.description}</StyledTableCell>
        <StyledTableCell>
          <Button
            startIcon={<DeleteIcon />}
            onClick={() => onDeleteTemplate(template)}
            sx={tableButtonStyles}
          >
            Delete
          </Button>
        </StyledTableCell>
      </StyledTableRow>
      <TableRow>
        <TableCell style={{ padding: 0, border: 'none', background: 'none' }} colSpan={4}>
          <Collapse in={open} timeout="auto" unmountOnExit>
            <Box sx={{ margin: 1 }}>
              <Typography variant="h6" gutterBottom component="div">
                Template Details
              </Typography>
              <pre style={{ whiteSpace: 'pre-wrap', wordBreak: 'break-word' }}>
                {JSON.stringify(template, null, 2)}
              </pre>
            </Box>
          </Collapse>
        </TableCell>
      </TableRow>
    </React.Fragment>
  );
}

export default function TemplateTable({ templates, onDeleteTemplate }) {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(5);

  const handleChangePage = (event, newPage) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <StyledTableContainer>
        <StyledTable aria-label="collapsible table">
          <StyledTableHead>
            <StyledTableRow>
              <StyledHeaderCell />
              <StyledHeaderCell>Name</StyledHeaderCell>
              <StyledHeaderCell>Description</StyledHeaderCell>
              <StyledHeaderCell>Actions</StyledHeaderCell>
            </StyledTableRow>
          </StyledTableHead>
          <TableBody>
            {templates
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((template) => (
                <Row key={template.id} template={template} onDeleteTemplate={onDeleteTemplate} />
              ))}
          </TableBody>
        </StyledTable>
      </StyledTableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={templates.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
}
