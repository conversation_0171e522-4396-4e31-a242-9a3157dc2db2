import Image from 'next/image';

export const DownloadIcon = ({ fileType, isMulti }) => {
  // Handle special case for csv/csv_pva
  const normalizedType = fileType.toLowerCase() === 'csv' ? 'csv_pva' : fileType.toLowerCase();
  const iconPath = `/icons/${normalizedType}-${isMulti ? 'multi' : 'single'}.svg`;
  return (
    <Image
      src={iconPath}
      alt={`${fileType} ${isMulti ? 'multi' : 'single'} download`}
      width={24}
      height={24}
      style={{ width: '24px', height: 'auto' }}
      priority
    />
  );
};

export default DownloadIcon;