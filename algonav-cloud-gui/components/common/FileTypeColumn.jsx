import React from 'react';
import { IconButton, Tooltip } from '@mui/material';

/**
 * A reusable column component for file type actions
 */
const FileTypeColumn = ({ icon: Icon, label, disabled, onClick }) => (
  <Tooltip title={label}>
    <span>
      <IconButton
        size="small"
        disabled={disabled}
        onClick={onClick}
        sx={{
          '&.MuiIconButton-root': {
            color: disabled ? 'action.disabled' : 'primary.main',
            '&:hover': {
              backgroundColor: 'rgba(25, 118, 210, 0.04)'
            }
          }
        }}
      >
        <Icon fontSize="small" />
      </IconButton>
    </span>
  </Tooltip>
);

export default FileTypeColumn;