// filepath: /Users/<USER>/tokeep/mvp/algonav-cloud-gui/components/common/SupportIcon.jsx
import React from 'react';
import Image from 'next/image';

const SupportIcon = ({ width = 24, height = 24, className = '' }) => {
  return (
    <Image 
      src="/icons/support.svg"
      width={width}
      height={height}
      alt="Support"
      style={{ width: `${width}px`, height: 'auto' }}
      className={className}
    />
  );
};

export default SupportIcon;