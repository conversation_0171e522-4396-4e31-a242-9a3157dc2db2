import { styled } from '@mui/material/styles';
import {
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
} from '@mui/material';

// Common styles that match Job.jsx
export const tableStyles = {
  headerCell: {
    borderBottom: '2px solid rgba(25, 118, 210, 0.1)',
    fontWeight: 600,
    whiteSpace: 'nowrap',
    padding: '8px 16px', // Slightly increased vertical padding
    color: 'inherit',
    height: '48px', // Increased from 40px
  },
  cell: {
    borderBottom: '1px solid rgba(224, 224, 224, 1)',
    padding: '6px 16px', // Slightly increased vertical padding
    height: '48px', // Increased from 40px
  },
  container: {
    borderRadius: '8px',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)',
    margin: '16px 0',
  }
};

// Styled components
export const StyledTableContainer = styled(TableContainer)(({ theme }) => ({
  ...tableStyles.container,
  backgroundColor: theme.palette.background.paper,
}));

export const StyledTable = styled(Table)(({ theme }) => ({
  minWidth: 650,
  '& td, & th': { 
    fontSize: '0.875rem',
  },
}));

export const StyledTableHead = styled(TableHead)(({ theme }) => ({
  backgroundColor: theme.palette.background.paper,
}));

export const StyledHeaderCell = styled(TableCell)(({ theme }) => ({
  ...tableStyles.headerCell,
  lineHeight: '1.5', // Increased from 1.2
}));

export const StyledTableCell = styled(TableCell)(({ theme }) => ({
  ...tableStyles.cell,
  lineHeight: '1.5', // Increased from 1.2
  '& .MuiButtonBase-root': { // Style for buttons/icons inside cells
    padding: '4px',
    minWidth: 'unset',
    '& .MuiSvgIcon-root': {
      fontSize: '18px', // Smaller icons
    }
  },
  '& .MuiStack-root': { // Style for Stack components inside cells
    minHeight: '36px', // Increased from 32px
  }
}));

export const StyledTableRow = styled(TableRow)(({ theme }) => ({
  '&:hover': {
    backgroundColor: theme.palette.action.hover,
  },
  backgroundColor: theme.palette.background.paper,
  height: '48px', // Increased from 40px
}));

// Button styles for table actions
export const tableButtonStyles = {
  minWidth: '32px',
  height: '32px',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  padding: '4px',
  margin: '0 2px',
  '& .MuiSvgIcon-root': {
    fontSize: '18px',
  }
};

// Icon styles for table buttons
export const tableIconStyles = {
  fontSize: '18px',
};
