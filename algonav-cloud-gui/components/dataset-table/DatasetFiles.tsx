'use client';

import React from 'react';
import {
  Box,
  CircularProgress,
  TableBody,
  Typography,
} from '@mui/material';
import {
  StyledTable,
  StyledTableHead,
  StyledHeaderCell,
  StyledTableRow,
  StyledTableCell,
} from '../common/TablePresets';
import { DatasetFile } from './types';

interface DatasetFilesProps {
  files: DatasetFile[];
  isLoading: boolean;
}

export const DatasetFiles = ({ files, isLoading }: DatasetFilesProps) => {
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
        <CircularProgress size={24} />
      </Box>
    );
  }

  if (files.length === 0) {
    return (
      <Box sx={{ p: 2, textAlign: 'center' }}>
        No files available
      </Box>
    );
  }

  return (
    <Box sx={{ margin: 1 }}>
      <Typography variant="h6" gutterBottom component="div">
        Files
      </Typography>
      <StyledTable size="small">
        <StyledTableHead>
          <StyledTableRow>
            <StyledHeaderCell>File Name</StyledHeaderCell>
            <StyledHeaderCell>File Type</StyledHeaderCell>
            <StyledHeaderCell>File Size</StyledHeaderCell>
          </StyledTableRow>
        </StyledTableHead>
        <TableBody>
          {files.map((file) => (
            <StyledTableRow key={file.id}>
              <StyledTableCell component="th" scope="row">
                {file.file_name}
              </StyledTableCell>
              <StyledTableCell>{file.file_type}</StyledTableCell>
              <StyledTableCell>{file.file_size} bytes</StyledTableCell>
            </StyledTableRow>
          ))}
        </TableBody>
      </StyledTable>
    </Box>
  );
};
