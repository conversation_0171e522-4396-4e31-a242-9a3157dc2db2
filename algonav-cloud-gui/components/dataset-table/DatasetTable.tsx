'use client';

import React, { useState } from 'react';
import {
  Paper,
  TableBody,
  TablePagination,
} from '@mui/material';
import {
  StyledTableContainer,
  StyledTable,
  StyledTableHead,
  StyledHeaderCell,
  StyledTableRow,
} from '../common/TablePresets';
import { DatasetRow } from './DatasetRow';
import { Dataset } from './types';

interface DatasetTableProps {
  datasets: Dataset[];
  onDelete: (dataset: Dataset) => void;
}

export const DatasetTable = ({ datasets, onDelete }: DatasetTableProps) => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(25);

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  return (
    <Paper sx={{ width: '100%', overflow: 'hidden' }}>
      <StyledTableContainer>
        <StyledTable aria-label="collapsible table">
          <StyledTableHead>
            <StyledTableRow>
              <StyledHeaderCell />
              <StyledHeaderCell>Name</StyledHeaderCell>
              <StyledHeaderCell>Description/Tags</StyledHeaderCell>
              <StyledHeaderCell align="right">Actions</StyledHeaderCell>
            </StyledTableRow>
          </StyledTableHead>
          <TableBody>
            {datasets
              .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
              .map((dataset) => (
                <DatasetRow
                  key={dataset.id}
                  dataset={dataset}
                  onDelete={onDelete}
                />
              ))}
          </TableBody>
        </StyledTable>
      </StyledTableContainer>
      <TablePagination
        rowsPerPageOptions={[5, 10, 25]}
        component="div"
        count={datasets.length}
        rowsPerPage={rowsPerPage}
        page={page}
        onPageChange={handleChangePage}
        onRowsPerPageChange={handleChangeRowsPerPage}
      />
    </Paper>
  );
};
