'use client';

import { useState } from 'react';
import { DatasetFile } from '../types';

export const useDatasetActions = (datasetId: string) => {
  const [files, setFiles] = useState<DatasetFile[]>([]);
  const [isLoading, setIsLoading] = useState(false);

  const fetchFiles = async () => {
    setIsLoading(true);
    try {
      const response = await fetch(`/api/dataset-files?datasetId=${datasetId}`);
      if (response.ok) {
        const data = await response.json();
        setFiles(data.data.map((item: any) => ({
          ...item.files,
          file_type: item.file_type
        })));
      }
    } catch (error) {
      console.error('Error fetching dataset files:', error);
    }
    setIsLoading(false);
  };

  const deleteDataset = async (): Promise<boolean> => {
    try {
      const response = await fetch(`/api/datasets/${datasetId}`, {
        method: 'DELETE',
      });
      
      return response.ok;
    } catch (error) {
      console.error('Error deleting dataset:', error);
      return false;
    }
  };

  return { files, isLoading, fetchFiles, deleteDataset };
};
