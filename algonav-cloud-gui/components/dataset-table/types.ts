export interface Dataset {
  id: string;
  name: string;
  description: string;
  category_id: string;
}

export interface DatasetFile {
  id: string;
  file_name: string;
  file_type: string;
  file_size: number;
}

export interface DatasetTableProps {
  datasets: Dataset[];
  onDelete: (id: string) => void;
}

export interface DatasetRowProps {
  dataset: Dataset;
  onDelete: (id: string) => void;
}
