import React, { useState, useEffect, useRef } from 'react';
import {
    Dialog,
    DialogContent,
    IconButton,
    Typography,
    Box,
    Button,
    Stack,
    CircularProgress,
    AppBar,
    Toolbar,
    useTheme
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import SaveIcon from '@mui/icons-material/Save';
import TemplateRenderer, { TemplateRendererRef } from '@/components/template/TemplateRenderer';
import { useSaveTemplateVars, useMergedTemplateVars } from '@/lib/hooks/useTemplateVars';
import { useTemplateStore } from '@/lib/stores/templateStore';
import { ValidationError } from '@/lib/stores/templateStore';
import { ErrorDialog } from './ErrorDialog';
import { useQuery } from '@tanstack/react-query';
import { api } from '@/lib/services/api';

interface EditTemplateDialogProps {
    open: boolean;
    onClose: () => void;
    templateId: string;
}

export const EditTemplateDialog: React.FC<EditTemplateDialogProps> = ({
    open,
    onClose,
    templateId
}) => {
    console.log('EditTemplateDialog render - templateId:', templateId);

    const theme = useTheme();
    const templateRendererRef = useRef<TemplateRendererRef>(null);
    const { setTemplateVars, clearAllValidationErrors } = useTemplateStore();
    const saveTemplate = useSaveTemplateVars();
    const [error, setError] = useState<string | ValidationError[] | null>(null);
    const [errorDialogOpen, setErrorDialogOpen] = useState(false);
    const [isSaving, setIsSaving] = useState(false);

    // Fetch template details
    const { data: template, isLoading: isLoadingTemplate } = useQuery({
        queryKey: ['template', templateId],
        queryFn: () => api.getTemplate(templateId),
        enabled: !!templateId
    });

    // Use merged template vars
    const { mergedVars, isLoading } = useMergedTemplateVars(templateId);

    // Initialize template variables when merged vars change
    useEffect(() => {
        if (mergedVars) {
            console.log('Initializing merged template vars:', mergedVars);
            setTemplateVars(mergedVars);
        }
    }, [mergedVars, setTemplateVars]);

    // Reset error state when dialog opens/closes
    useEffect(() => {
        setError(null);
        setErrorDialogOpen(false);
    }, [open]);

    const scrollToFirstError = () => {
        const firstErrorElement = document.querySelector('[aria-invalid="true"]');
        if (firstErrorElement) {
            firstErrorElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
        }
    };

    const handleSave = async () => {
        // Check for validation errors
        const templateStore = useTemplateStore.getState();
        if (templateStore.hasValidationErrors()) {
            setError(templateStore.validationErrors);
            setErrorDialogOpen(true);
            return;
        }

        try {
            setIsSaving(true);
            await saveTemplate.mutateAsync({
                templateId: templateId,
                updatedTemplate: {
                    id: templateId,
                    vars: templateStore.templateVars
                }
            });
            setError(null);
            onClose(); // Close dialog after successful save
        } catch (err: any) {
            console.error('Save error:', err);
            setError(err.message || 'Failed to save template');
            setErrorDialogOpen(true);
        } finally {
            setIsSaving(false);
        }
    };

    const handleClose = () => {
        clearAllValidationErrors();
        onClose();
    };

    const handleErrorDialogClose = () => {
        setErrorDialogOpen(false);
        if (Array.isArray(error)) { // If it's validation errors
            scrollToFirstError();
        }
    };

    if (!templateId) {
        return null;
    }

    const isLoadingAny = isLoading || isLoadingTemplate;

    return (
        <Dialog 
            open={open} 
            onClose={handleClose}
            maxWidth="lg"
            fullWidth
            PaperProps={{
                sx: {
                    height: '90vh',
                    maxHeight: '90vh',
                }
            }}
        >
            <AppBar 
                position="sticky" 
                elevation={0}
                sx={{
                    backgroundColor: 'background.paper',
                    borderBottom: `1px solid ${theme.palette.divider}`,
                }}
            >
                <Toolbar sx={{ px: 3 }}>
                    <Typography variant="h6" component="h1" sx={{ flexGrow: 1 }}>
                        {template?.name || `Template ${templateId}`}
                    </Typography>
                    
                    {/* Group Links */}
                    <Box sx={{
                        display: 'flex',
                        gap: 2,
                        mx: 4,
                        overflow: 'auto',
                        '&::-webkit-scrollbar': { height: 6 },
                        '&::-webkit-scrollbar-track': { background: 'transparent' },
                        '&::-webkit-scrollbar-thumb': { background: theme.palette.divider, borderRadius: 3 }
                    }}>
                        {!isLoadingAny && mergedVars && Array.from(mergedVars
                            .reduce((groups: Set<string>, variable: any) => {
                                if (variable.gui?.group) groups.add(variable.gui.group);
                                return groups;
                            }, new Set())
                        ).map((group: string) => (
                            <Button
                                key={group}
                                variant="text"
                                size="small"
                                onClick={() => {
                                    if (templateRendererRef.current) {
                                        templateRendererRef.current.scrollToGroup(group);
                                    }
                                }}
                                sx={{
                                    color: 'text.secondary',
                                    '&:hover': {
                                        color: 'text.primary',
                                    },
                                    whiteSpace: 'nowrap'
                                }}
                            >
                                {group}
                            </Button>
                        ))}
                    </Box>

                    <Stack direction="row" spacing={2} alignItems="center">
                        <Button
                            variant="contained"
                            color="primary"
                            onClick={handleSave}
                            disabled={isSaving}
                            startIcon={isSaving ? <CircularProgress size={20} /> : <SaveIcon />}
                            size="large"
                        >
                            {isSaving ? 'Saving...' : 'Save Changes'}
                        </Button>
                        <IconButton 
                            onClick={handleClose} 
                            size="large"
                            sx={{
                                color: 'text.secondary',
                                '&:hover': {
                                    color: 'text.primary',
                                }
                            }}
                        >
                            <CloseIcon />
                        </IconButton>
                    </Stack>
                </Toolbar>
            </AppBar>

            <DialogContent sx={{ p: 0, display: 'flex', flexDirection: 'column' }}>
                <ErrorDialog 
                    open={errorDialogOpen}
                    onClose={handleErrorDialogClose}
                    error={error}
                    title="Validation Error"
                />

                <Box sx={{ p: 3 }}>
                    {isLoadingAny ? (
                        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                            <CircularProgress />
                        </Box>
                    ) : (
                        <TemplateRenderer
                            ref={templateRendererRef}
                            templateId={templateId}
                            templateName={template?.name}
                        />
                    )}
                </Box>
            </DialogContent>
        </Dialog>
    );
};
