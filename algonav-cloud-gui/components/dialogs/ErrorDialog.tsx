import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography
} from '@mui/material';
import ErrorOutlineIcon from '@mui/icons-material/ErrorOutline';

interface ValidationError {
  variableName: string;
  message: string;
}

interface ErrorDialogProps {
  open: boolean;
  onClose: () => void;
  error: string | null | ValidationError[];
  title?: string;
}

export const ErrorDialog = ({ 
  open, 
  onClose, 
  error, 
  title = 'Error' 
}: ErrorDialogProps) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>{title}</DialogTitle>
      <DialogContent sx={{ textAlign: 'center', py: 3 }}>
        <ErrorOutlineIcon color="error" sx={{ fontSize: 48, mb: 2 }} />
        {Array.isArray(error) ? (
          error.map((err, index) => (
            <Typography key={index} color="error" sx={{ mb: 1 }}>
              Variable <strong>{err.variableName}</strong>: {err.message}
            </Typography>
          ))
        ) : (
          <Typography color="error">{error}</Typography>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary" autoFocus>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};
