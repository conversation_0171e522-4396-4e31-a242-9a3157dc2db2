import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography
} from '@mui/material';
import CheckCircleOutlineIcon from '@mui/icons-material/CheckCircleOutline';

interface JobSuccessDialogProps {
  open: boolean;
  onClose: () => void;
}

export const JobSuccessDialog = ({ open, onClose }: JobSuccessDialogProps) => {
  return (
    <Dialog open={open} onClose={onClose}>
      <DialogTitle>Success</DialogTitle>
      <DialogContent sx={{ textAlign: 'center', py: 3 }}>
        <CheckCircleOutlineIcon color="success" sx={{ fontSize: 48, mb: 2 }} />
        <Typography>Job has been successfully created!</Typography>
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="primary" autoFocus>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};
