import { FormControl, FormLabel, FormGroup, FormControlLabel, Checkbox, FormHelperText, Stack, Tooltip } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { useTemplateStore } from '../../lib/stores/templateStore';
import { useState } from 'react';

// Deep equality comparison for any data type
function isEqual(a: any, b: any): boolean {
  // Handle null/undefined
  if (a === b) return true;
  if (a === null || b === null) return false;
  if (a === undefined || b === undefined) return false;

  // Handle different types
  if (typeof a !== typeof b) return false;

  // Handle arrays
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    return a.every((item, index) => isEqual(item, b[index]));
  }

  // Handle objects
  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    
    if (keysA.length !== keysB.length) return false;
    
    return keysA.every(key => 
      Object.prototype.hasOwnProperty.call(b, key) && 
      isEqual(a[key], b[key])
    );
  }

  // Handle primitives
  return Object.is(a, b);
}

interface CheckboxGroupGUIProps {
  value: any[];
  onChange: (value: any[]) => void;
  name: string;
  gui: {
    label?: string;
    items?: Array<{
      label: string;
      value: any;
    }>;
    min_checked?: number;
    max_checked?: number;
    msg_below_min?: string;
    tooltip?: string;
    [key: string]: any;
  };
}

export default function CheckboxGroup({
  value = [],
  onChange,
  gui,
  name,
}: CheckboxGroupGUIProps) {
  const { setValidationError, getValidationError } = useTemplateStore();
  const [touched, setTouched] = useState(false);
  const error = getValidationError(name);

  const handleChange = (itemValue: any, checked: boolean) => {
    let newValue: any[];
    
    if (checked) {
      // Add value if not already present (using deep equality)
      if (!value.some(v => isEqual(v, itemValue))) {
        newValue = [...value, itemValue];
      } else {
        return; // Value already present
      }
    } else {
      // Remove value if present (using deep equality)
      newValue = value.filter(v => !isEqual(v, itemValue));
    }

    // Always allow the change, validation happens on blur
    onChange(newValue);
  };

  const handleBlur = () => {
    setTouched(true);
    const selectedCount = value.length;

    // Validate against min/max constraints
    if (gui.min_checked && selectedCount < gui.min_checked) {
      setValidationError(name, gui.msg_below_min || `Please select at least ${gui.min_checked} items`);
      return;
    }
    if (gui.max_checked && selectedCount > gui.max_checked) {
      setValidationError(name, `Maximum ${gui.max_checked} items can be selected`);
      return;
    }

    // Clear validation error if valid
    setValidationError(name, null);
  };

  // Only show error message when there is one
  const helperText = touched && error ? error : '';

  return (
    <FormControl 
      component="fieldset" 
      sx={{ width: '100%' }} 
      error={!!error && touched}
      onBlur={handleBlur}
    >
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel component="legend">
            {gui.label}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}
      <FormGroup>
        {gui.items?.map((item) => (
          <FormControlLabel
            key={JSON.stringify(item.value)}
            control={
              <Checkbox
                checked={value.some((v) => isEqual(v, item.value))}
                onChange={(e) => handleChange(item.value, e.target.checked)}
              />
            }
            label={item.label}
          />
        ))}
      </FormGroup>
      {helperText && <FormHelperText>{helperText}</FormHelperText>}
    </FormControl>
  );
}