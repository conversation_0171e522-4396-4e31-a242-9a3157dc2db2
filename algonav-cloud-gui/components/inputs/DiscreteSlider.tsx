import { FormControl, FormLabel, <PERSON>lider, <PERSON>, Stack, Tooltip } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { styled } from '@mui/system';

interface DiscreteSliderGUIProps {
  value: number;
  onChange: (value: number) => void;
  name: string;
  gui: {
    label?: string;
    items?: Array<{
      label: string;
      value: number;
    }>;
    tooltip?: string;
    [key: string]: any;
  };
}

const StyledSlider = styled(Slider)(({ theme }) => ({
  '& .MuiSlider-thumb': {
    height: 24,
    width: 24,
    backgroundColor: '#fff',
    border: '2px solid currentColor',
    '&:focus, &:hover, &.Mui-active, &.Mui-focusVisible': {
      boxShadow: 'inherit',
    },
  },
  '& .MuiSlider-mark': {
    backgroundColor: theme.palette.primary.main,
    height: 8,
    width: 1,
    marginTop: -3,
  },
  '& .MuiSlider-markActive': {
    backgroundColor: theme.palette.primary.main,
  },
  '& .MuiSlider-markLabel': {
    fontSize: '0.875rem',
    fontWeight: 400,
    color: theme.palette.text.secondary,
    width: '48px',  // Fixed width for label container
    textAlign: 'center',
    whiteSpace: 'normal',  // Allow text wrapping
    lineHeight: 1.2,
    marginTop: '8px',
    transform: 'translate(-24px, 0)',  // Center the label (half of width)
    '&.selected': {
      color: theme.palette.primary.main,
      fontWeight: 500,
    },
  },
  '& .MuiSlider-rail': {
    opacity: 0.8,
  },
  marginLeft: '24px',   // Add margin to accommodate first label
  marginRight: '24px',  // Add margin to accommodate last label
  width: 'calc(100% - 48px)',  // Adjust width to account for margins
}));

export default function DiscreteSlider({
  value,
  onChange,
  gui,
}: DiscreteSliderGUIProps) {
  const items = gui.items || [];

  // Create marks for the slider with custom label class for the selected value
  const marks = items.map((item, index) => ({
    value: index,
    label: item.label,
    className: item.value === value ? 'selected' : '',
  }));

  // Find the index of the current value in items
  const currentIndex = items.findIndex(item => item.value === value);

  const handleChange = (_event: Event, newIndex: number | number[]) => {
    // Convert the index back to the actual value
    const index = Array.isArray(newIndex) ? newIndex[0] : newIndex;
    const newValue = items[index]?.value ?? value;
    onChange(newValue);
  };

  return (
    <FormControl fullWidth>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
          <FormLabel>
            {gui.label}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}
      <Box sx={{ 
        width: '100%', 
        mt: 3,
        mb: 5, // Add bottom margin to accommodate wrapped labels
      }}>
        <StyledSlider
          value={currentIndex === -1 ? 0 : currentIndex}
          onChange={handleChange}
          step={null}
          marks={marks}
          min={0}
          max={items.length - 1}
          valueLabelDisplay="off"
        />
      </Box>
    </FormControl>
  );
}
