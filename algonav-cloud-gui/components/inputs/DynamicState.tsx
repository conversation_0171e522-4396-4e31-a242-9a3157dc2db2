// components/inputs/DynamicState.tsx
import {
  FormControl,
  FormLabel,
  Box,
  RadioGroup,
  FormControlLabel,
  Radio,
  Tooltip,
  TextField,
  Typography,
  Checkbox,
  InputAdornment,
  Stack,
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { styled } from '@mui/system';
import { Fragment, useState, useEffect } from 'react';
import isEqual from 'lodash/isEqual';
import { useTemplateStore } from '../../lib/stores/templateStore';
import { validateDisplayNumber } from '../../utils/numberValidation';

export const PROCESS_TYPE = {
  CONST: 'CONST',
  CONSIDER: 'CONSIDER',
  RC: 'RC',
  RW: 'RW',
} as const;
type ProcessType = typeof PROCESS_TYPE[keyof typeof PROCESS_TYPE];

const StyledTextField = styled(TextField)(({ theme }) => ({
  '& .MuiInputBase-root': {
    width: '160px',
  },
  '& .MuiFormHelperText-root': {
    position: 'absolute',
    bottom: '-20px',
  },
  '& .MuiInputLabel-root': {
    width: 'auto',
    whiteSpace: 'nowrap',
  },
  '& input[type=number]': {
    MozAppearance: 'textfield',
    '&::-webkit-outer-spin-button, &::-webkit-inner-spin-button': {
      WebkitAppearance: 'none',
      margin: '0',
    },
  },
}));

const StyledWideTextField = styled(StyledTextField)(({ theme }) => ({
  '& .MuiInputBase-root': {
    width: '180px',
  },
}));

const ProcessTypeSelector = ({
  availableTypes,
  value,
  onChange,
}: {
  availableTypes: string[];
  value: ProcessType;
  onChange: (newType: ProcessType) => void;
}) => {
  const getProcessTypeLabel = (type: string) => {
    switch (type) {
      case PROCESS_TYPE.RC:
        return "Estimate as constant (random constant)";
      case PROCESS_TYPE.CONSIDER:
        return (
          <Stack direction="row" spacing={1} alignItems="center">
            Consider State
            <Tooltip title="I know that this state has some uncertainty, but I do not want the software to estimate it">
              <HelpOutlineIcon sx={{ fontSize: '1rem', verticalAlign: 'middle' }} />
            </Tooltip>
          </Stack>
        );
      case PROCESS_TYPE.RW:
        return "Estimate as variable (random walk)";
      case PROCESS_TYPE.CONST:
        return "Fixed Value";
      default:
        return type;
    }
  };

  return (
    <Box sx={{ mb: 4, bgcolor: 'grey.50', p: 2, borderRadius: 1 }}>
      <FormLabel sx={{ mb: 1, display: 'block' }}>Process Type</FormLabel>
      <RadioGroup
        value={value}
        onChange={(e) => onChange(e.target.value as ProcessType)}
      >
        {availableTypes.map((type) => (
          <FormControlLabel
            key={type}
            value={type}
            control={<Radio />}
            label={getProcessTypeLabel(type)}
            sx={{ mb: 1 }}
          />
        ))}
      </RadioGroup>
    </Box>
  );
};

const UnifiedUncertaintyToggle = ({
  unified,
  globalStd0,
  onToggle,
  onGlobalStd0Change,
  error,
  gui,
  multiplyFactor,
  maxDigits,
}: {
  unified: boolean;
  globalStd0: number;
  onToggle: (checked: boolean) => void;
  onGlobalStd0Change: (val: string) => void;
  error?: string | null;
  gui: any;
  multiplyFactor: number;
  maxDigits?: number;
}) => {
  const roundedGlobalStd0 = isNaN(globalStd0)
    ? ''
    : (globalStd0 * multiplyFactor);
  const [localValue, setLocalValue] = useState<string>(String(roundedGlobalStd0));

  useEffect(() => {
    const rounded = isNaN(globalStd0)
      ? ''
      : (globalStd0 * multiplyFactor);
    setLocalValue(String(rounded));
  }, [globalStd0, multiplyFactor, maxDigits]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalValue(e.target.value);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    onGlobalStd0Change(localValue);
  };

  return (
    <Box
      sx={{
        mb: 3,
        bgcolor: 'grey.50',
        p: 2,
        borderRadius: 1,
        display: 'flex',
        alignItems: 'center',
        gap: 2,
      }}
    >
      <FormControlLabel
        control={
          <Checkbox
            checked={unified}
            onChange={(e) => onToggle(e.target.checked)}
          />
        }
        label="Use same initial uncertainty"
      />
      {unified && (
        <StyledTextField
          type="number"
          size="small"
          label="Initial Uncertainty"
          value={localValue}
          onChange={handleChange}
          onBlur={handleBlur}
          error={!!error}
          helperText={error}
          InputProps={{
            endAdornment:
              gui.unit && (
                <InputAdornment position="end">{gui.unit}</InputAdornment>
              ),
          }}
        />
      )}
    </Box>
  );
};

interface AxisRowProps {
  axisIndex: number;
  axisLabel: string;
  initValue: number;
  std0Value: number;
  estaxSelected: boolean;
  canEditStd0: boolean;
  processType: ProcessType;
  onToggleAxis: (axisIndex: number) => void;
  onInitValueChange: (axisIndex: number, val: string) => void;
  onStd0ValueChange: (axisIndex: number, val: string) => void;
  initError?: string | null;
  std0Error?: string | null;
  unit?: string;
  gui: any;
  multiplyFactor: number;
  maxDigits?: number;
}
const AxisRow = ({
  axisIndex,
  axisLabel,
  initValue,
  std0Value,
  estaxSelected,
  canEditStd0,
  processType,
  onToggleAxis,
  onInitValueChange,
  onStd0ValueChange,
  initError,
  std0Error,
  unit,
  gui,
  multiplyFactor,
  maxDigits,
}: AxisRowProps) => {
  const roundedInitValue = isNaN(initValue)
    ? ''
    : (initValue * multiplyFactor);
  const roundedStd0Value = isNaN(std0Value)
    ? ''
    : (std0Value * multiplyFactor);

  const [localInit, setLocalInit] = useState<string>(String(roundedInitValue));
  const [localStd0, setLocalStd0] = useState<string>(String(roundedStd0Value));

  useEffect(() => {
    const rounded = isNaN(initValue) ? '' : (initValue * multiplyFactor);
    setLocalInit(String(rounded));
  }, [initValue, multiplyFactor]);

  useEffect(() => {
    const rounded = isNaN(std0Value) ? '' : (std0Value * multiplyFactor);
    setLocalStd0(String(rounded));
  }, [std0Value, multiplyFactor]);

  const handleInitChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalInit(e.target.value);
  };

  const handleInitBlur = () => {
    onInitValueChange(axisIndex, localInit);
  };

  const handleStd0Change = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalStd0(e.target.value);
  };

  const handleStd0Blur = () => {
    onStd0ValueChange(axisIndex, localStd0);
  };

  const showStd0Field = processType !== PROCESS_TYPE.CONST && estaxSelected;

  return (
    <Box sx={{ 
      display: 'grid', 
      gridTemplateColumns: '180px 180px auto', // Increased from 140px to 180px for the axis label column
      gap: 1, 
      mb: 0, 
      alignItems: 'flex-start' 
    }}>
      <FormControlLabel
        control={
          <Checkbox
            checked={estaxSelected}
            onChange={() => onToggleAxis(axisIndex)}
            disabled={processType === PROCESS_TYPE.CONST}
          />
        }
        label={axisLabel}
        sx={{
          m: 0,
          '& .MuiFormControlLabel-label': {
            minWidth: '60px',
          },
        }}
      />
      <Box sx={{ position: 'relative', minHeight: '80px' }}>
        <StyledTextField
          type="number"
          size="small"
          value={localInit}
          onChange={handleInitChange}
          onBlur={handleInitBlur}
          error={!!initError}
          helperText={initError}
          InputProps={{
            endAdornment: unit && <InputAdornment position="end">{unit}</InputAdornment>,
          }}
        />
      </Box>
      {showStd0Field && (
        <Box sx={{ position: 'relative', minHeight: '80px' }}>
          <StyledWideTextField
            type="number"
            size="small"
            value={localStd0}
            onChange={handleStd0Change}
            onBlur={handleStd0Blur}
            error={!!std0Error}
            helperText={std0Error}
            label="Initial Uncertainty"
            disabled={!canEditStd0}
            InputProps={{
              endAdornment: unit && <InputAdornment position="end">{unit}</InputAdornment>,
            }}
          />
        </Box>
      )}
    </Box>
  );
};

interface AxesConfigurationProps {
  visibleAxes: number[];
  processType: ProcessType;
  initValues: number[];
  std0Values: number[];
  estax: number[];
  unifiedUncertainty: boolean;
  onToggleAxis: (axisIndex: number) => void;
  onInitValueChange: (axisIndex: number, val: string) => void;
  onStd0ValueChange: (axisIndex: number, val: string) => void;
  validationErrors: Record<string, string | null>;
  gui: any;
  name: string;
  multiplyFactor: number;
  maxDigits?: number;
}
const AxesConfiguration = ({
  visibleAxes,
  processType,
  initValues,
  std0Values,
  estax,
  unifiedUncertainty,
  onToggleAxis,
  onInitValueChange,
  onStd0ValueChange,
  validationErrors,
  gui,
  name,
  multiplyFactor,
  maxDigits,
}: AxesConfigurationProps) => {
  const getAxesConfigLabel = () => {
    switch (processType) {
      case PROCESS_TYPE.RC:
        return "Select Axes to be estimated by the Software";
      case PROCESS_TYPE.CONSIDER:
        return "Select Axes to be considered with an uncertainty";
      case PROCESS_TYPE.RW:
        return "Select Axes to be estimated";
      default:
        return "Select Axes to be estimated";
    }
  };

  const getAxisLabel = (i: number) => gui.axes?.[i] || `Axis ${i + 1}`;

  return (
    <Box sx={{ mt: 2 }}>
      <Typography
        variant="body2"
        sx={{
          mb: 2,
          color: processType === PROCESS_TYPE.CONST ? 'text.disabled' : 'text.primary',
        }}
      >
        {getAxesConfigLabel()}
      </Typography>
      {visibleAxes.map((axisIndex) => (
        <AxisRow
          key={axisIndex}
          axisIndex={axisIndex}
          axisLabel={getAxisLabel(axisIndex)}
          initValue={initValues[axisIndex]}
          std0Value={std0Values[axisIndex]}
          estaxSelected={estax.includes(axisIndex)}
          canEditStd0={!unifiedUncertainty}
          processType={processType}
          onToggleAxis={onToggleAxis}
          onInitValueChange={onInitValueChange}
          onStd0ValueChange={onStd0ValueChange}
          initError={validationErrors[`${name}_init_${axisIndex}`]}
          std0Error={validationErrors[`${name}_std0_${axisIndex}`]}
          unit={gui.unit}
          gui={gui}
          multiplyFactor={multiplyFactor}
          maxDigits={maxDigits}
        />
      ))}
    </Box>
  );
};

interface RandomWalkConfigProps {
  sqrtQ: number;
  onSqrtQChange: (val: string) => void;
  error?: string | null;
  gui: any;
  multiplyFactor: number;
  maxDigits?: number;
}
const RandomWalkConfig = ({
  sqrtQ,
  onSqrtQChange,
  error,
  gui,
  multiplyFactor,
  maxDigits,
}: RandomWalkConfigProps) => {
  const roundedSqrtQ = isNaN(sqrtQ) ? '' : (sqrtQ * multiplyFactor);
  const [localSqrtQ, setLocalSqrtQ] = useState<string>(String(roundedSqrtQ));

  useEffect(() => {
    const rounded = isNaN(sqrtQ) ? '' : (sqrtQ * multiplyFactor);
    setLocalSqrtQ(String(rounded));
  }, [sqrtQ, multiplyFactor, maxDigits]);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setLocalSqrtQ(e.target.value);
  };
  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    onSqrtQChange(localSqrtQ);
  };
  const displayStep = gui.integer
    ? (1 * multiplyFactor).toString()
    : gui.max_digits
      ? (Math.pow(10, -gui.max_digits) * multiplyFactor).toString()
      : 'any';

  return (
    <Box sx={{ mt: 3 }}>
      <Typography variant="body2" sx={{ mb: 2.5 }}>
      Variability over time
      </Typography>
      <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
        <StyledWideTextField
          type="number"
          size="small"
          label="Variability over time"
          value={localSqrtQ}
          onChange={handleChange}
          onBlur={handleBlur}
          error={!!error}
          helperText={error}
          InputProps={{
            endAdornment: gui.unit && (
              <InputAdornment position="end">{`${gui.unit}/√s`}</InputAdornment>
            ),
          }}
        />
        
      </Box>
    </Box>
  );
};

interface DynamicStateGUIProps {
  value: {
    init: number[];
    std0: number[];
    estax: number[];
    processes: {
      type: ProcessType;
      sqrt_q?: number;
    };
    unified_uncertainty?: boolean;
  };
  onChange: (value: any) => void;
  name: string;
  gui: {
    label?: string;
    axes?: string[];
    visible_axes?: number[];
    tooltip?: string;
    process_type?: string[];
    min_value?: number;
    max_value?: number;
    max_digits?: number;
    unit?: string;
    multiply?: number;
    [key: string]: any;
  };
}
export default function DynamicState({
  value,
  onChange,
  gui,
  name,
}: DynamicStateGUIProps) {
  const { setValidationError, getValidationError } = useTemplateStore();
  const visibleAxes = gui.visible_axes ?? value.init.map((_, i) => i);
  const [previousEstax, setPreviousEstax] = useState<number[]>(value.estax);
  const [previousStd0, setPreviousStd0] = useState<number[]>(value.std0);
  const multiplyFactor = gui.multiply || 1;
  const maxDigits = gui.max_digits;

  const [unifiedUncertainty, setUnifiedUncertainty] = useState(() => {
    if (value.unified_uncertainty !== undefined) {
      return value.unified_uncertainty;
    }
    if (value.std0.length === 1 && value.estax.length > 1) {
      return true;
    }
    const uniqueValues = new Set(value.std0.filter((v) => !isNaN(v)));
    return uniqueValues.size <= 1;
  });

  useEffect(() => {
    if (value.unified_uncertainty !== unifiedUncertainty) {
      onChange({ ...value, unified_uncertainty: unifiedUncertainty });
    }
  }, [unifiedUncertainty]);

  useEffect(() => {
    if (value.processes.type !== PROCESS_TYPE.CONST) {
      setPreviousEstax(value.estax);
      setPreviousStd0(value.std0);
    }
  }, [value.processes.type, value.estax, value.std0]);

  useEffect(() => {
    if (!unifiedUncertainty && value.processes.type !== PROCESS_TYPE.CONST) {
      const newStd0 = [...value.std0];
      let needsUpdate = false;
      for (let i = 0; i < value.init.length; i++) {
        if (!value.estax.includes(i)) {
          newStd0[i] = 0;
          needsUpdate = true;
        }
      }
      if (needsUpdate) {
        onChange({ ...value, std0: newStd0 });
      }
    }
  }, []);

  const handleProcessTypeChange = (newType: ProcessType) => {
    let newEstax = value.estax;
    let newStd0 = value.std0;
    const newProcesses = { ...value.processes, type: newType };
    if (newType === PROCESS_TYPE.RW) {
      newProcesses.sqrt_q = newProcesses.sqrt_q ?? 0;
    } else {
      if ('sqrt_q' in newProcesses) {
        delete newProcesses.sqrt_q;
      }
    }
    if (newType === PROCESS_TYPE.CONST) {
      newEstax = [];
    } else if (value.processes.type === PROCESS_TYPE.CONST) {
      newEstax = previousEstax;
      newStd0 = previousStd0;
    }
    const newValue = {
      ...value,
      processes: newProcesses,
      estax: newEstax,
      std0: newStd0,
    };
    if (!isEqual(value, newValue)) {
      onChange(newValue);
    }
  };

  const handleAxisToggle = (axisIndex: number) => {
    if (value.processes.type === PROCESS_TYPE.CONST) return;
    const isSelected = value.estax.includes(axisIndex);
    let newEstax: number[];
    if (isSelected) {
      newEstax = value.estax.filter((i) => i !== axisIndex);
    } else {
      newEstax = [...value.estax, axisIndex].sort();
    }
    const newStd0 = [...value.std0];
    if (!unifiedUncertainty) {
      newStd0[axisIndex] = 0;
    }
    onChange({ ...value, estax: newEstax, std0: newStd0 });
  };

  const handleInitValueChange = (axisIndex: number, newVal: string) => {
    if (newVal === '') {
      setValidationError(`${name}_init_${axisIndex}`, 'Required');
      const newInit = [...value.init];
      newInit[axisIndex] = NaN;
      onChange({ ...value, init: newInit });
      return;
    }
    const numValue = parseFloat(newVal) / multiplyFactor;
    const err = validateDisplayNumber(numValue, gui);
    setValidationError(`${name}_init_${axisIndex}`, err);
    const newInit = [...value.init];
    newInit[axisIndex] = numValue;
    onChange({ ...value, init: newInit });
  };

  const handleStd0ValueChange = (axisIndex: number, newVal: string) => {
    if (newVal === '') {
      const errorKey = unifiedUncertainty ? `${name}_global_std0` : `${name}_std0_${axisIndex}`;
      setValidationError(errorKey, 'Required');
      let newStd0: number[];
      if (unifiedUncertainty) {
        newStd0 = [...value.std0];
        visibleAxes.forEach((idx) => {
          newStd0[idx] = NaN;
        });
      } else {
        newStd0 = [...value.std0];
        newStd0[axisIndex] = NaN;
      }
      onChange({ ...value, std0: newStd0 });
      return;
    }
    const numValue = parseFloat(newVal) / multiplyFactor;
    const err = validateDisplayNumber(numValue, { ...gui, min_value: 0 });
    const errorKey = unifiedUncertainty ? `${name}_global_std0` : `${name}_std0_${axisIndex}`;
    setValidationError(errorKey, err);
    let newStd0: number[];
    if (unifiedUncertainty) {
      newStd0 = [...value.std0];
      visibleAxes.forEach((idx) => {
        newStd0[idx] = numValue;
      });
    } else {
      newStd0 = [...value.std0];
      newStd0[axisIndex] = numValue;
    }
    onChange({ ...value, std0: newStd0 });
  };

  const handleToggleUnified = (checked: boolean) => {
    setUnifiedUncertainty(checked);
    if (checked) {
      const firstVisibleValue = value.std0[visibleAxes[0]];
      const newStd0 = [...value.std0];
      visibleAxes.forEach((idx) => {
        newStd0[idx] = firstVisibleValue;
      });
      onChange({ ...value, std0: newStd0, unified_uncertainty: checked });
    } else if (!checked && value.std0.length === 1) {
      const expandedStd0 = [...value.init].map((_, idx) => {
        if (visibleAxes.includes(idx)) {
          return value.std0[0];
        }
        return value.std0[idx] || value.std0[0];
      });
      onChange({ ...value, std0: expandedStd0, unified_uncertainty: checked });
    }
  };

  const validationErrors: Record<string, string | null> = {};
  visibleAxes.forEach((axisIndex) => {
    validationErrors[`${name}_init_${axisIndex}`] = getValidationError(`${name}_init_${axisIndex}`);
    validationErrors[`${name}_std0_${axisIndex}`] = getValidationError(`${name}_std0_${axisIndex}`);
  });
  validationErrors[`${name}_global_std0`] = getValidationError(`${name}_global_std0`);
  validationErrors[`${name}_sqrt_q`] = getValidationError(`${name}_sqrt_q`);

  return (
    <FormControl fullWidth>
      <Box
        sx={{
          mb: 3,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderBottom: '1px solid',
          borderColor: 'divider',
          pb: 1,
        }}
      >
        <Stack direction="row" spacing={1} alignItems="center">
          <Typography variant="body1" sx={{ color: 'text.primary', fontWeight: 500 }}>
            {gui.label || 'Dynamic State Configuration'}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }} />
              </Tooltip>
            )}
          </Typography>
        </Stack>
      </Box>
      <ProcessTypeSelector
        availableTypes={Array.isArray(gui.process_type) ? gui.process_type : (typeof gui.process_type === 'string' ? [gui.process_type] : [])}
        value={value.processes.type}
        onChange={handleProcessTypeChange}
      />
      {value.processes.type !== PROCESS_TYPE.CONST && (
        <UnifiedUncertaintyToggle
          unified={unifiedUncertainty}
          globalStd0={value.std0[0]}
          onToggle={handleToggleUnified}
          onGlobalStd0Change={(val) => handleStd0ValueChange(0, val)}
          error={validationErrors[`${name}_global_std0`]}
          gui={gui}
          multiplyFactor={multiplyFactor}
          maxDigits={maxDigits}
        />
      )}
      <AxesConfiguration
        visibleAxes={visibleAxes}
        processType={value.processes.type}
        initValues={value.init}
        std0Values={value.std0}
        estax={value.estax}
        unifiedUncertainty={unifiedUncertainty}
        onToggleAxis={handleAxisToggle}
        onInitValueChange={handleInitValueChange}
        onStd0ValueChange={handleStd0ValueChange}
        validationErrors={validationErrors}
        gui={gui}
        name={name}
        multiplyFactor={multiplyFactor}
        maxDigits={maxDigits}
      />
      {value.processes.type === PROCESS_TYPE.RW && (
        <RandomWalkConfig
          sqrtQ={value.processes.sqrt_q ?? 0}
          onSqrtQChange={(newVal) => {
            if (newVal === '') {
              setValidationError(`${name}_sqrt_q`, 'Required');
              onChange({
                ...value,
                processes: { ...value.processes, sqrt_q: NaN },
              });
              return;
            }
            const numValue = parseFloat(newVal) / multiplyFactor;
            const err = validateDisplayNumber(numValue, { ...gui, min_value: 0 });
            setValidationError(`${name}_sqrt_q`, err);
            onChange({
              ...value,
              processes: { ...value.processes, sqrt_q: numValue },
            });
          }}
          error={getValidationError(`${name}_sqrt_q`)}
          gui={gui}
          multiplyFactor={multiplyFactor}
          maxDigits={maxDigits}
        />
      )}
    </FormControl>
  );
}
