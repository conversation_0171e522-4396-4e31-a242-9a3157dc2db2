// components/inputs/IntegerSlider.tsx
import React from 'react';
import { <PERSON>lider, FormControl, FormLabel, Stack, Tooltip } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface IntegerSliderGUI {
  label?: string;
  tooltip?: string;
  min_value: number;
  max_value: number;
}

interface IntegerSliderProps {
  value: number;
  onChange: (value: number) => void;
  name: string;
  gui: IntegerSliderGUI;
}

export default function IntegerSlider({ value, onChange, name, gui }: IntegerSliderProps) {
  const handleChange = (event: Event, newValue: number | number[]) => {
    if (typeof newValue === 'number') {
      onChange(newValue);
    }
  };

  // Erzeuge Markierungen für jeden ganzzahligen Wert im Bereich
  const marks = [];
  for (let i = gui.min_value; i <= gui.max_value; i++) {
    marks.push({ value: i, label: i.toString() });
  }

  return (
    <FormControl fullWidth>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 2 }}>
          <FormLabel>
            {gui.label}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}
      <Slider
        value={value}
        onChange={handleChange}
        step={1}
        marks={marks}
        min={gui.min_value}
        max={gui.max_value}
        valueLabelDisplay="auto"
      />
    </FormControl>
  );
}
