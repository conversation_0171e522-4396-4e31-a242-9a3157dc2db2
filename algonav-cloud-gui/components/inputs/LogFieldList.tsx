// components/inputs/LogFieldList.tsx
import { useState } from 'react';
import {
  List,
  ListItem,
  ListItemText,
  ListItemSecondaryAction,
  IconButton,
  Paper,
  Typography,
  Box,
  Tooltip,
  Divider,
  Button
} from '@mui/material';
import DeleteIcon from '@mui/icons-material/Delete';
import EditIcon from '@mui/icons-material/Edit';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import ArrowUpwardIcon from '@mui/icons-material/ArrowUpward';
import ArrowDownwardIcon from '@mui/icons-material/ArrowDownward';

interface LogFieldListProps {
  fields: Array<{
    field: string;
    logprefix: string;
    caption: string;
    [key: string]: any;
  }>;
  onRemoveField: (index: number) => void;
  onEditField: (index: number) => void;
  onReorderFields: (startIndex: number, endIndex: number) => void;
}

export default function LogFieldList({
  fields,
  onRemoveField,
  onEditField,
  onReorderFields,
}: LogFieldListProps) {
  const handleMoveUp = (index: number) => {
    if (index > 0) {
      onReorderFields(index, index - 1);
    }
  };

  const handleMoveDown = (index: number) => {
    if (index < fields.length - 1) {
      onReorderFields(index, index + 1);
    }
  };

  return (
    <Paper variant="outlined" sx={{ mt: 2 }}>
      <Box p={2} bgcolor="background.default">
        <Typography variant="subtitle1" fontWeight="bold">
          Selected Log Fields ({fields.length})
        </Typography>
        <Typography variant="caption">
          Use the up/down buttons to reorder fields. The order here will be reflected in the CSV output.
        </Typography>
      </Box>

      <Divider />

      {fields.length === 0 ? (
        <Box p={4} textAlign="center">
          <Typography variant="body2" color="text.secondary">
            No fields selected. Click "Add Fields" to select log fields.
          </Typography>
        </Box>
      ) : (
        <List dense>
          {fields.map((field, index) => (
            <ListItem
              key={`${field.logprefix}-${field.field}-${index}`}
              divider={index < fields.length - 1}
            >
              <Box
                sx={{
                  display: 'flex',
                  flexDirection: 'column',
                  mr: 1
                }}
              >
                <IconButton
                  size="small"
                  onClick={() => handleMoveUp(index)}
                  disabled={index === 0}
                >
                  <ArrowUpwardIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => handleMoveDown(index)}
                  disabled={index === fields.length - 1}
                >
                  <ArrowDownwardIcon fontSize="small" />
                </IconButton>
              </Box>

              <ListItemText
                primary={field.caption}
                secondary={
                  <Box component="span">
                    {field.logprefix && (
                      <Typography
                        component="span"
                        variant="caption"
                        color="primary"
                        sx={{ mr: 1 }}
                      >
                        {field.logprefix}:
                      </Typography>
                    )}
                    {field.field}
                  </Box>
                }
              />

              <Box>
                <Tooltip title="Edit field settings">
                  <IconButton
                    edge="end"
                    aria-label="edit"
                    onClick={() => onEditField(index)}
                    size="small"
                    sx={{ mr: 1 }}
                  >
                    <EditIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
                <Tooltip title="Remove field">
                  <IconButton
                    edge="end"
                    aria-label="delete"
                    onClick={() => onRemoveField(index)}
                    size="small"
                  >
                    <DeleteIcon fontSize="small" />
                  </IconButton>
                </Tooltip>
              </Box>
            </ListItem>
          ))}
        </List>
      )}
    </Paper>
  );
}
