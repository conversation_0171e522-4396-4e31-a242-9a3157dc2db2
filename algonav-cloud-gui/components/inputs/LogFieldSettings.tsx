// components/inputs/LogFieldSettings.tsx
import { useState, useEffect } from 'react';
import { 
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Typography,
  Box,
  Divider,
  Tab,
  Tabs
} from '@mui/material';
import LogSettingCols from './LogSettingCols';
import LogSettingColsPS from './LogSettingColsPS';
import LogSettingEccFRD from './LogSettingEccFRD';
import LogSettingAsciiFormat from './LogSettingAsciiFormat';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`field-settings-tabpanel-${index}`}
      aria-labelledby={`field-settings-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface LogFieldSettingsProps {
  open: boolean;
  onClose: () => void;
  field: {
    field: string;
    logprefix: string;
    caption: string;
    [key: string]: any;
  } | null;
  databaseField: {
    field: string;
    caption: string;
    precision: string;
    settings: Array<{
      name: string;
      data: any;
      gui: {
        component_id: string;
      };
    }>;
  } | null;
  onSave: (updatedField: any) => void;
}

export default function LogFieldSettings({
  open,
  onClose,
  field,
  databaseField,
  onSave,
}: LogFieldSettingsProps) {
  const [tabValue, setTabValue] = useState(0);
  const [localField, setLocalField] = useState<any>(null);

  useEffect(() => {
    if (field && databaseField) {
      // Initialize local field with current field values or database defaults
      const initializedField = { ...field };
      
      // For each setting in the database field, ensure it exists in the local field
      databaseField.settings.forEach(setting => {
        if (!initializedField[setting.name]) {
          initializedField[setting.name] = [...setting.data];
        }
      });
      
      setLocalField(initializedField);
    }
  }, [field, databaseField]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  const handleSettingChange = (settingName: string, value: any) => {
    if (localField) {
      setLocalField({
        ...localField,
        [settingName]: value
      });
    }
  };

  const handleSave = () => {
    if (localField) {
      onSave(localField);
    }
    onClose();
  };

  if (!field || !databaseField || !localField) {
    return null;
  }

  // Group settings by component type
  const settingsByType: Record<string, any[]> = {};
  databaseField.settings.forEach(setting => {
    const componentId = setting.gui.component_id;
    if (!settingsByType[componentId]) {
      settingsByType[componentId] = [];
    }
    settingsByType[componentId].push(setting);
  });

  // Create tabs based on available setting types
  const tabs = Object.keys(settingsByType);

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      fullWidth
      maxWidth="md"
    >
      <DialogTitle>
        Edit Field Settings: {field.caption}
      </DialogTitle>
      <DialogContent>
        <Box sx={{ mb: 2 }}>
          <Typography variant="subtitle2" color="text.secondary">
            Field: {field.logprefix ? `${field.logprefix}:` : ''}{field.field}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Precision: {databaseField.precision}
          </Typography>
        </Box>
        
        <Divider sx={{ mb: 2 }} />
        
        {tabs.length > 1 ? (
          <>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs 
                value={tabValue} 
                onChange={handleTabChange}
                aria-label="field settings tabs"
              >
                {tabs.map((tab, index) => {
                  const label = tab === 'logsetting_cols' ? 'Column Names' :
                               tab === 'logsetting_cols_ps' ? 'Columns with Prefix/Suffix' :
                               tab === 'logsetting_ecc_FRD' ? 'Excentricity' :
                               tab === 'logsetting_ascii_format' ? 'ASCII Format' : tab;
                  
                  return (
                    <Tab key={tab} label={label} id={`field-settings-tab-${index}`} />
                  );
                })}
              </Tabs>
            </Box>
            
            {tabs.map((tab, index) => (
              <TabPanel key={tab} value={tabValue} index={index}>
                {renderSettingComponent(tab, settingsByType[tab][0], localField, handleSettingChange)}
              </TabPanel>
            ))}
          </>
        ) : (
          // If only one setting type, don't use tabs
          <Box sx={{ p: 1 }}>
            {tabs.map(tab => 
              renderSettingComponent(tab, settingsByType[tab][0], localField, handleSettingChange)
            )}
          </Box>
        )}
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose}>Cancel</Button>
        <Button onClick={handleSave} variant="contained">
          Save Changes
        </Button>
      </DialogActions>
    </Dialog>
  );
}

// Helper function to render the appropriate setting component
function renderSettingComponent(
  componentId: string, 
  setting: any, 
  field: any, 
  onChange: (name: string, value: any) => void
) {
  switch (componentId) {
    case 'logsetting_cols':
      return (
        <LogSettingCols
          value={field[setting.name] || [...setting.data]}
          onChange={(value) => onChange(setting.name, value)}
          name={setting.name}
          gui={{ 
            label: 'Column Names',
            tooltip: 'Define the column names for this field in the CSV output'
          }}
        />
      );
    case 'logsetting_cols_ps':
      return (
        <LogSettingColsPS
          value={field[setting.name] || [...setting.data]}
          onChange={(value) => onChange(setting.name, value)}
          name={setting.name}
          gui={{ 
            label: 'Column Names with Prefix/Suffix',
            tooltip: 'Define prefix, suffix, and column names for this field in the CSV output'
          }}
        />
      );
    case 'logsetting_ecc_FRD':
      return (
        <LogSettingEccFRD
          value={field[setting.name] || [...setting.data]}
          onChange={(value) => onChange(setting.name, value)}
          name={setting.name}
          gui={{ 
            label: 'Excentricity/Lever Arm (Front, Right, Down)',
            tooltip: 'Define the excentricity/lever arm values in meters',
            unit: 'm',
            max_digits: 3
          }}
        />
      );
    case 'logsetting_ascii_format':
      return (
        <LogSettingAsciiFormat
          value={field[setting.name] || [...setting.data]}
          onChange={(value) => onChange(setting.name, value)}
          name={setting.name}
          gui={{ 
            label: 'ASCII Format Strings',
            tooltip: 'Define the C-style format strings for this field in the ASCII output'
          }}
        />
      );
    default:
      return (
        <Typography>
          Unknown setting type: {componentId}
        </Typography>
      );
  }
}
