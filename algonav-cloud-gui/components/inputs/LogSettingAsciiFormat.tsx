// components/inputs/LogSettingAsciiFormat.tsx
import { useState, useEffect } from 'react';
import {
  FormControl,
  FormLabel,
  Stack,
  TextField,
  Tooltip,
  Box,
  Typography
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface LogSettingAsciiFormatProps {
  value: string[];
  onChange: (value: string[]) => void;
  name: string;
  gui: {
    label?: string;
    tooltip?: string;
    max_chars?: number;
    [key: string]: any;
  };
}

export default function LogSettingAsciiFormat({
  value = [],
  onChange,
  name,
  gui,
}: LogSettingAsciiFormatProps) {
  const [localValues, setLocalValues] = useState<string[]>(value);

  useEffect(() => {
    setLocalValues(value);
  }, [value]);

  const handleChange = (index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValues = [...localValues];
    newValues[index] = e.target.value;
    setLocalValues(newValues);
    // Update parent component immediately to ensure changes are tracked
    onChange(newValues);
  };

  // Handle blur event to update parent only when focus leaves the field
  const handleBlur = () => {
    onChange(localValues);
  };

  return (
    <FormControl fullWidth>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel>
            {gui.label || 'ASCII Format Strings'}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}

      <Typography variant="caption" sx={{ mb: 1 }}>
        C-style format strings (e.g., %14.9f, %10.4f, %8d)
      </Typography>

      <Stack spacing={2}>
        {localValues.map((val, index) => (
          <Box key={index} display="flex" alignItems="center">
            <TextField
              fullWidth
              size="small"
              value={val}
              onChange={handleChange(index)}
              onBlur={handleBlur}
              placeholder={`Format ${index + 1}`}
              inputProps={{
                maxLength: gui.max_chars,
              }}
            />
          </Box>
        ))}


      </Stack>
    </FormControl>
  );
}
