// components/inputs/LogSettingColsPS.tsx
import { useState, useEffect } from 'react';
import {
  FormControl,
  FormLabel,
  Stack,
  TextField,
  Tooltip,
  Box,
  Typography,
  Divider
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface LogSettingColsPSProps {
  value: string[];
  onChange: (value: string[]) => void;
  name: string;
  gui: {
    label?: string;
    tooltip?: string;
    max_chars?: number;
    [key: string]: any;
  };
}

export default function LogSettingColsPS({
  value = ['', '', ''],
  onChange,
  name,
  gui,
}: LogSettingColsPSProps) {
  // Ensure we have at least prefix, suffix, and one column
  const initialValues = value.length >= 3 ? value : ['', '', ''];
  const [localValues, setLocalValues] = useState<string[]>(initialValues);

  useEffect(() => {
    // Only update if the value is valid (has at least 3 elements)
    if (value.length >= 3) {
      setLocalValues(value);
    }
  }, [value]);

  const handleChange = (index: number) => (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValues = [...localValues];
    newValues[index] = e.target.value;
    setLocalValues(newValues);
    // Update parent component immediately to ensure changes are tracked
    onChange(newValues);
  };

  // Handle blur event to update parent only when focus leaves the field
  const handleBlur = () => {
    onChange(localValues);
  };

  return (
    <FormControl fullWidth>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel>
            {gui.label}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}

      <Stack spacing={2}>
        {/* Prefix and Suffix */}
        <Box display="flex" gap={2}>
          <TextField
            label="Prefix"
            size="small"
            value={localValues[0]}
            onChange={handleChange(0)}
            onBlur={handleBlur}
            inputProps={{
              maxLength: gui.max_chars,
            }}
            sx={{ flex: 1 }}
          />
          <TextField
            label="Suffix"
            size="small"
            value={localValues[1]}
            onChange={handleChange(1)}
            onBlur={handleBlur}
            inputProps={{
              maxLength: gui.max_chars,
            }}
            sx={{ flex: 1 }}
          />
        </Box>

        <Divider />
        <Typography variant="subtitle2">Column Names</Typography>

        {/* Column names (starting from index 2) */}
        {localValues.slice(2).map((val, idx) => {
          const index = idx + 2; // Actual index in the array
          return (
            <Box key={index} display="flex" alignItems="center">
              <TextField
                fullWidth
                size="small"
                value={val}
                onChange={handleChange(index)}
                onBlur={handleBlur}
                placeholder={`Column ${idx + 1}`}
                inputProps={{
                  maxLength: gui.max_chars,
                }}
              />
            </Box>
          );
        })}


      </Stack>
    </FormControl>
  );
}
