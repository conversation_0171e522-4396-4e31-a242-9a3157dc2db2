// components/inputs/LogTriggerSettings.tsx
import { useState, useEffect } from 'react';
import { 
  FormControl, 
  FormLabel, 
  Stack, 
  TextField, 
  Tooltip, 
  Box,
  RadioGroup,
  FormControlLabel,
  Radio,
  MenuItem,
  Select,
  InputLabel,
  Typography
} from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

interface LogTriggerSettingsProps {
  value: {
    trigger: string;
    dt?: number;
    sensor?: string;
    step?: number;
  };
  onChange: (value: any) => void;
  name: string;
  gui: {
    label?: string;
    tooltip?: string;
    availableSensors?: Array<{
      name: string;
      label: string;
      logprefix: string;
    }>;
    [key: string]: any;
  };
}

export default function LogTriggerSettings({
  value = { trigger: 'RTS_TIME', dt: 0.1 },
  onChange,
  name,
  gui,
}: LogTriggerSettingsProps) {
  const [triggerType, setTriggerType] = useState<string>(value.trigger || 'RTS_TIME');
  const [dt, setDt] = useState<number>(value.dt || 0.1);
  const [sensor, setSensor] = useState<string>(value.sensor || '');
  const [step, setStep] = useState<number>(value.step || 1);

  // Update local state when props change
  useEffect(() => {
    setTriggerType(value.trigger || 'RTS_TIME');
    setDt(value.dt || 0.1);
    setSensor(value.sensor || '');
    setStep(value.step || 1);
  }, [value]);

  // Update parent component when local state changes
  useEffect(() => {
    const newValue = triggerType === 'RTS_TIME'
      ? { trigger: triggerType, dt }
      : { trigger: triggerType, sensor, step };
    
    onChange(newValue);
  }, [triggerType, dt, sensor, step, onChange]);

  const handleTriggerChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setTriggerType(event.target.value);
  };

  const handleDtChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newDt = parseFloat(event.target.value);
    setDt(isNaN(newDt) ? 0.1 : newDt);
  };

  const handleSensorChange = (event: any) => {
    setSensor(event.target.value);
  };

  const handleStepChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const newStep = parseInt(event.target.value, 10);
    setStep(isNaN(newStep) ? 1 : newStep);
  };

  return (
    <FormControl fullWidth>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel>
            {gui.label || 'Log Trigger Settings'}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}
      
      <Stack spacing={3}>
        <FormControl component="fieldset">
          <FormLabel component="legend">Trigger Type</FormLabel>
          <RadioGroup
            value={triggerType}
            onChange={handleTriggerChange}
            row
          >
            <FormControlLabel 
              value="RTS_TIME" 
              control={<Radio />} 
              label="Time-based" 
            />
            <FormControlLabel 
              value="RTS_MEAS" 
              control={<Radio />} 
              label="Sensor-based" 
            />
          </RadioGroup>
        </FormControl>
        
        {triggerType === 'RTS_TIME' ? (
          <FormControl fullWidth>
            <TextField
              label="Log Rate (seconds)"
              type="number"
              value={dt}
              onChange={handleDtChange}
              size="small"
              inputProps={{
                step: 0.01,
                min: 0.01,
              }}
              helperText="Time interval between log entries (in seconds)"
            />
          </FormControl>
        ) : (
          <Stack spacing={2}>
            <FormControl fullWidth>
              <InputLabel id="sensor-select-label">Sensor</InputLabel>
              <Select
                labelId="sensor-select-label"
                value={sensor}
                onChange={handleSensorChange}
                label="Sensor"
                size="small"
              >
                {gui.availableSensors?.map((s) => (
                  <MenuItem key={s.logprefix} value={s.logprefix}>
                    {s.label}
                  </MenuItem>
                )) || (
                  <MenuItem value="" disabled>
                    No sensors available
                  </MenuItem>
                )}
              </Select>
            </FormControl>
            
            <FormControl fullWidth>
              <TextField
                label="Step"
                type="number"
                value={step}
                onChange={handleStepChange}
                size="small"
                inputProps={{
                  step: 1,
                  min: 1,
                }}
                helperText="Log every Nth sensor measurement (1 = every measurement)"
              />
            </FormControl>
          </Stack>
        )}
      </Stack>
    </FormControl>
  );
}
