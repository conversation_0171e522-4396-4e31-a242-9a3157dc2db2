// components/inputs/NumberField.tsx
import { FormControl, FormLabel, Stack, TextField, Tooltip } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { useTemplateStore } from '../../lib/stores/templateStore';
import { useState } from 'react';
import { validateDisplayNumber } from '../../utils/numberValidation';

interface NumberFieldGUIProps {
  value: number;
  onChange: (value: number) => void;
  name: string;
  gui: {
    label?: string;
    min_value?: number;
    max_value?: number;
    max_digits?: number;
    integer?: boolean;
    tooltip?: string;
    unit?: string;
    multiply?: number;
    [key: string]: any;
  };
}

export default function NumberField({
  value,
  onChange,
  name,
  gui,
}: NumberFieldGUIProps) {
  const { setValidationError, getValidationError } = useTemplateStore();
  const [touched, setTouched] = useState(false);
  const error = getValidationError(name);
  const multiplyFactor = gui.multiply || 1;
  const displayValue = isNaN(value) ? '' : value * multiplyFactor;

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const newValue = e.target.value;
    onChange(newValue === '' ? NaN : Number(newValue) / multiplyFactor);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setTouched(true);
    const newValue = e.target.value;
    if (newValue === '') {
      setValidationError(name, 'Required');
      return;
    }
    const num = Number(newValue) / multiplyFactor;
    if (isNaN(num)) {
      setValidationError(name, 'Must be a number');
      return;
    }
    const validationError = validateDisplayNumber(num, gui);
    setValidationError(name, validationError);
  };

  // Step-Attribut anhand des angezeigten Werts berechnen
  const displayStep = gui.integer
    ? (1 * multiplyFactor).toString()
    : gui.max_digits
      ? (Math.pow(10, -gui.max_digits) * multiplyFactor).toString()
      : 'any';

  // Zusammenstellung von Hilfetext-Constraints
  const constraints = [];
  if (gui.min_value !== undefined) constraints.push(`min: ${gui.min_value}`);
  if (gui.max_value !== undefined) constraints.push(`max: ${gui.max_value}`);
  if (!gui.integer && gui.max_digits !== undefined && gui.max_digits > 0) {
    constraints.push(`precision: ${gui.max_digits} decimals`);
  }
  if (gui.integer) constraints.push('integers only');
  const helperText = touched ? (error || constraints.join(', ')) : '';

  const label = gui.unit ? `${gui.label || ''} (${gui.unit})` : gui.label;

  return (
    <FormControl fullWidth error={!!error && touched}>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel>
            {gui.label}
            {gui.unit ? ` (${gui.unit})` : ''}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}
      <TextField
        fullWidth
        type="number"
        value={displayValue}
        onChange={handleChange}
        onBlur={handleBlur}
        error={!!error && touched}
        helperText={helperText}
        inputProps={{
          step: displayStep,
          min: gui.min_value,
          max: gui.max_value,
        }}
        size="small"
      />
    </FormControl>
  );
}
