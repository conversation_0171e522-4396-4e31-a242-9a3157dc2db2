import { FormControl, FormLabel, RadioGroup, FormControlLabel, Radio, Stack, Tooltip } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';

// Deep equality comparison for any data type
function isEqual(a: any, b: any): boolean {
  // Handle null/undefined
  if (a === b) return true;
  if (a === null || b === null) return false;
  if (a === undefined || b === undefined) return false;

  // Handle different types
  if (typeof a !== typeof b) return false;

  // Handle arrays
  if (Array.isArray(a) && Array.isArray(b)) {
    if (a.length !== b.length) return false;
    return a.every((item, index) => isEqual(item, b[index]));
  }

  // Handle objects
  if (typeof a === 'object' && typeof b === 'object') {
    const keysA = Object.keys(a);
    const keysB = Object.keys(b);
    
    if (keysA.length !== keysB.length) return false;
    
    return keysA.every(key => 
      Object.prototype.hasOwnProperty.call(b, key) && 
      isEqual(a[key], b[key])
    );
  }

  // Handle primitives
  return Object.is(a, b);
}

interface RadioItem {
  label: string;
  value: any;  
}

interface RadioButtonsGUIProps {
  value: any;  
  onChange: (value: any) => void;
  name: string;
  gui: {
    label?: string;
    items?: RadioItem[];
    tooltip?: string;
    [key: string]: any;
  };
}

export default function RadioButtons({
  value,
  onChange,
  gui,
}: RadioButtonsGUIProps) {
  const items = gui.items || [];

  // Create a map of stringified values to original values for comparison
  const valueMap = new Map(
    items.map(item => [JSON.stringify(item.value), item.value])
  );

  // Find the stringified value that matches our current value
  const currentStringValue = Array.from(valueMap.entries()).find(
    ([_, val]) => isEqual(val, value)
  )?.[0] ?? '';

  const handleChange = (stringValue: string) => {
    // Convert back to original value type when changing
    const originalValue = valueMap.get(stringValue);
    if (originalValue !== undefined) {
      onChange(originalValue);
    }
  };

  return (
    <FormControl component="fieldset" sx={{ width: '100%' }}>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel component="legend">
            {gui.label}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}
      <RadioGroup
        value={currentStringValue}
        onChange={(e) => handleChange(e.target.value)}
      >
        {items.map((item) => (
          <FormControlLabel
            key={JSON.stringify(item.value)}
            value={JSON.stringify(item.value)}
            control={<Radio />}
            label={item.label}
          />
        ))}
      </RadioGroup>
    </FormControl>
  );
}