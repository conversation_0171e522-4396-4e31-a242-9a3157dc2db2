import { TextField as M<PERSON><PERSON><PERSON>t<PERSON>ield, FormControl, FormLabel, Stack, Tooltip } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import { useTemplateStore } from '../../lib/stores/templateStore';
import { useState } from 'react';

interface TextFieldGUIProps {
  value: string;
  onChange: (value: string) => void;
  name: string;
  gui: {
    label?: string;
    max_chars?: number;
    tooltip?: string;
    [key: string]: any;
  };
}

export default function TextField({
  value,
  onChange,
  name,
  gui,
}: TextFieldGUIProps) {
  const { setValidationError, getValidationError } = useTemplateStore();
  const [touched, setTouched] = useState(false);
  const error = getValidationError(name);

  function validateText(text: string): string | null {
    if (!text) {
      return 'Required';
    }
    if (gui.max_chars !== undefined && text.length > gui.max_chars) {
      return `Maximum ${gui.max_chars} characters allowed`;
    }
    return null;
  }

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
  };

  const handleBlur = (e: React.FocusEvent<HTMLInputElement>) => {
    setTouched(true);
    const newValue = e.target.value;
    const validationError = validateText(newValue);
    setValidationError(name, validationError);
  };

  const constraints = [];
  if (gui.max_chars !== undefined) constraints.push(`${value.length}/${gui.max_chars} characters`);
  
  const helperText = touched ? (error || (constraints.length > 0 ? constraints.join(', ') : undefined)) : '';

  return (
    <FormControl fullWidth error={!!error && touched}>
      {gui.label && (
        <Stack direction="row" spacing={1} alignItems="center" sx={{ mb: 1 }}>
          <FormLabel>
            {gui.label}
            {gui.tooltip && (
              <Tooltip title={<div dangerouslySetInnerHTML={{ __html: gui.tooltip }} />}>
                <HelpOutlineIcon
                  sx={{ ml: 1, fontSize: '1rem', verticalAlign: 'middle' }}
                />
              </Tooltip>
            )}
          </FormLabel>
        </Stack>
      )}
      <MUITextField
        fullWidth
        value={value}
        onChange={handleChange}
        onBlur={handleBlur}
        error={!!error && touched}
        helperText={helperText}
        variant="outlined"
        size="small"
      />
    </FormControl>
  );
}