// components/inputs/logconfig/LogConfigTriggerSettings.tsx
import React, { useCallback } from 'react';
import {
  Box,
  Typography,
  RadioGroup,
  FormControlLabel,
  Radio,
  Slider,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
} from '@mui/material';
import {
  TriggerSettings,
  LogGroup,
} from '../types/LogConfigTypes';

interface LogConfigTriggerSettingsProps {
  triggerSettings: TriggerSettings;
  onChange: (settings: TriggerSettings) => void;
  availableSensors?: LogGroup[];
}

const LogConfigTriggerSettings = React.memo(function LogConfigTriggerSettings({
  triggerSettings,
  onChange,
  availableSensors = [],
}: LogConfigTriggerSettingsProps) {
  // Define scale and unscale functions for the logarithmic slider
  const scaleValue = useCallback((value: number) => Math.log(value), []);
  const unscaleValue = useCallback((value: number) => Math.exp(value), []);

  const handleTriggerChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newTrigger = event.target.value as 'RTS_TIME' | 'RTS_MEAS' | 'RTS_PREDICTOR';
      const newSettings: TriggerSettings = {
        trigger: newTrigger,
        dt: undefined,
        sensor: undefined,
        step: undefined
      };
      if (newTrigger === 'RTS_TIME') {
        newSettings.dt = triggerSettings.dt ?? 0.1;
        newSettings.sensor = undefined;
        newSettings.step = undefined;
      } else if (newTrigger === 'RTS_MEAS') {
        newSettings.dt = undefined;
        newSettings.sensor = triggerSettings.sensor ?? '';
        newSettings.step = triggerSettings.step ?? 1;
      } else if (newTrigger === 'RTS_PREDICTOR') {
        newSettings.dt = undefined;
        newSettings.sensor = undefined;
        newSettings.step = triggerSettings.step ?? 1;
      }
      onChange(newSettings);
    },
    [triggerSettings, onChange],
  );

  const handleDataRateChange = useCallback(
    (_: Event, v: number | number[]) => {
      // v is the scaled value from the slider
      let dt = unscaleValue(typeof v === 'number' ? v : v[0]);
      // Round to avoid floating point issues, e.g., to 4 decimal places
      dt = parseFloat(dt.toFixed(4)); 
      
      // Ensure dt is within the defined min/max bounds after unscaling
      const minDt = 0.01;
      const maxDt = 10;
      if (dt < minDt) dt = minDt;
      if (dt > maxDt) dt = maxDt;

      onChange({ ...triggerSettings, dt });
    },
    [triggerSettings, onChange, unscaleValue],
  );

  const handleStepChange = useCallback(
    (event: React.ChangeEvent<HTMLInputElement>) => {
      const newStep = parseInt(event.target.value, 10) || 1;
      onChange({ ...triggerSettings, step: newStep });
    },
    [triggerSettings, onChange]
  );

  const handleSensorChange = useCallback(
    (event: any) => {
      const newSensor = event.target.value as string;
      onChange({ ...triggerSettings, sensor: newSensor });
    },
    [triggerSettings, onChange]
  );

  return (
    <Box>
      <Typography variant="subtitle1" gutterBottom sx={{ fontWeight: 600 }}>
        Trigger Settings
      </Typography>
      <RadioGroup
        aria-label="trigger-type"
        name="trigger-type"
        value={triggerSettings.trigger}
        onChange={handleTriggerChange}
        row
      >
        <FormControlLabel value="RTS_TIME" control={<Radio />} label="Time-based" />
        <FormControlLabel value="RTS_MEAS" control={<Radio />} label="Sensor-based" />
        <FormControlLabel value="RTS_PREDICTOR" control={<Radio />} label="Predictor-based" />
      </RadioGroup>

      {triggerSettings.trigger === 'RTS_TIME' && (
        <Box sx={{ mt: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1, border: '1px solid', borderColor: 'grey.200' }}>
          <Typography variant="body2" gutterBottom sx={{ fontWeight: 500 }}>
            Data rate for the CSV outputs (one row per epoch):
          </Typography>
          <Box sx={{ px: 1, mt: 2 }}>
            <Slider
              value={scaleValue(triggerSettings.dt ?? 0.1)}
              onChange={handleDataRateChange}
              min={scaleValue(0.01)}
              max={scaleValue(10)}
              step={1e-5}
              scale={scaleValue}
              marks={[
                { value: 0.01, label: '100 Hz' },
                { value: 0.02, label: '50 Hz' },
                { value: 0.05, label: '20 Hz' },
                { value: 0.1, label: '10 Hz' },
                { value: 0.2, label: '5 Hz' },
                { value: 0.5, label: '2 Hz' },
                { value: 1, label: '1 Hz' },
                { value: 2, label: '2 s' },
                { value: 5, label: '5 s' },
                { value: 10, label: '10 s' },
                { value: 30, label: '30 s' },
                { value: 60, label: '60 s' }
              ].map(mark => ({ ...mark, value: scaleValue(mark.value) }))}
              valueLabelFormat={(scaledValue) => {
                const unscaled = unscaleValue(scaledValue);
                if (unscaled >= 1) return `${unscaled.toFixed(1)} s`;
                return `${(1 / unscaled).toFixed(0)} Hz`;
              }}
            />
          </Box>
        </Box>
      )}

      {triggerSettings.trigger === 'RTS_MEAS' && (
        <Box sx={{ mt: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1, border: '1px solid', borderColor: 'grey.200' }}>
          <Box sx={{ display: 'flex', gap: 2 }}>
          <FormControl fullWidth size="small">
            <InputLabel>Sensor</InputLabel>
            <Select
              value={triggerSettings.sensor ?? ''}
              label="Sensor"
              onChange={handleSensorChange}
            >
              {availableSensors?.map((s) => (
                <MenuItem key={s.logprefix} value={s.logprefix}>
                  {s.label}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <TextField
            label="Step"
            type="number"
            size="small"
            value={triggerSettings.step ?? 1}
            sx={{ width: '30%' }}
            InputProps={{ inputProps: { min: 1 } }}
            onChange={handleStepChange}
          />
          </Box>
        </Box>
      )}

      {triggerSettings.trigger === 'RTS_PREDICTOR' && (
        <Box sx={{ mt: 2, p: 2, backgroundColor: 'grey.50', borderRadius: 1, border: '1px solid', borderColor: 'grey.200' }}>
          <TextField
            label="Step"
            type="number"
            size="small"
            fullWidth
            value={triggerSettings.step ?? 1}
            InputProps={{ inputProps: { min: 1 } }}
            onChange={handleStepChange}
          />
        </Box>
      )}
    </Box>
  );
});

// Custom comparison function for better memoization
LogConfigTriggerSettings.displayName = 'LogConfigTriggerSettings';

export default LogConfigTriggerSettings;