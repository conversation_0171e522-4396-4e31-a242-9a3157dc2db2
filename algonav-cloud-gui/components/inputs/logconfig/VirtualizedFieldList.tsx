// components/inputs/logconfig/VirtualizedFieldList.tsx
import React, { memo } from 'react';
import { FixedSizeList as List } from 'react-window';
import AutoSizer from 'react-virtualized-auto-sizer';
import {
  ListItem,
  ListItemText,
  IconButton,
  Box,
} from '@mui/material';
import {
  DraggableProvided,
  Draggable,
} from 'react-beautiful-dnd';
import ArrowForwardIcon from '@mui/icons-material/ArrowForward';
import EditIcon from '@mui/icons-material/Edit';
import DeleteIcon from '@mui/icons-material/Delete';
import DragIndicatorIcon from '@mui/icons-material/DragIndicator';
import {
  LogField,
  DatabaseLogField,
  LogFieldEditHandler,
  LogFieldRemoveHandler,
} from '../types/LogConfigTypes';

interface VirtualizedFieldListProps {
  fields: DatabaseLogField[] | LogField[];
  onFieldAdd?: (field: DatabaseLogField, groupName: string, logprefix: string) => void;
  onFieldEdit?: LogFieldEditHandler;
  onFieldRemove?: LogFieldRemoveHandler;
  selectedFields?: LogField[];
  groupName?: string;
  logprefix?: string;
  isSelected?: boolean;
  itemHeight?: number;
}

interface FieldItemProps {
  index: number;
  style: React.CSSProperties;
  data: {
    fields: DatabaseLogField[] | LogField[];
    onFieldAdd?: (field: DatabaseLogField, groupName: string, logprefix: string) => void;
    onFieldEdit?: LogFieldEditHandler;
    onFieldRemove?: LogFieldRemoveHandler;
    selectedFields?: LogField[];
    groupName?: string;
    logprefix?: string;
    isSelected?: boolean;
  };
}

const FieldItem = memo(({ index, style, data }: FieldItemProps) => {
  const {
    fields,
    onFieldAdd,
    onFieldEdit,
    onFieldRemove,
    selectedFields,
    groupName,
    logprefix,
    isSelected,
  } = data;

  const field = fields[index];
  
  if (isSelected) {
    // Render selected field
    const selectedField = field as LogField;
    return (
      <Draggable
        key={`selected-${selectedField.logprefix || ''}-${selectedField.field}-${index}`}
        draggableId={`selected-${selectedField.logprefix || ''}-${selectedField.field}-${index}`}
        index={index}
      >
        {(provided: DraggableProvided) => (
          <div style={style}>
            <ListItem
              ref={provided.innerRef}
              {...provided.draggableProps}
              divider={index < fields.length - 1}
            >
              <Box
                {...provided.dragHandleProps}
                sx={{
                  flexGrow: 1,
                  cursor: 'grab',
                  display: 'flex',
                  alignItems: 'center'
                }}
              >
                <DragIndicatorIcon
                  fontSize="small"
                  sx={{
                    color: 'text.secondary',
                    mr: 1,
                    opacity: 0.7
                  }}
                />
                <ListItemText
                  primary={selectedField.caption}
                  secondary={
                    <Box component="span">
                      {selectedField.logprefix && (
                        <Box
                          component="span"
                          sx={{ color: 'primary.main', mr: 1, fontSize: '0.75rem' }}
                        >
                          {selectedField.logprefix}:
                        </Box>
                      )}
                      {selectedField.field}
                    </Box>
                  }
                />
              </Box>

              <Box onClick={(e) => e.stopPropagation()}>
                <IconButton
                  size="small"
                  sx={{ mr: 1 }}
                  onClick={() => onFieldEdit?.(selectedField, index)}
                >
                  <EditIcon fontSize="small" />
                </IconButton>
                <IconButton
                  size="small"
                  onClick={() => onFieldRemove?.(index)}
                >
                  <DeleteIcon fontSize="small" />
                </IconButton>
              </Box>
            </ListItem>
          </div>
        )}
      </Draggable>
    );
  } else {
    // Render available field
    const availableField = field as DatabaseLogField;
    const added = selectedFields?.some(
      (sel: LogField) =>
        sel.field === availableField.field && sel.logprefix === logprefix,
    );

    return (
      <Draggable
        key={`${groupName}|${index}|${logprefix}`}
        draggableId={`${groupName}|${index}|${logprefix}`}
        index={index}
      >
        {(provided: DraggableProvided) => (
          <div style={style}>
            <ListItem
              ref={provided.innerRef}
              {...provided.draggableProps}
              {...provided.dragHandleProps}
              secondaryAction={
                <IconButton
                  size="small"
                  edge="end"
                  onClick={() =>
                    onFieldAdd?.(availableField, groupName!, logprefix!)
                  }
                >
                  <ArrowForwardIcon />
                </IconButton>
              }
              sx={{
                cursor: 'grab',
                ...(added && {
                  borderLeft: '4px solid',
                  borderColor: 'success.main',
                  pl: 2,
                  backgroundColor: 'rgba(76,175,80,0.08)',
                })
              }}
            >
              <DragIndicatorIcon
                fontSize="small"
                sx={{
                  color: 'text.secondary',
                  mr: 1,
                  opacity: 0.7
                }}
              />
              <ListItemText
                primary={availableField.caption}
                secondary={availableField.field}
                primaryTypographyProps={{
                  variant: 'body2',
                  color: 'text.primary',
                }}
                secondaryTypographyProps={{
                  variant: 'caption',
                  color: 'text.secondary',
                }}
              />
            </ListItem>
          </div>
        )}
      </Draggable>
    );
  }
});

FieldItem.displayName = 'FieldItem';

export default function VirtualizedFieldList({
  fields,
  onFieldAdd,
  onFieldEdit,
  onFieldRemove,
  selectedFields,
  groupName,
  logprefix,
  isSelected = false,
  itemHeight = 60,
}: VirtualizedFieldListProps) {
  // Only use virtualization for large lists (50+ items)
  if (fields.length < 50) {
    return null; // Let the parent component render normally
  }

  const itemData = {
    fields,
    onFieldAdd,
    onFieldEdit,
    onFieldRemove,
    selectedFields,
    groupName,
    logprefix,
    isSelected,
  };

  return (
    <Box sx={{ height: 400, width: '100%' }}>
      <AutoSizer>
        {({ height, width }) => (
          <List
            height={height}
            width={width}
            itemCount={fields.length}
            itemSize={itemHeight}
            itemData={itemData}
          >
            {FieldItem}
          </List>
        )}
      </AutoSizer>
    </Box>
  );
}