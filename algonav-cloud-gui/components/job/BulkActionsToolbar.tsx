import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Button,
  Divider,
  CircularProgress,
  Box
} from '@mui/material';
import DownloadIcon from '../common/DownloadIcon';
import KmlIcon from '@mui/icons-material/Place';

export type BulkTaskStatus = 'idle' | 'creating' | 'processing' | 'complete' | 'error';

interface BulkActionsToolbarProps {
  selectedIds: number[];
  downloadingFiles: Record<string, boolean>;
  onDownloadSelected: () => void;
  // Bulk job related props
  bulkTaskStatus?: BulkTaskStatus;
  onCreateMultiKML?: () => void;
  bulkTaskError?: string | null;
}

const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({
  selectedIds,
  downloadingFiles,
  onDownloadSelected,
  bulkTaskStatus = 'idle',
  onCreateMultiKML,
  bulkTaskError
}) => {
  const isDownloading = selectedIds.some(id => downloadingFiles[`task-${id}`]);
  const hasSelections = selectedIds.length > 0;

  // Get appropriate icon and text for the Create Multi-KML button based on status
  const getMultiKMLButtonIcon = () => {
    switch(bulkTaskStatus) {
      case 'creating':
      case 'processing':
        return <CircularProgress size={20} />;
      case 'complete':
        return <DownloadIcon fileType="kml" isMulti={true} />;
      case 'error':
        return <KmlIcon color="error" />;
      default:
        return <KmlIcon />;
    }
  };

  const getMultiKMLButtonText = () => {
    switch(bulkTaskStatus) {
      case 'creating':
        return 'Creating...';
      case 'processing':
        return 'Processing...';
      case 'complete':
        return 'Download Multi-KML';
      case 'error':
        return 'Retry Multi-KML';
      default:
        return 'Create Multi-KML';
    }
  };

  const isMultiKMLButtonDisabled = 
    !hasSelections || 
    bulkTaskStatus === 'creating' || 
    bulkTaskStatus === 'processing';

  return (
    <Paper 
      sx={{ 
        mb: 2, 
        p: 1,
        borderRadius: 1,
        display: 'flex', 
        flexDirection: { xs: 'column', sm: 'row' },
        gap: 1,
        alignItems: { xs: 'flex-start', sm: 'center' },
        opacity: hasSelections ? 1 : 0.7,
        bgcolor: hasSelections ? 'action.selected' : 'background.paper',
        transition: 'all 0.2s ease-in-out',
      }}
      elevation={0}
    >
      <Typography variant="body2" sx={{ mr: 1, px: 1 }}>
        {hasSelections 
          ? `${selectedIds.length} ${selectedIds.length === 1 ? 'item' : 'items'} selected` 
          : "Select items to perform bulk actions"}
      </Typography>
      
      <Divider orientation="vertical" flexItem sx={{ display: { xs: 'none', sm: 'block' } }} />
      <Divider sx={{ display: { xs: 'block', sm: 'none' }, width: '100%' }} />
      
      <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
        <Button
          variant="text"
          size="small"
          disabled={!hasSelections}
          onClick={onDownloadSelected}
          startIcon={
            isDownloading ? 
            <CircularProgress size={20} /> : 
            <DownloadIcon fileType="all" isMulti={true} />
          }
        >
          Download
        </Button>
        
        {onCreateMultiKML && (
          <Button
            variant="text"
            size="small"
            disabled={isMultiKMLButtonDisabled}
            onClick={onCreateMultiKML}
            startIcon={getMultiKMLButtonIcon()}
            color={bulkTaskStatus === 'error' ? 'error' : 'primary'}
          >
            {getMultiKMLButtonText()}
          </Button>
        )}
      </Box>
      
      {bulkTaskError && bulkTaskStatus === 'error' && (
        <Typography variant="caption" color="error" sx={{ mt: 1, display: 'block' }}>
          {bulkTaskError}
        </Typography>
      )}
    </Paper>
  );
};

export default BulkActionsToolbar;
