import React from 'react';
import { 
  Box, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Chip,
  OutlinedInput,
  SelectChangeEvent 
} from '@mui/material';

// Define the available status options for tasks
const STATUS_OPTIONS = [
  { value: 'all', label: 'All Statuses' },
  { value: 'queued', label: 'Queued' },
  { value: 'processing', label: 'Processing' },
  { value: 'completed', label: 'Completed' },
  { value: 'success', label: 'Success' },
  { value: 'failed', label: 'Failed' },
  { value: 'error', label: 'Error' }
];

interface StatusFilterProps {
  selectedStatuses: string[];
  onChange: (statuses: string[]) => void;
}

const StatusFilter: React.FC<StatusFilterProps> = ({ selectedStatuses, onChange }) => {
  const handleChange = (event: SelectChangeEvent<string[]>) => {
    const value = event.target.value as string[];
    
    // If "All Statuses" is selected, clear other selections
    if (value.includes('all') && selectedStatuses.length !== 0 && !selectedStatuses.includes('all')) {
      onChange(['all']);
      return;
    }
    
    // If any specific status is selected and "All" was previously selected, remove "All"
    if (value.includes('all') && value.length > 1) {
      onChange(value.filter(status => status !== 'all'));
      return;
    }
    
    // If nothing is selected, default to "All"
    if (value.length === 0) {
      onChange(['all']);
      return;
    }
    
    onChange(value);
  };

  return (
    <FormControl size="small" sx={{ minWidth: 200, width: { xs: '100%', sm: 'auto' } }}>
      <InputLabel id="status-filter-label">Status Filter</InputLabel>
      <Select
        labelId="status-filter-label"
        id="status-filter"
        multiple
        value={selectedStatuses}
        onChange={handleChange}
        input={<OutlinedInput label="Status Filter" />}
        renderValue={(selected) => (
          <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
            {selected.map((value) => {
              const option = STATUS_OPTIONS.find(opt => opt.value === value);
              return (
                <Chip 
                  key={value} 
                  label={option?.label || value} 
                  size="small" 
                />
              );
            })}
          </Box>
        )}
      >
        {STATUS_OPTIONS.map((option) => (
          <MenuItem key={option.value} value={option.value}>
            {option.label}
          </MenuItem>
        ))}
      </Select>
    </FormControl>
  );
};

export default StatusFilter;
