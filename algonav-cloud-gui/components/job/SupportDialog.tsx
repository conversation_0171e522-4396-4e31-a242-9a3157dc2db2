import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Button,
  IconButton,
  FormControl,
  FormLabel,
  RadioGroup,
  FormControlLabel,
  Radio,
  TextField,
  Tabs,
  Tab,
  Box,
  Typography
} from '@mui/material';
import CloseIcon from '@mui/icons-material/Close';
import { SupportReasonType } from '../../types/job';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`support-tabpanel-${index}`}
      aria-labelledby={`support-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 2 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

interface SupportDialogProps {
  open: boolean;
  taskId: number | null;
  reason: string;
  additionalInfo: string;
  supportType: 'dataset' | 'job' | 'general';
  datasetName?: string;
  jobId?: string;
  onClose: () => void;
  onReasonChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onAdditionalInfoChange: (event: React.ChangeEvent<HTMLInputElement>) => void;
  onSubmit: () => void;
}

const SupportDialog: React.FC<SupportDialogProps> = ({
  open,
  taskId,
  reason,
  additionalInfo,
  supportType,
  datasetName,
  jobId,
  onClose,
  onReasonChange,
  onAdditionalInfoChange,
  onSubmit
}) => {
  // Set initial tab based on support type
  const [tabValue, setTabValue] = useState(0);
  
  // Reset tab selection when dialog opens/changes
  useEffect(() => {
    if (supportType === 'dataset') {
      setTabValue(0);
    } else if (supportType === 'job') {
      setTabValue(0); // First tab is job support when in job mode
    } else {
      setTabValue(supportType === 'general' ? 1 : 0); // General will be tab index 1 in job mode
    }
  }, [supportType, open]);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Generate dialog title based on current tab and support type
  const getDialogTitle = () => {
    if (supportType === 'dataset') {
      return tabValue === 0 
        ? `Request Support for Dataset ${datasetName || 'Unknown'}${taskId ? ` (Task #${taskId})` : ''}` 
        : tabValue === 1 ? `Request Support for Job${jobId ? ` #${jobId}` : ''}` : 'General Support Request';
    } else if (supportType === 'job') {
      return tabValue === 0 
        ? `Request Support for Job${jobId ? ` #${jobId}` : ''}`
        : 'General Support Request';
    } else {
      return 'General Support Request';
    }
  };

  return (
    <Dialog 
      open={open} 
      onClose={onClose}
      maxWidth="md"
      fullWidth
      aria-labelledby="support-dialog-title"
    >
      <DialogTitle id="support-dialog-title">
        {getDialogTitle()}
        <IconButton
          aria-label="close"
          onClick={onClose}
          sx={{
            position: 'absolute',
            right: 8,
            top: 8,
          }}
        >
          <CloseIcon />
        </IconButton>
      </DialogTitle>
      <DialogContent dividers>
        {/* Different tab configurations based on support type */}
        {supportType === 'dataset' && (
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="support request tabs"
            variant="fullWidth"
          >
            <Tab label="Task Support" />
            <Tab label="Job Support" />
            <Tab label="General Support" />
          </Tabs>
        )}
        
        {supportType === 'job' && (
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="support request tabs"
            variant="fullWidth"
          >
            <Tab label="Job Support" />
            <Tab label="General Support" />
          </Tabs>
        )}
        
        {supportType === 'general' && (
          <Tabs 
            value={tabValue} 
            onChange={handleTabChange} 
            aria-label="support request tabs"
            variant="fullWidth"
          >
            <Tab label="General Support" />
          </Tabs>
        )}

        {/* Dataset Support Tab Panel - Only shown for dataset support type */}
        {supportType === 'dataset' && (
          <TabPanel value={tabValue} index={0}>
            <DialogContentText gutterBottom>
              Please describe the issue you're experiencing with the selected dataset.
            </DialogContentText>
            
            <FormControl component="fieldset" sx={{ mt: 2, width: '100%' }}>
              <FormLabel component="legend">Issue Type</FormLabel>
              <RadioGroup
                value={reason}
                onChange={onReasonChange}
              >
                <FormControlLabel value="processing_terminated" control={<Radio />} label="The processing has terminated with some error message." />
                <FormControlLabel value="no_output_files" control={<Radio />} label="No output files have been generated." />
                <FormControlLabel value="corrupted_files" control={<Radio />} label="The output files seem corrupted or badly formatted." />
                <FormControlLabel value="incomplete_trajectory" control={<Radio />} label="The trajectory appears to be incomplete." />
                <FormControlLabel value="unexpected_artifacts" control={<Radio />} label="There are one or more unexpected artifacts in the resulting trajectory." />
                <FormControlLabel value="low_accuracy" control={<Radio />} label="Overall, I was expecting a higher accuracy." />
                <FormControlLabel value="bad_reliability" control={<Radio />} label="Bad reliability: The estimated accuracy appears clearly too optimistic." />
                <FormControlLabel value="partially_faulty" control={<Radio />} label="Parts of the trajectory seem to be faulty, e.g. shifted, rugged, or unsteady." />
                <FormControlLabel value="entirely_faulty" control={<Radio />} label="The entire trajectory seems to be faulty, e.g. shifted, rugged, or unsteady." />
              </RadioGroup>
            </FormControl>
          </TabPanel>
        )}

        {/* Job Support Tab Panel - Only shown in job support type */}
        {(supportType === 'dataset' || supportType === 'job') && (
          <TabPanel value={tabValue} index={supportType === 'dataset' ? 1 : 0}>
            <DialogContentText gutterBottom>
              Please describe the issue you're experiencing with this job.
            </DialogContentText>
            
            <FormControl component="fieldset" sx={{ mt: 2, width: '100%' }}>
              <FormLabel component="legend">Issue Type</FormLabel>
              <RadioGroup
                value={reason}
                onChange={onReasonChange}
              >
                <FormControlLabel value="most_failed" control={<Radio />} label="Most/all data sets have failed with some error messages." />
                <FormControlLabel value="empty_files" control={<Radio />} label="The processing seemed ok at first, but I only get empty or corrupted output files." />
                <FormControlLabel value="output_question" control={<Radio />} label="I have a question regarding the output files (format, data rate, contents)." />
                <FormControlLabel value="template_question" control={<Radio />} label="I have a general question regarding some template setting." />
                <FormControlLabel value="parameter_tuning" control={<Radio />} label="The results seem ok - I am wondering, if parameter tuning can improve things further." />
                <FormControlLabel value="sensor_setup" control={<Radio />} label="Can you suggest any extension/optimization of my positioning sensor setup?" />
              </RadioGroup>
            </FormControl>
          </TabPanel>
        )}

        {/* General Support Tab Panel */}
        <TabPanel value={tabValue} index={supportType === 'dataset' ? 2 : (supportType === 'job' ? 1 : 0)}>
          <DialogContentText gutterBottom>
            Please describe your general support request.
          </DialogContentText>
          
          <FormControl component="fieldset" sx={{ mt: 2, width: '100%' }}>
            <FormLabel component="legend">Issue Type</FormLabel>
            <RadioGroup
              value={reason}
              onChange={onReasonChange}
            >
              <FormControlLabel value="account_question" control={<Radio />} label="I have a question regarding my plan or account details." />
              <FormControlLabel value="invoice_question" control={<Radio />} label="I have a question regarding my invoice / payments." />
              <FormControlLabel value="cannot_create_jobs" control={<Radio />} label="I am not able to create new jobs." />
              <FormControlLabel value="new_sensor_config" control={<Radio />} label="I have a new sensor configuration and require the set-up of a new positioning pipeline." />
              <FormControlLabel value="broken_pipeline" control={<Radio />} label="It seems my previously working positioning pipeline is broken somehow." />
            </RadioGroup>
          </FormControl>
        </TabPanel>

        <TextField
          label="Provide more details as required"
          multiline
          rows={4}
          value={additionalInfo}
          onChange={onAdditionalInfoChange}
          fullWidth
          margin="normal"
          variant="outlined"
          placeholder="Additional details about your support request (optional)"
          aria-label="Additional information about the support request"
        />
      </DialogContent>
      <DialogActions>
        <Button onClick={onClose} color="inherit">Cancel</Button>
        <Button 
          onClick={onSubmit} 
          variant="contained" 
          color="primary"
          autoFocus
        >
          Submit Support Request
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default SupportDialog;
