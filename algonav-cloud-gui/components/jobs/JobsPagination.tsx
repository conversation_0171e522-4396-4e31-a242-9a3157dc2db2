import React from 'react';
import { Box, Pagination } from '@mui/material';
import { JobPaginationProps } from '../../types/jobs';

const JobsPagination: React.FC<JobPaginationProps> = ({ 
  totalPages, 
  currentPage, 
  onPageChange 
}) => {
  const handleChange = (_event: React.ChangeEvent<unknown>, value: number) => {
    onPageChange(value);
  };

  return (
    <Box sx={{ 
      display: 'flex', 
      justifyContent: 'center', 
      mt: 3, 
      mb: 3 
    }}>
      <Pagination
        count={totalPages}
        page={currentPage}
        onChange={handleChange}
        color="primary"
        showFirstButton
        showLastButton
        size="medium"
      />
    </Box>
  );
};

export default JobsPagination;
