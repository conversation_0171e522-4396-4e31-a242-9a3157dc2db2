"use client"

import { createClient } from "@/utils/supabase/client"
import { useAuthStore } from "@/lib/stores/authStore"
import { useEffect } from "react"

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const setUser = useAuthStore(state => state.setUser)
  const supabase = createClient()

  useEffect(() => {
    // Initial user fetch
    supabase.auth.getUser().then(({ data: { user }}) => {
      setUser(user)
    })

    // Listen for auth changes
    const { data: { subscription }} = supabase.auth.onAuthStateChange((_event, session) => {
      setUser(session?.user ?? null)
    })

    return () => subscription.unsubscribe()
  }, [])

  return <>{children}</>
}
