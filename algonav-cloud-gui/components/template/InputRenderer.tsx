import dynamic from 'next/dynamic'
import { TemplateVariable } from './types/template'
import { useTemplateStore } from '../../lib/stores/templateStore'
import { Box } from '@mui/material'
import InfoIcon from '@mui/icons-material/Info'
import IMUAxisOrientation from '../inputs/IMUAxisOrientation'
import MultiNumberField from '../inputs/MultiNumberField'

const componentMap: { [key: string]: any } = {
  TextField: dynamic(() => import('../inputs/TextField')),
  NumberField: dynamic(() => import('../inputs/NumberField')),
  Checkbox: dynamic(() => import('../inputs/Checkbox')),
  RadioButtons: dynamic(() => import('../inputs/RadioButtons')),
  CheckboxGroup: dynamic(() => import('../inputs/CheckboxGroup')),
  DiscreteSlider: dynamic(() => import('../inputs/DiscreteSlider')),
  DynamicState: dynamic(() => import('../inputs/DynamicState')),
  IntegerSlider: dynamic(() => import('../inputs/IntegerSlider')),
  IMUAxisOrientation: dynamic(() => import('../inputs/IMUAxisOrientation')),
  MultiNumberField: dynamic(() => import('../inputs/MultiNumberField')),
  // Log configuration components
  LogConfigInput: dynamic(() => import('../inputs/LogConfigInput')),
  LogSettingCols: dynamic(() => import('../inputs/LogSettingCols')),
  LogSettingColsPS: dynamic(() => import('../inputs/LogSettingColsPS')),
  LogSettingEccFRD: dynamic(() => import('../inputs/LogSettingEccFRD')),
  LogSettingAsciiFormat: dynamic(() => import('../inputs/LogSettingAsciiFormat')),
}

interface InputRendererProps {
  variable: TemplateVariable
  template_data?: any
}

export default function InputRenderer({ variable, template_data }: InputRendererProps) {
  const { templateVars, updateVariableData } = useTemplateStore()

  // Early return if no gui or component_id
  if (!variable.gui?.component_id) {
    return null
  }

  // Check if this component depends on another variable
  if (variable.gui?.dependson_var) {
    const dependentVar = templateVars.find(v => v.name === variable.gui?.dependson_var)
    if (!dependentVar) return null // Don't render if dependent variable not found

    // If dependson_val is not set, check if the dependent variable is truthy/falsy
    if (!variable.gui?.dependson_val) {
      if (!dependentVar.data) return null
    }
    // Otherwise check if the value matches the dependson_val array
    else if (!variable.gui.dependson_val.includes(dependentVar.data)) {
      return null
    }
  }

  // Use component_name from the merged GUI component data instead of component_id directly
  const componentName = variable.component_name || variable.gui.component_id
  const Component = componentMap[componentName]

  if (!Component) {
    return <Box>Unknown component type: {componentName}</Box>
  }

  const handleChange = (value: any) => {
    if (!variable.gui) return; // Safety check

    // Handle special cases for checkbox groups
    if (componentName === 'CheckboxGroup') {
      updateVariableData(variable.name, value)
      return
    }

    // Handle special cases for checkbox with complex data
    if (componentName === 'Checkbox' && variable.gui.value_checked !== undefined) {
      value = value ? variable.gui.value_checked : variable.gui.value_unchecked
    }

    updateVariableData(variable.name, value)
  }

  return (
    <Box
      sx={{
        ml: variable.gui?.dependson_var ? 4 : 0,
        mt: variable.gui?.dependson_var ? 3 : 1,
        borderLeft: variable.gui?.dependson_var ? '2px solid #e0e0e0' : 'none',
        pl: variable.gui?.dependson_var ? 2 : 0,
      }}
    >
      {componentName === 'LogConfigInput' ? (
        <Component
          value={variable.data}
          onChange={handleChange}
          name={variable.name}
          gui={variable.gui}
          template_data={template_data}
        />
      ) : (
        <Component
          value={variable.data}
          onChange={handleChange}
          name={variable.name}
          gui={variable.gui}
        />
      )}
    </Box>
  )
}