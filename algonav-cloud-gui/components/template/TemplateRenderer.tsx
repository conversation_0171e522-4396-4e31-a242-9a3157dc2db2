import { useEffect, useRef, forwardRef, useImperativeHandle } from 'react'
import { useMergedTemplateVars } from '../../lib/hooks/useTemplateVars'
import { useTemplateStore } from '../../lib/stores/templateStore'
import ListGroupRenderer, { GroupRendererRef } from './ListGroupRenderer'
import { TemplateVariable } from './types/template'
import { Box, CircularProgress, Alert, Stack, Typography } from '@mui/material'

interface TemplateRendererProps {
  templateId: string;
  templateName?: string;
}

export interface TemplateRendererRef {
  expandGroup: (groupName: string) => void;
  collapseGroup: (groupName: string) => void;
  scrollToGroup: (groupName: string) => void;
}

const TemplateRenderer = forwardRef<TemplateRendererRef, TemplateRendererProps>(
  ({ templateId, templateName }, ref) => {
    const { mergedVars, isLoading, template_data } = useMergedTemplateVars(templateId)
    const { setTemplateVars, templateVars } = useTemplateStore()
    const groupRefs = useRef<{ [key: string]: GroupRendererRef | null }>({})

    useEffect(() => {
      if (mergedVars.length > 0) {
        setTemplateVars(mergedVars)
      }
    }, [mergedVars, setTemplateVars])

    useImperativeHandle(ref, () => ({
      expandGroup: (groupName: string) => {
        const groupRef = groupRefs.current[groupName]
        if (groupRef) {
          groupRef.expandGroup()
        }
      },
      collapseGroup: (groupName: string) => {
        const groupRef = groupRefs.current[groupName]
        if (groupRef) {
          groupRef.collapseGroup()
        }
      },
      scrollToGroup: (groupName: string) => {
        const element = document.getElementById(`group-${groupName.toLowerCase().replace(/\s+/g, '-')}`)
        if (element) {
          // First expand the group
          const groupRef = groupRefs.current[groupName]
          if (groupRef) {
            groupRef.expandGroup()
          }
          // Then scroll to it with a small delay to allow expansion animation
          setTimeout(() => {
            element.scrollIntoView({ behavior: 'smooth', block: 'start' })
          }, 100)
        }
      },
    }))

    if (isLoading) {
      return (
        <Box display="flex" justifyContent="center" p={4}>
          <CircularProgress />
        </Box>
      )
    }

    if (!templateVars.length) {
      return (
        <Alert severity="error">
          Error loading template variables
        </Alert>
      )
    }

    const groupedVariables = templateVars.reduce((groups: { [key: string]: TemplateVariable[] }, variable) => {
      if (!variable.gui?.group) return groups;

      const group = variable.gui.group
      if (!groups[group]) {
        groups[group] = []
      }
      groups[group].push(variable)
      return groups
    }, {})

    // Sort groups by the minimum order in each group
    const sortedGroups = Object.entries(groupedVariables)
      .sort(([groupA, varsA], [groupB, varsB]) => {
        const minOrderA = Math.min(...varsA.map(v => v.gui?.order ?? Infinity))
        const minOrderB = Math.min(...varsB.map(v => v.gui?.order ?? Infinity))
        return minOrderA - minOrderB
      })

    return (
      <Stack spacing={5}>
        {templateName && (
          <Typography variant="h4" component="h2" gutterBottom>
            {templateName}
          </Typography>
        )}
        {sortedGroups.map(([groupName, variables]) => (
          <ListGroupRenderer
            key={groupName}
            ref={(el) => {
              groupRefs.current[groupName] = el
            }}
            groupName={groupName}
            variables={variables}
            template_data={template_data}
          />
        ))}
      </Stack>
    )
  }
)

TemplateRenderer.displayName = 'TemplateRenderer'

export default TemplateRenderer