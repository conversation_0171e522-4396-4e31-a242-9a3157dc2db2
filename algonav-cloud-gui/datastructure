erDiagram
    USERS ||--o{ GLOBAL_JOB_TEMPLATES : creates
    USERS ||--o{ CATEGORIES : owns
    USERS ||--o{ DATASETS : owns
    USERS ||--o{ FILES : uploads
    USERS ||--o{ JOBS : creates

    GLOBAL_JOB_TEMPLATES }o--o{ CATEGORIES : "associated with"
    CATEGORIES ||--o{ CATEGORIES : "parent of"
    CATEGORIES ||--o{ DATASETS : contains
    DATASETS ||--o{ FILES : contains
    GLOBAL_JOB_TEMPLATES ||--o{ JOBS : "used in"
    DATASETS ||--o{ JOBS : "used in"

    USERS {
        string id
        string email
        string name
        datetime created_at
    }

    GLOBAL_JOB_TEMPLATES {
        string id
        string user_id
        string name
        string description
        json template_data
        json variables
    }

    CATEGORIES {
        string id
        string user_id
        string name
        string description
        string parent_category_id
        json variable_overrides
    }

    DATASETS {
        string id
        string user_id
        string category_id
        string name
        string description
        json variable_overrides
    }

    FILES {
        string id
        string user_id
        string dataset_id
        string bucket_name
        string file_path
        string file_name
        int file_size
        string content_type
        datetime created_at
    }

    JOBS {
        string id
        string user_id
        string name
        string description
        string global_job_template_id
        string dataset_id
        json job_json
        string status
        datetime created_at
        datetime updated_at
    }

    CATEGORY_JOB_TEMPLATES {
        string category_id
        string global_job_template_id
    }