SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgsodium" WITH SCHEMA "pgsodium";

CREATE SCHEMA IF NOT EXISTS "testing";

ALTER SCHEMA "testing" OWNER TO "supabase_admin";

CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";

CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";

CREATE OR REPLACE FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
declare
  v_dataset_id uuid;
  v_file_id uuid;
begin
  -- Insert the dataset
  insert into datasets (user_id, name, description, category_id, variable_overrides)
  values (p_user_id, p_name, p_description, p_category_id, p_variable_overrides)
  returning id into v_dataset_id;

  -- Associate files with the dataset
  foreach v_file_id in array p_file_ids loop
    insert into dataset_files (dataset_id, file_id)
    values (v_dataset_id, v_file_id);
  end loop;

  return json_build_object('dataset_id', v_dataset_id);
end;
$$;

ALTER FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "text"[]) OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" integer, "status" "text") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    current_status TEXT;
BEGIN
    -- Try to acquire a lock on the row
    PERFORM pg_advisory_xact_lock(jobid);
    
    -- Check the current status of the job
    SELECT status INTO current_status FROM jobs WHERE id = jobid;
    UPDATE jobs SET status = status WHERE id = jobid;
    IF current_status != status THEN
        -- If the status has not been changed, update it
        UPDATE jobs SET status = status WHERE id = jobid;
        -- Return true to indicate success
        RETURN TRUE;
    ELSE
        -- If the status has already been changed, do not update it
        -- Return false to indicate failure
        RETURN FALSE;
    END IF;
    
EXCEPTION
    WHEN OTHERS THEN
        -- If an error occurred, rollback the transaction
        ROLLBACK;
        -- Return false to indicate failure
        RETURN FALSE;
END;
$$;

ALTER FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" integer, "status" "text") OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    affected_rows TEXT;
BEGIN
    SELECT status INTO affected_rows FROM jobs WHERE id = UUID(jobid) FOR NO KEY UPDATE NOWAIT;
    IF affected_rows != oldstatus THEN
        RETURN FALSE;
    END IF;
    UPDATE jobs SET status = newstatus WHERE id = UUID(jobid);
    RETURN TRUE;
END;
$$;

ALTER FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text") OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text", "table_name" "text" DEFAULT 'jobs'::"text") RETURNS boolean
    LANGUAGE "plpgsql"
    AS $_$
DECLARE
    affected_rows TEXT;
    query TEXT;
BEGIN
    -- Dynamically construct the SELECT query
    query := format('SELECT status FROM %I WHERE id = $1 FOR NO KEY UPDATE NOWAIT', table_name);
    
    -- Execute the dynamic query
    EXECUTE query INTO affected_rows USING UUID(jobid);
    
    IF affected_rows != oldstatus THEN
        RETURN FALSE;
    END IF;
    
    -- Dynamically construct the UPDATE query
    query := format('UPDATE %I SET status = $1 WHERE id = $2', table_name);
    
    -- Execute the dynamic UPDATE query
    EXECUTE query USING newstatus, UUID(jobid);
    
    RETURN TRUE;
EXCEPTION
    WHEN OTHERS THEN
        -- Handle any exceptions (e.g., if the row is locked)
        RETURN FALSE;
END;
$_$;

ALTER FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text", "table_name" "text") OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "public"."sync_job_template_files"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    file_id INTEGER;
BEGIN
    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        -- Verify ownership for each file_id in the template_data
        FOR file_id IN 
            SELECT (jsonb_array_elements_text(elem->'file_ids'))::integer
            FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem
        LOOP
            PERFORM verify_file_ownership(file_id, 'job_template', NEW.id);
        END LOOP;

        -- Delete existing entries and insert new ones
        DELETE FROM testing.job_template_files WHERE job_template_id = NEW.id;
        
        INSERT INTO testing.job_template_files (job_template_id, file_id, file_type)
        SELECT
            NEW.id,
            (jsonb_array_elements_text(elem->'file_ids'))::integer,
            elem->>'name'
        FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem;

        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "public"."sync_job_template_files"() OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "public"."test_storage_policy"("path" "text") RETURNS TABLE("full_path" "text", "folder_name" "text"[], "array_string" "text", "would_allow" boolean)
    LANGUAGE "plpgsql"
    AS $$
BEGIN
  RETURN QUERY SELECT
    path,
    storage.foldername(path),
    array_to_string(storage.foldername(path), ''),
    array_to_string(storage.foldername(path), '') != '';
END;
$$;

ALTER FUNCTION "public"."test_storage_policy"("path" "text") OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "public"."update_dataset_with_files"("p_dataset_id" "uuid", "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "uuid"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
declare
  v_file_id uuid;
begin
  -- Update the dataset
  update datasets
  set name = p_name,
      description = p_description,
      category_id = p_category_id,
      variable_overrides = p_variable_overrides
  where id = p_dataset_id and user_id = p_user_id;

  -- Remove existing file associations
  delete from dataset_files where dataset_id = p_dataset_id;

  -- Add new file associations
  foreach v_file_id in array p_file_ids loop
    insert into dataset_files (dataset_id, file_id)
    values (p_dataset_id, v_file_id);
  end loop;

  return json_build_object('dataset_id', p_dataset_id);
end;
$$;

ALTER FUNCTION "public"."update_dataset_with_files"("p_dataset_id" "uuid", "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "uuid"[]) OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_dataset_id integer;
    v_file_path text;
    v_file_id integer;
    v_files_count integer := 0;
    v_associated_files_count integer := 0;
BEGIN
    -- Insert the dataset
    INSERT INTO testing.datasets (user_id, name, description, category_id, variable_overrides)
    VALUES (p_user_id, p_name, p_description, p_category_id, p_variable_overrides)
    RETURNING id INTO v_dataset_id;

    -- Check if the dataset was actually inserted
    IF v_dataset_id IS NULL THEN
        RAISE EXCEPTION 'Failed to insert dataset';
    END IF;

    RAISE NOTICE 'Dataset created successfully with ID: %', v_dataset_id;

    -- Count the number of files provided
    v_files_count := array_length(p_file_paths, 1);

    -- Associate files with the dataset
    FOREACH v_file_path IN ARRAY p_file_paths LOOP
        -- Get the file ID from the file table, ensuring it belongs to the user
        SELECT id INTO v_file_id
        FROM testing.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO testing.dataset_files (dataset_id, file_id)
            VALUES (v_dataset_id, v_file_id);
            v_associated_files_count := v_associated_files_count + 1;
            RAISE NOTICE 'File associated with dataset. Dataset ID: %, File ID: %', v_dataset_id, v_file_id;
        ELSE
            RAISE WARNING 'File not found or user does not have permission: %', v_file_path;
        END IF;
    END LOOP;

    RAISE NOTICE 'Associated % out of % files with the dataset', v_associated_files_count, v_files_count;

    RETURN json_build_object(
        'dataset_id', v_dataset_id,
        'associated_files_count', v_associated_files_count,
        'total_files_count', v_files_count
    );
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise
        RAISE NOTICE 'Error in create_dataset_with_files: %', SQLERRM;
        RAISE;
END;
$$;

ALTER FUNCTION "testing"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_dataset_id integer;
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_files_count integer := 0;
    v_associated_files_count integer := 0;
BEGIN
    -- Insert the dataset
    INSERT INTO testing.datasets (user_id, name, description, category_id, variable_overrides)
    VALUES (p_user_id, p_name, p_description, p_category_id, p_variable_overrides)
    RETURNING id INTO v_dataset_id;

    -- Count the number of files provided
    v_files_count := array_length(p_file_paths, 1);

    -- Associate files with the dataset
    FOR i IN 1..v_files_count LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Get the file ID from the file table, ensuring it belongs to the user
        SELECT id INTO v_file_id
        FROM testing.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO testing.dataset_files (dataset_id, file_id, file_type)
            VALUES (v_dataset_id, v_file_id, v_file_type);
            v_associated_files_count := v_associated_files_count + 1;
        END IF;
    END LOOP;

    RETURN json_build_object(
        'dataset_id', v_dataset_id,
        'associated_files_count', v_associated_files_count,
        'total_files_count', v_files_count
    );
END;
$$;

ALTER FUNCTION "testing"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."handle_storage_change"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    file_owner_id uuid;
    file_name text;
    folder_name text;
BEGIN
    -- Extract the first folder name from the file path
    folder_name := split_part(NEW.name, '/', 1);
    
    -- Check if the folder name is a valid UUID and exists in the auth.users table
    IF EXISTS (
        SELECT 1 FROM auth.users WHERE id::text = folder_name
    ) THEN
        file_owner_id := folder_name::uuid;
    ELSE
        -- If not a valid user UUID, set to NULL or handle as needed
        file_owner_id := NULL;
    END IF;

    -- Extract the file name from the path
    file_name := split_part(NEW.name, '/', -1);

    IF (TG_OP = 'INSERT') THEN
        INSERT INTO testing.FILES (user_id, bucket_name, file_path, file_name, file_size, content_type)
        VALUES (
            file_owner_id,
            TG_ARGV[0]::text,
            NEW.name,
            file_name,
            COALESCE((NEW.metadata->>'size')::bigint, 0),
            COALESCE(NEW.metadata->>'mimetype', 'application/octet-stream')
        );
    ELSIF (TG_OP = 'DELETE') THEN
        DELETE FROM testing.FILES WHERE bucket_name = TG_ARGV[0]::text AND file_path = OLD.name;
    END IF;

    RETURN NEW;
END;
$$;

ALTER FUNCTION "testing"."handle_storage_change"() OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."remove_json_comments"("commented_json" "text") RETURNS "jsonb"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    json_without_comments TEXT := '';
    in_string BOOLEAN := FALSE;
    string_quote CHAR(1) := NULL;
    escape_char BOOLEAN := FALSE;
    in_comment BOOLEAN := FALSE;
    in_multiline_comment BOOLEAN := FALSE;
    i INTEGER := 1;
    c CHAR(1);
    next_c CHAR(1);
BEGIN
    WHILE i <= length(commented_json) LOOP
        c := substr(commented_json, i, 1);
        next_c := substr(commented_json, i+1, 1);
        
        IF NOT in_string AND NOT in_comment AND NOT in_multiline_comment THEN
            IF c = '/' AND next_c = '/' THEN
                in_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF c = '/' AND next_c = '*' THEN
                in_multiline_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF c IN ('"', '''') THEN
                in_string := TRUE;
                string_quote := c;
            END IF;
        ELSIF in_comment THEN
            IF c = E'\n' THEN
                in_comment := FALSE;
            END IF;
            i := i + 1;
            CONTINUE;
        ELSIF in_multiline_comment THEN
            IF c = '*' AND next_c = '/' THEN
                in_multiline_comment := FALSE;
                i := i + 2;
                CONTINUE;
            END IF;
            i := i + 1;
            CONTINUE;
        ELSIF in_string THEN
            IF c = '\' AND NOT escape_char THEN
                escape_char := TRUE;
            ELSIF c = string_quote AND NOT escape_char THEN
                in_string := FALSE;
                string_quote := NULL;
            ELSE
                escape_char := FALSE;
            END IF;
        END IF;
        
        json_without_comments := json_without_comments || c;
        i := i + 1;
    END LOOP;
    
    RETURN json_without_comments::JSONB;
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Invalid JSON after removing comments. Processed string: %', json_without_comments;
END;
$$;

ALTER FUNCTION "testing"."remove_json_comments"("commented_json" "text") OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."set_job_status_with_check_and_lock"("jobid" integer, "oldstatus" "text", "newstatus" "text", "table_name" "text" DEFAULT 'jobs'::"text") RETURNS "text"
    LANGUAGE "plpgsql"
    AS $_$
DECLARE
    affected_rows TEXT;
    query TEXT;
BEGIN
    -- Dynamically construct the SELECT query
    query := format('SELECT status FROM testing.%I WHERE id = $1 FOR NO KEY UPDATE NOWAIT', table_name);

    -- Execute the dynamic query
    EXECUTE query INTO affected_rows USING jobid;

    IF affected_rows != oldstatus THEN
        RETURN 'Status mismatch';
    END IF;

    -- Dynamically construct the UPDATE query
    query := format('UPDATE testing.%I SET status = $1 WHERE id = $2', table_name);

    -- Execute the dynamic UPDATE query
    EXECUTE query USING newstatus, jobid;

    RETURN 'Success';

EXCEPTION
    WHEN OTHERS THEN
        -- Return the exception message
        RETURN SQLERRM;
END;
$_$;

ALTER FUNCTION "testing"."set_job_status_with_check_and_lock"("jobid" integer, "oldstatus" "text", "newstatus" "text", "table_name" "text") OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."sync_category_files"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    file_id_value INTEGER;
BEGIN
    SET search_path TO testing;
    -- Verify ownership for each file_id in the variable_overrides
    FOR file_id_value IN 
        SELECT (file_id::text)::integer
        FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
             jsonb_array_elements_text(elem->'file_ids') AS file_id
    LOOP
        PERFORM verify_file_ownership(file_id_value, 'category', NEW.id);
    END LOOP;

    DELETE FROM testing.category_files WHERE category_id = NEW.id;
  
    INSERT INTO testing.category_files (category_id, file_id, file_type)
    SELECT 
        NEW.id,
        (file_id::text)::integer,
        jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
    FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
         jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
    RETURN NEW;
END;
$$;

ALTER FUNCTION "testing"."sync_category_files"() OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."sync_dataset_files"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    file_id_value INTEGER;
BEGIN
  SET search_path TO testing;
    -- Verify ownership for each file_id in the variable_overrides
    FOR file_id_value IN 
        SELECT (file_id::text)::integer
        FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
             jsonb_array_elements_text(elem->'file_ids') AS file_id
    LOOP
        PERFORM verify_file_ownership(file_id_value, 'dataset', NEW.id);
    END LOOP;

    -- Delete old entries
    DELETE FROM dataset_files WHERE dataset_id = NEW.id;
  
    -- Insert new entries
    INSERT INTO dataset_files (dataset_id, file_id, file_type)
    SELECT 
        NEW.id,
        (file_id::text)::integer,
        jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
    FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
         jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
    RETURN NEW;
END;
$$;

ALTER FUNCTION "testing"."sync_dataset_files"() OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."sync_job_template_files"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    file_id INTEGER;
BEGIN
    SET search_path TO testing;

    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        -- Verify ownership for each file_id in the template_data
        FOR file_id IN 
            SELECT (jsonb_array_elements_text(elem->'file_ids'))::integer
            FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem
        LOOP
            PERFORM verify_file_ownership(file_id, 'job_template', NEW.id);
        END LOOP;

        -- Delete existing entries and insert new ones
        DELETE FROM testing.job_template_files WHERE job_template_id = NEW.id;
        
        INSERT INTO testing.job_template_files (job_template_id, file_id, file_type)
        SELECT
            NEW.id,
            (jsonb_array_elements_text(elem->'file_ids'))::integer,
            elem->>'name'
        FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem;

        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;

ALTER FUNCTION "testing"."sync_job_template_files"() OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_ids" integer[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_file_id integer;
BEGIN
  -- Update the dataset
  UPDATE testing.datasets
  SET name = p_name,
      description = p_description,
      category_id = p_category_id,
      variable_overrides = p_variable_overrides
  WHERE id = p_dataset_id AND user_id = p_user_id;

  -- Remove existing file associations
  DELETE FROM testing.dataset_files WHERE dataset_id = p_dataset_id;

  -- Add new file associations
  FOREACH v_file_id IN ARRAY p_file_ids LOOP
    INSERT INTO testing.dataset_files (dataset_id, file_id)
    VALUES (p_dataset_id, v_file_id);
  END LOOP;

  RETURN json_build_object('dataset_id', p_dataset_id);
END;
$$;

ALTER FUNCTION "testing"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_ids" integer[]) OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_rows_affected integer;
BEGIN
    -- Update the dataset
    UPDATE testing.datasets
    SET name = p_name,
        description = p_description,
        category_id = p_category_id,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Check if the dataset was actually updated
    IF v_rows_affected = 0 THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    RAISE NOTICE 'Dataset updated successfully. Rows affected: %', v_rows_affected;

    -- Remove existing file associations
    DELETE FROM testing.dataset_files WHERE dataset_id = p_dataset_id;
    
    RAISE NOTICE 'Existing file associations removed for dataset ID: %', p_dataset_id;

    -- Add new file associations
    FOREACH v_file_path IN ARRAY p_file_paths LOOP
        -- Get the file ID from the file table
        SELECT id INTO v_file_id
        FROM testing.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO testing.dataset_files (dataset_id, file_id)
            VALUES (p_dataset_id, v_file_id);
            RAISE NOTICE 'File associated with dataset. Dataset ID: %, File ID: %', p_dataset_id, v_file_id;
        ELSE
            RAISE WARNING 'File not found or user does not have permission: %', v_file_path;
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise
        RAISE NOTICE 'Error in update_dataset_with_files: %', SQLERRM;
        RAISE;
END;
$$;

ALTER FUNCTION "testing"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_rows_affected integer;
    v_dataset_exists boolean;
BEGIN
    -- Check if the dataset exists and belongs to the user
    SELECT EXISTS (
        SELECT 1 
        FROM testing.datasets 
        WHERE id = p_dataset_id AND user_id = p_user_id
    ) INTO v_dataset_exists;

    IF NOT v_dataset_exists THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    -- Update the dataset
    UPDATE testing.datasets
    SET name = p_name,
        description = p_description,
        category_id = p_category_id,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Remove existing file associations
    DELETE FROM testing.dataset_files WHERE dataset_id = p_dataset_id;

    -- Add new file associations
    FOR i IN 1..array_length(p_file_paths, 1) LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Check if the file exists and belongs to the user
        SELECT id INTO v_file_id
        FROM testing.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO testing.dataset_files (dataset_id, file_id, file_type)
            VALUES (p_dataset_id, v_file_id, v_file_type);
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
END;
$$;

ALTER FUNCTION "testing"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."update_template_data"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := testing.remove_json_comments(NEW.commented_json);
    END IF;
    RETURN NEW;
END;
$$;

ALTER FUNCTION "testing"."update_template_data"() OWNER TO "supabase_admin";

CREATE OR REPLACE FUNCTION "testing"."verify_file_ownership"("p_file_id" integer, "p_object_type" "text", "p_object_id" integer) RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    v_file_owner uuid;
    v_object_owner uuid;
BEGIN
    SET search_path TO testing;
    -- Get the owner of the file
    SELECT user_id INTO v_file_owner
    FROM files
    WHERE id = p_file_id;

    -- Get the owner of the object (dataset, category, or job_template)
    CASE p_object_type
        WHEN 'dataset' THEN
            SELECT user_id INTO v_object_owner
            FROM datasets
            WHERE id = p_object_id;
        WHEN 'category' THEN
            SELECT user_id INTO v_object_owner
            FROM categories
            WHERE id = p_object_id;
        WHEN 'job_template' THEN
            SELECT user_id INTO v_object_owner
            FROM global_job_templates
            WHERE id = p_object_id;
    END CASE;
    -- Compare the owners
    
    IF v_file_owner != v_object_owner THEN
        RAISE EXCEPTION 'File (ID: %) does not belong to the owner of the % (ID: %)', 
                        p_file_id, p_object_type, p_object_id;
    END IF;
    
END;
$$;

ALTER FUNCTION "testing"."verify_file_ownership"("p_file_id" integer, "p_object_type" "text", "p_object_id" integer) OWNER TO "supabase_admin";

SET default_tablespace = '';

SET default_table_access_method = "heap";

CREATE TABLE IF NOT EXISTS "public"."broken_files" (
    "id" integer NOT NULL,
    "job_id" "uuid" NOT NULL,
    "email" character varying(255) NOT NULL,
    "created_at" timestamp with time zone DEFAULT CURRENT_TIMESTAMP
);

ALTER TABLE "public"."broken_files" OWNER TO "supabase_admin";

CREATE SEQUENCE IF NOT EXISTS "public"."broken_files_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE "public"."broken_files_id_seq" OWNER TO "supabase_admin";

ALTER SEQUENCE "public"."broken_files_id_seq" OWNED BY "public"."broken_files"."id";

CREATE TABLE IF NOT EXISTS "public"."fileuploader" (
    "id" "text" NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "updated_at" timestamp without time zone DEFAULT "now"(),
    "email" "text",
    "comment" "text",
    "agreed_to_terms" boolean DEFAULT false
);

ALTER TABLE "public"."fileuploader" OWNER TO "supabase_admin";

CREATE TABLE IF NOT EXISTS "public"."jobs" (
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "status" "text",
    "updated_at" timestamp with time zone,
    "json" "json",
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "process_template" bigint
);

ALTER TABLE "public"."jobs" OWNER TO "supabase_admin";

CREATE TABLE IF NOT EXISTS "public"."jobs_test" (
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "status" "text",
    "updated_at" timestamp with time zone,
    "json" "json",
    "id" "uuid" DEFAULT "gen_random_uuid"() NOT NULL,
    "process_template" bigint
);

ALTER TABLE "public"."jobs_test" OWNER TO "supabase_admin";

CREATE TABLE IF NOT EXISTS "public"."process_templates" (
    "id" bigint NOT NULL,
    "created_at" timestamp with time zone DEFAULT "now"() NOT NULL,
    "json" "text"
);

ALTER TABLE "public"."process_templates" OWNER TO "supabase_admin";

ALTER TABLE "public"."process_templates" ALTER COLUMN "id" ADD GENERATED BY DEFAULT AS IDENTITY (
    SEQUENCE NAME "public"."process_templates_id_seq"
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1
);

CREATE TABLE IF NOT EXISTS "testing"."batches" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "status" character varying(50) NOT NULL,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);

ALTER TABLE "testing"."batches" OWNER TO "supabase_admin";

CREATE SEQUENCE IF NOT EXISTS "testing"."batches_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE "testing"."batches_id_seq" OWNER TO "supabase_admin";

ALTER SEQUENCE "testing"."batches_id_seq" OWNED BY "testing"."batches"."id";

CREATE TABLE IF NOT EXISTS "testing"."categories" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "parent_category_id" integer,
    "variable_overrides" "jsonb"
);

ALTER TABLE "testing"."categories" OWNER TO "supabase_admin";

CREATE SEQUENCE IF NOT EXISTS "testing"."categories_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE "testing"."categories_id_seq" OWNER TO "supabase_admin";

ALTER SEQUENCE "testing"."categories_id_seq" OWNED BY "testing"."categories"."id";

CREATE TABLE IF NOT EXISTS "testing"."category_files" (
    "category_id" integer NOT NULL,
    "file_id" integer NOT NULL,
    "file_type" "text"
);

ALTER TABLE "testing"."category_files" OWNER TO "supabase_admin";

CREATE TABLE IF NOT EXISTS "testing"."category_job_templates" (
    "category_id" integer NOT NULL,
    "global_job_template_id" integer NOT NULL
);

ALTER TABLE "testing"."category_job_templates" OWNER TO "supabase_admin";

CREATE TABLE IF NOT EXISTS "testing"."dataset_files" (
    "dataset_id" integer NOT NULL,
    "file_id" integer NOT NULL,
    "file_type" "text"
);

ALTER TABLE "testing"."dataset_files" OWNER TO "supabase_admin";

CREATE TABLE IF NOT EXISTS "testing"."datasets" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "category_id" integer,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "variable_overrides" "jsonb"
);

ALTER TABLE "testing"."datasets" OWNER TO "supabase_admin";

CREATE SEQUENCE IF NOT EXISTS "testing"."datasets_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE "testing"."datasets_id_seq" OWNER TO "supabase_admin";

ALTER SEQUENCE "testing"."datasets_id_seq" OWNED BY "testing"."datasets"."id";

CREATE TABLE IF NOT EXISTS "testing"."files" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "dataset_id" integer,
    "bucket_name" character varying(255) NOT NULL,
    "file_path" character varying(255) NOT NULL,
    "file_name" character varying(255) NOT NULL,
    "file_size" integer,
    "content_type" character varying(255),
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);

ALTER TABLE "testing"."files" OWNER TO "supabase_admin";

CREATE SEQUENCE IF NOT EXISTS "testing"."files_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE "testing"."files_id_seq" OWNER TO "supabase_admin";

ALTER SEQUENCE "testing"."files_id_seq" OWNED BY "testing"."files"."id";

CREATE TABLE IF NOT EXISTS "testing"."global_job_templates" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "template_data" "jsonb",
    "variables" "jsonb",
    "commented_json" "text"
);

ALTER TABLE "testing"."global_job_templates" OWNER TO "supabase_admin";

CREATE SEQUENCE IF NOT EXISTS "testing"."global_job_templates_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE "testing"."global_job_templates_id_seq" OWNER TO "supabase_admin";

ALTER SEQUENCE "testing"."global_job_templates_id_seq" OWNED BY "testing"."global_job_templates"."id";

CREATE TABLE IF NOT EXISTS "testing"."job_template_files" (
    "job_template_id" integer NOT NULL,
    "file_id" integer NOT NULL,
    "file_type" "text"
);

ALTER TABLE "testing"."job_template_files" OWNER TO "supabase_admin";

CREATE TABLE IF NOT EXISTS "testing"."jobs" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "global_job_template_id" integer NOT NULL,
    "dataset_id" integer NOT NULL,
    "job_json" "jsonb",
    "status" character varying(50) NOT NULL,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "batch_id" integer,
    "workervars" "jsonb",
    "vars" "jsonb",
    "result" "jsonb"
);

ALTER TABLE "testing"."jobs" OWNER TO "supabase_admin";

CREATE SEQUENCE IF NOT EXISTS "testing"."jobs_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;

ALTER TABLE "testing"."jobs_id_seq" OWNER TO "supabase_admin";

ALTER SEQUENCE "testing"."jobs_id_seq" OWNED BY "testing"."jobs"."id";

ALTER TABLE ONLY "public"."broken_files" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."broken_files_id_seq"'::"regclass");

ALTER TABLE ONLY "testing"."batches" ALTER COLUMN "id" SET DEFAULT "nextval"('"testing"."batches_id_seq"'::"regclass");

ALTER TABLE ONLY "testing"."categories" ALTER COLUMN "id" SET DEFAULT "nextval"('"testing"."categories_id_seq"'::"regclass");

ALTER TABLE ONLY "testing"."datasets" ALTER COLUMN "id" SET DEFAULT "nextval"('"testing"."datasets_id_seq"'::"regclass");

ALTER TABLE ONLY "testing"."files" ALTER COLUMN "id" SET DEFAULT "nextval"('"testing"."files_id_seq"'::"regclass");

ALTER TABLE ONLY "testing"."global_job_templates" ALTER COLUMN "id" SET DEFAULT "nextval"('"testing"."global_job_templates_id_seq"'::"regclass");

ALTER TABLE ONLY "testing"."jobs" ALTER COLUMN "id" SET DEFAULT "nextval"('"testing"."jobs_id_seq"'::"regclass");

ALTER TABLE ONLY "public"."broken_files"
    ADD CONSTRAINT "broken_files_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."fileuploader"
    ADD CONSTRAINT "fileuploader_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."jobs_test"
    ADD CONSTRAINT "jobs_test_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."process_templates"
    ADD CONSTRAINT "process_templates_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "testing"."batches"
    ADD CONSTRAINT "batches_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "testing"."categories"
    ADD CONSTRAINT "categories_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "testing"."category_files"
    ADD CONSTRAINT "category_files_pkey" PRIMARY KEY ("category_id", "file_id");

ALTER TABLE ONLY "testing"."category_job_templates"
    ADD CONSTRAINT "category_job_templates_pkey" PRIMARY KEY ("category_id", "global_job_template_id");

ALTER TABLE ONLY "testing"."dataset_files"
    ADD CONSTRAINT "dataset_files_pkey" PRIMARY KEY ("dataset_id", "file_id");

ALTER TABLE ONLY "testing"."datasets"
    ADD CONSTRAINT "datasets_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "testing"."files"
    ADD CONSTRAINT "files_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "testing"."global_job_templates"
    ADD CONSTRAINT "global_job_templates_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "testing"."job_template_files"
    ADD CONSTRAINT "job_template_files_pkey" PRIMARY KEY ("job_template_id", "file_id");

ALTER TABLE ONLY "testing"."jobs"
    ADD CONSTRAINT "jobs_pkey" PRIMARY KEY ("id");

CREATE OR REPLACE TRIGGER "sync_category_files_trigger" AFTER INSERT OR UPDATE ON "testing"."categories" FOR EACH ROW EXECUTE FUNCTION "testing"."sync_category_files"();

CREATE OR REPLACE TRIGGER "sync_job_template_files_trigger" AFTER INSERT OR UPDATE ON "testing"."global_job_templates" FOR EACH ROW EXECUTE FUNCTION "testing"."sync_job_template_files"();

CREATE OR REPLACE TRIGGER "update_template_data_trigger" BEFORE INSERT OR UPDATE ON "testing"."global_job_templates" FOR EACH ROW EXECUTE FUNCTION "testing"."update_template_data"();

ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_process_template_fkey" FOREIGN KEY ("process_template") REFERENCES "public"."process_templates"("id");

ALTER TABLE ONLY "public"."jobs_test"
    ADD CONSTRAINT "public_jobs_test_process_template_fkey" FOREIGN KEY ("process_template") REFERENCES "public"."process_templates"("id");

ALTER TABLE ONLY "testing"."batches"
    ADD CONSTRAINT "batches_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE ONLY "testing"."categories"
    ADD CONSTRAINT "categories_parent_category_id_fkey" FOREIGN KEY ("parent_category_id") REFERENCES "testing"."categories"("id");

ALTER TABLE ONLY "testing"."categories"
    ADD CONSTRAINT "categories_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE ONLY "testing"."category_files"
    ADD CONSTRAINT "category_files_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "testing"."categories"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "testing"."category_files"
    ADD CONSTRAINT "category_files_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "testing"."files"("id");

ALTER TABLE ONLY "testing"."category_job_templates"
    ADD CONSTRAINT "category_job_templates_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "testing"."categories"("id");

ALTER TABLE ONLY "testing"."category_job_templates"
    ADD CONSTRAINT "category_job_templates_global_job_template_id_fkey" FOREIGN KEY ("global_job_template_id") REFERENCES "testing"."global_job_templates"("id");

ALTER TABLE ONLY "testing"."dataset_files"
    ADD CONSTRAINT "dataset_files_dataset_id_fkey" FOREIGN KEY ("dataset_id") REFERENCES "testing"."datasets"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "testing"."dataset_files"
    ADD CONSTRAINT "dataset_files_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "testing"."files"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "testing"."datasets"
    ADD CONSTRAINT "datasets_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "testing"."categories"("id");

ALTER TABLE ONLY "testing"."datasets"
    ADD CONSTRAINT "datasets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE ONLY "testing"."files"
    ADD CONSTRAINT "files_dataset_id_fkey" FOREIGN KEY ("dataset_id") REFERENCES "testing"."datasets"("id");

ALTER TABLE ONLY "testing"."files"
    ADD CONSTRAINT "files_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE ONLY "testing"."global_job_templates"
    ADD CONSTRAINT "global_job_templates_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE ONLY "testing"."job_template_files"
    ADD CONSTRAINT "job_template_files_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "testing"."files"("id");

ALTER TABLE ONLY "testing"."job_template_files"
    ADD CONSTRAINT "job_template_files_job_template_id_fkey" FOREIGN KEY ("job_template_id") REFERENCES "testing"."global_job_templates"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "testing"."jobs"
    ADD CONSTRAINT "jobs_batch_id_fkey" FOREIGN KEY ("batch_id") REFERENCES "testing"."batches"("id");

ALTER TABLE ONLY "testing"."jobs"
    ADD CONSTRAINT "jobs_dataset_id_fkey" FOREIGN KEY ("dataset_id") REFERENCES "testing"."datasets"("id");

ALTER TABLE ONLY "testing"."jobs"
    ADD CONSTRAINT "jobs_global_job_template_id_fkey" FOREIGN KEY ("global_job_template_id") REFERENCES "testing"."global_job_templates"("id");

ALTER TABLE ONLY "testing"."jobs"
    ADD CONSTRAINT "jobs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE "public"."broken_files" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."fileuploader" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."jobs" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."jobs_test" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "public"."process_templates" ENABLE ROW LEVEL SECURITY;

ALTER TABLE "testing"."batches" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "batches_policy" ON "testing"."batches" USING (("auth"."uid"() = "user_id"));

ALTER TABLE "testing"."categories" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "categories_policy" ON "testing"."categories" USING (("auth"."uid"() = "user_id"));

ALTER TABLE "testing"."category_files" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "category_files_access_policy" ON "testing"."category_files" USING ((EXISTS ( SELECT 1
   FROM "testing"."categories"
  WHERE (("categories"."id" = "category_files"."category_id") AND ("categories"."user_id" = "auth"."uid"())))));

ALTER TABLE "testing"."category_job_templates" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "category_job_templates_policy" ON "testing"."category_job_templates" USING ((EXISTS ( SELECT 1
   FROM "testing"."categories"
  WHERE (("categories"."id" = "category_job_templates"."category_id") AND ("categories"."user_id" = "auth"."uid"())))));

ALTER TABLE "testing"."dataset_files" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "dataset_files_access_policy" ON "testing"."dataset_files" USING ((EXISTS ( SELECT 1
   FROM ("testing"."datasets" "d"
     JOIN "testing"."files" "f" ON (("f"."id" = "dataset_files"."file_id")))
  WHERE (("d"."id" = "dataset_files"."dataset_id") AND ("d"."user_id" = "auth"."uid"()) AND ("f"."user_id" = "auth"."uid"())))));

ALTER TABLE "testing"."datasets" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "datasets" ON "testing"."datasets" TO "authenticated" USING (("auth"."uid"() = "user_id"));

ALTER TABLE "testing"."files" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "files_policy2" ON "testing"."files" USING (("auth"."uid"() = "user_id"));

ALTER TABLE "testing"."global_job_templates" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "global_job_templates_policy" ON "testing"."global_job_templates" USING (("auth"."uid"() = "user_id"));

ALTER TABLE "testing"."job_template_files" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "job_template_files_access_policy" ON "testing"."job_template_files" USING ((EXISTS ( SELECT 1
   FROM "testing"."global_job_templates"
  WHERE (("global_job_templates"."id" = "job_template_files"."job_template_id") AND ("global_job_templates"."user_id" = "auth"."uid"())))));

ALTER TABLE "testing"."jobs" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "jobs_policy" ON "testing"."jobs" USING (("auth"."uid"() = "user_id"));

GRANT USAGE ON SCHEMA "public" TO "postgres";
GRANT USAGE ON SCHEMA "public" TO "anon";
GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";

GRANT USAGE ON SCHEMA "testing" TO "authenticated";
GRANT USAGE ON SCHEMA "testing" TO "service_role";
GRANT USAGE ON SCHEMA "testing" TO "postgres";

GRANT ALL ON FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "text"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "text"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "text"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "text"[]) TO "service_role";

GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" integer, "status" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" integer, "status" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" integer, "status" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" integer, "status" "text") TO "service_role";

GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text") TO "service_role";

GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text", "table_name" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text", "table_name" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text", "table_name" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" "text", "oldstatus" "text", "newstatus" "text", "table_name" "text") TO "service_role";

GRANT ALL ON FUNCTION "public"."sync_job_template_files"() TO "postgres";
GRANT ALL ON FUNCTION "public"."sync_job_template_files"() TO "anon";
GRANT ALL ON FUNCTION "public"."sync_job_template_files"() TO "authenticated";
GRANT ALL ON FUNCTION "public"."sync_job_template_files"() TO "service_role";

GRANT ALL ON FUNCTION "public"."test_storage_policy"("path" "text") TO "postgres";
GRANT ALL ON FUNCTION "public"."test_storage_policy"("path" "text") TO "anon";
GRANT ALL ON FUNCTION "public"."test_storage_policy"("path" "text") TO "authenticated";
GRANT ALL ON FUNCTION "public"."test_storage_policy"("path" "text") TO "service_role";

GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" "uuid", "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "uuid"[]) TO "postgres";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" "uuid", "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "uuid"[]) TO "anon";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" "uuid", "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "uuid"[]) TO "authenticated";
GRANT ALL ON FUNCTION "public"."update_dataset_with_files"("p_dataset_id" "uuid", "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" "uuid", "p_variable_overrides" "jsonb", "p_file_ids" "uuid"[]) TO "service_role";

GRANT ALL ON TABLE "public"."broken_files" TO "postgres";
GRANT ALL ON TABLE "public"."broken_files" TO "anon";
GRANT ALL ON TABLE "public"."broken_files" TO "authenticated";
GRANT ALL ON TABLE "public"."broken_files" TO "service_role";

GRANT ALL ON SEQUENCE "public"."broken_files_id_seq" TO "postgres";
GRANT ALL ON SEQUENCE "public"."broken_files_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."broken_files_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."broken_files_id_seq" TO "service_role";

GRANT ALL ON TABLE "public"."fileuploader" TO "postgres";
GRANT ALL ON TABLE "public"."fileuploader" TO "anon";
GRANT ALL ON TABLE "public"."fileuploader" TO "authenticated";
GRANT ALL ON TABLE "public"."fileuploader" TO "service_role";

GRANT ALL ON TABLE "public"."jobs" TO "postgres";
GRANT ALL ON TABLE "public"."jobs" TO "anon";
GRANT ALL ON TABLE "public"."jobs" TO "authenticated";
GRANT ALL ON TABLE "public"."jobs" TO "service_role";

GRANT ALL ON TABLE "public"."jobs_test" TO "postgres";
GRANT ALL ON TABLE "public"."jobs_test" TO "anon";
GRANT ALL ON TABLE "public"."jobs_test" TO "authenticated";
GRANT ALL ON TABLE "public"."jobs_test" TO "service_role";

GRANT ALL ON TABLE "public"."process_templates" TO "postgres";
GRANT ALL ON TABLE "public"."process_templates" TO "anon";
GRANT ALL ON TABLE "public"."process_templates" TO "authenticated";
GRANT ALL ON TABLE "public"."process_templates" TO "service_role";

GRANT ALL ON SEQUENCE "public"."process_templates_id_seq" TO "postgres";
GRANT ALL ON SEQUENCE "public"."process_templates_id_seq" TO "anon";
GRANT ALL ON SEQUENCE "public"."process_templates_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."process_templates_id_seq" TO "service_role";

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "testing"."batches" TO "authenticated";
GRANT SELECT ON TABLE "testing"."batches" TO "postgres";

GRANT SELECT,USAGE ON SEQUENCE "testing"."batches_id_seq" TO "authenticated";

GRANT ALL ON TABLE "testing"."categories" TO "authenticated";
GRANT ALL ON TABLE "testing"."categories" TO "service_role";
GRANT SELECT ON TABLE "testing"."categories" TO "postgres";

GRANT ALL ON SEQUENCE "testing"."categories_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "testing"."categories_id_seq" TO "service_role";

GRANT SELECT ON TABLE "testing"."category_files" TO "postgres";

GRANT ALL ON TABLE "testing"."category_job_templates" TO "authenticated";
GRANT ALL ON TABLE "testing"."category_job_templates" TO "service_role";
GRANT SELECT ON TABLE "testing"."category_job_templates" TO "postgres";

GRANT SELECT ON TABLE "testing"."dataset_files" TO PUBLIC;
GRANT SELECT ON TABLE "testing"."dataset_files" TO "postgres";

GRANT ALL ON TABLE "testing"."datasets" TO "authenticated";
GRANT ALL ON TABLE "testing"."datasets" TO "service_role";
GRANT SELECT ON TABLE "testing"."datasets" TO "postgres";

GRANT ALL ON SEQUENCE "testing"."datasets_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "testing"."datasets_id_seq" TO "service_role";

GRANT ALL ON TABLE "testing"."files" TO "authenticated";
GRANT ALL ON TABLE "testing"."files" TO "service_role";
GRANT SELECT ON TABLE "testing"."files" TO "postgres";

GRANT ALL ON SEQUENCE "testing"."files_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "testing"."files_id_seq" TO "service_role";

GRANT ALL ON TABLE "testing"."global_job_templates" TO "authenticated";
GRANT ALL ON TABLE "testing"."global_job_templates" TO "service_role";
GRANT SELECT ON TABLE "testing"."global_job_templates" TO "postgres";

GRANT ALL ON SEQUENCE "testing"."global_job_templates_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "testing"."global_job_templates_id_seq" TO "service_role";

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "testing"."job_template_files" TO PUBLIC;
GRANT SELECT ON TABLE "testing"."job_template_files" TO "postgres";

GRANT ALL ON TABLE "testing"."jobs" TO "authenticated";
GRANT ALL ON TABLE "testing"."jobs" TO "service_role";
GRANT SELECT ON TABLE "testing"."jobs" TO "postgres";

GRANT ALL ON SEQUENCE "testing"."jobs_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "testing"."jobs_id_seq" TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "postgres";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "anon";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "testing" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "testing" GRANT ALL ON SEQUENCES  TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "testing" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "testing" GRANT ALL ON FUNCTIONS  TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "testing" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "testing" GRANT ALL ON TABLES  TO "service_role";

RESET ALL;
