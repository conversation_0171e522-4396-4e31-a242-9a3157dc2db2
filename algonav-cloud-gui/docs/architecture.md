1. Core Architecture Changes
1.1 Job-Task Hierarchy
Job (formerly Batch):
Top-level container for processing tasks
Contains multiple related Tasks
Manages overall processing state and metadata
Example: A Job for processing multiple datasets with the same template
Task (formerly Job):
Individual processing unit
Processed by the core worker
Contains specific dataset and template information
Example: Processing a single dataset with a specific template
1.2 Processing Flow
Code
CopyInsert
User Selection → Job Creation → Task Distribution → Core Processing → Result Aggregation
     ↓               ↓              ↓                    ↓                ↓
Select Data     Create Job      Split into         Process each    Combine Results
& Template      Container       Tasks              Task            (e.g., Lever Arm)
2. Data Structure
2.1 Job Entity
typescript
CopyInsert
interface Job {
    id: string;
    user_id: string;
    name: string;
    description: string;
    status: string;
    created_at: DateTime;
    updated_at: DateTime;
    tasks: Task[];
}
2.2 Task Entity
typescript
CopyInsert
interface Task {
    id: string;
    job_id: string;
    dataset_id: string;
    global_job_template_id: string;
    status: string;
    job_json: JSON;
    vars: VariableContainer;
    workervars: WorkerVariable[];
}
3. Core Processing
3.1 Task Processing
Each Task is processed independently by the core worker
Core worker receives:
Dataset information
Template configuration
Processing variables
Core worker returns:
JSON object with file information
Metadata for database storage
3.2 Result Management
Core (David's responsibility):
Manages output file templates
Adds required meta-information
Returns JSON with file details
Backend:
Stores file information in database
Handles file metadata
Manages file relationships
4. Aggregation Functions
4.1 Lever Arm Calculation
Process:
Individual Tasks process single datasets
Results stored as CSV files
Backend function aggregates results
Calculates mean lever arm values
Configuration:
Template defines aggregation requirements
User can set variables for datasets/campaigns
5. Implementation Guidelines
5.1 Frontend Components
Update Jobs.jsx:
Display Job overview
Show contained Tasks
Manage Job-level actions
Update Job.jsx:
Show Task details
Handle Task-specific operations
Display processing status
5.2 Backend Services
Task Processing:
typescript
CopyInsert
async function processTask(task: Task) {
  // Core worker processes single task
  const result = await coreWorker.process(task);
  // Store results and metadata
  await storeResults(result);
}
Result Aggregation:
typescript
CopyInsert
async function aggregateResults(job: Job) {
  // Collect all task results
  const taskResults = await getAllTaskResults(job.id);
  // Perform aggregation (e.g., lever arm calculation)
  const aggregatedResult = await calculateAggregation(taskResults);
  // Store aggregated result
  await storeAggregatedResult(job.id, aggregatedResult);
}
6. Database Schema Updates
6.1 Jobs Table
sql
CopyInsert
CREATE TABLE jobs (
    id UUID PRIMARY KEY,
    user_id UUID REFERENCES users(id),
    name VARCHAR(255),
    description TEXT,
    status VARCHAR(50),
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
6.2 Tasks Table
sql
CopyInsert
CREATE TABLE tasks (
    id UUID PRIMARY KEY,
    job_id UUID REFERENCES jobs(id),
    dataset_id UUID REFERENCES datasets(id),
    template_id UUID REFERENCES global_job_templates(id),
    status VARCHAR(50),
    job_json JSONB,
    vars JSONB,
    created_at TIMESTAMP DEFAULT NOW(),
    updated_at TIMESTAMP DEFAULT NOW()
);
7. API Endpoints
7.1 Job Management
POST /api/jobs: Create new Job
GET /api/jobs: List all Jobs
GET /api/jobs/:id: Get Job details
GET /api/jobs/:id/tasks: Get Tasks for Job
7.2 Task Operations
GET /api/tasks/:id: Get Task details
GET /api/tasks/:id/results: Get Task results
POST /api/tasks/:id/aggregate: Trigger result aggregation
This documentation provides a comprehensive overview of the new architecture and implementation details. The system is now better structured with clear separation between Jobs and Tasks, making it more maintainable and scalable.