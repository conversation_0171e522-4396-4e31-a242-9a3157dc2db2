# Bulk Job Feature Implementation Plan

## Overview
Implement a new "Bulk Job" feature to perform aggregation tasks on multiple selected tasks within a job. The initial implementation focuses on creating multi-KML files from selected tasks.

## Database Changes

```sql
-- Create enum type for bulk job types
CREATE TYPE bulk_job_type AS ENUM ('multi_kml');

-- Add columns to jobs table
ALTER TABLE jobs 
  ADD COLUMN bulk_job_type bulk_job_type,
  ADD COLUMN parent_job_id UUID REFERENCES jobs(id);

-- Create bulk job tasks relationship table
CREATE TABLE bulk_job_tasks (
    bulk_job_id INTEGER REFERENCES jobs(id) ON DELETE CASCADE,
    task_id INTEGER REFERENCES jobs(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (bulk_job_id, task_id)
);

-- Add index for querying bulk jobs by parent
CREATE INDEX idx_jobs_parent_id ON jobs(parent_job_id);
```

## Architecture

```mermaid
graph TD
    subgraph Frontend
        BAT[BulkActionsToolbar]
        KMB[CreateMultiKMLButton]
        UJH[useJobStatus Hook]
        JS[jobService]
    end
    
    subgraph Backend
        API[API Routes]
        BJH[Bulk Job Handler]
        DB[(Database)]
    end
    
    subgraph ErrorHandling
        EH[Error Handler]
        LOG[Logger]
    end
    
    BAT --> KMB
    KMB --> JS
    JS --> API
    KMB --> UJH
    UJH --> API
    API --> BJH
    BJH --> DB
    API --> EH
    EH --> LOG
```

## Job Creation Flow with Error Handling

```mermaid
sequenceDiagram
    participant User
    participant BAT as BulkActionsToolbar
    participant API as Backend API
    participant TX as Transaction
    participant DB as Database

    User->>BAT: Select multiple tasks
    User->>BAT: Click "Create Multi-KML"
    
    Note over BAT: Validate selected tasks
    
    BAT->>API: POST /api/jobs/bulk/kml
    
    activate TX
    Note over TX: Begin Transaction
    
    API->>DB: Create bulk job
    API->>DB: Create task relationships
    
    alt Success
        Note over TX: Commit Transaction
        API-->>BAT: Return job ID
    else Error
        Note over TX: Rollback Transaction
        API-->>BAT: Return error
        BAT->>User: Show error message
    end
    deactivate TX
    
    alt Job Created Successfully
        loop Every second until complete/error
            BAT->>API: GET /api/jobs/:id/status
            alt Job Complete
                API-->>BAT: Status: complete
                BAT->>API: GET /api/jobs/:id/download
                API-->>BAT: Return file
                BAT->>User: Auto-download file
                Note over BAT: Stop polling
            else Job Error
                API-->>BAT: Status: error
                BAT->>User: Show error message
                Note over BAT: Stop polling
            else Job Processing
                API-->>BAT: Status: processing
                Note over BAT: Continue polling
            end
        end
    end
```

## Implementation Steps

### 1. Frontend Updates

#### BulkActionsToolbar Component
```typescript
type bulkTaskStatus = 'idle' | 'creating' | 'processing' | 'complete' | 'error';

const BulkActionsToolbar: React.FC<BulkActionsToolbarProps> = ({
  selectedIds,
  onCreateMultiKML,
  jobStatus,
  error
}) => {
  // Button state based on jobStatus
  const buttonProps = useMemo(() => ({
    disabled: jobStatus === 'creating' || jobStatus === 'processing',
    startIcon: getButtonIcon(jobStatus),
    color: getButtonColor(jobStatus)
  }), [jobStatus]);

  return (
    <>
      <Button
        {...buttonProps}
        onClick={onCreateMultiKML}
      >
        {getButtonText(jobStatus)}
      </Button>
      
      {error && (
        <ErrorMessage 
          message={error}
          onRetry={onCreateMultiKML} 
        />
      )}
    </>
  );
};
```

#### useJobStatus Hook
```typescript
interface UseJobStatusOptions {
  interval?: number;
  maxRetries?: number;
  backoffFactor?: number;
}

const useJobStatus = (
  jobId: string | null, 
  options: UseJobStatusOptions = {}
) => {
  const {
    interval = 1000,
    maxRetries = 3,
    backoffFactor = 1.5
  } = options;

  const [status, setStatus] = useState<bulkTaskStatus>('idle');
  const [error, setError] = useState<string | null>(null);
  const retryCount = useRef(0);
  const timeoutId = useRef<NodeJS.Timeout>();

  useEffect(() => {
    if (!jobId) return;
    
    const pollStatus = async () => {
      try {
        const jobStatus = await jobService.getJobStatus(jobId);
        
        if (jobStatus === 'complete') {
          setStatus('complete');
          await jobService.downloadJobResult(jobId);
          return; // Stop polling
        }
        
        if (jobStatus === 'error') {
          setStatus('error');
          return; // Stop polling
        }

        // Reset retry count on successful poll
        retryCount.current = 0;
        
        // Schedule next poll with backoff if needed
        const nextInterval = interval * Math.pow(backoffFactor, retryCount.current);
        timeoutId.current = setTimeout(pollStatus, nextInterval);
      } catch (error) {
        retryCount.current++;
        
        if (retryCount.current >= maxRetries) {
          setStatus('error');
          setError('Failed to check job status after multiple retries');
          return; // Stop polling
        }
        
        // Retry with backoff
        const nextInterval = interval * Math.pow(backoffFactor, retryCount.current);
        timeoutId.current = setTimeout(pollStatus, nextInterval);
      }
    };

    pollStatus();
    
    return () => {
      if (timeoutId.current) {
        clearTimeout(timeoutId.current);
      }
    };
  }, [jobId, interval, maxRetries, backoffFactor]);

  return { status, error };
};
```

### 2. Backend API

#### Transaction Handling
```typescript
export async function createBulkKMLJob(taskIds: number[]) {
  // Start transaction
  const trx = await db.transaction();
  
  try {
    // 1. Validate all tasks exist and are in valid state
    const tasks = await validateTasks(taskIds, trx);
    
    // 2. Get template
    const template = await getTemplate('bulk_kml', trx);
    
    // 3. Create job with relationships
    const job = await trx('jobs').insert({
      bulk_job_type: 'multi_kml',
      parent_job_id: tasks[0].job_id,
      status: 'processing',
      // ... other job fields
    }).returning('*');

    // 4. Create bulk job task relationships
    await trx('bulk_job_tasks').insert(
      taskIds.map(taskId => ({
        bulk_job_id: job.id,
        task_id: taskId
      }))
    );

    // 5. Commit transaction
    await trx.commit();
    
    // 6. Return created job
    return job;
  } catch (error) {
    // Rollback on any error
    await trx.rollback();
    throw error;
  }
}

async function validateTasks(taskIds: number[], trx) {
  const tasks = await trx('jobs')
    .whereIn('id', taskIds)
    .select('id', 'status', 'job_id');
    
  // Ensure all tasks exist
  if (tasks.length !== taskIds.length) {
    throw new Error('One or more tasks not found');
  }
  
  // Ensure all tasks are completed successfully
  const invalidTasks = tasks.filter(t => t.status !== 'complete');
  if (invalidTasks.length > 0) {
    throw new Error(`Tasks ${invalidTasks.map(t => t.id).join(', ')} are not completed`);
  }
  
  return tasks;
}
```

### 3. Error Handling and Logging

```typescript
interface ErrorResponse {
  code: string;
  message: string;
  details?: any;
}

class bulkTaskError extends Error {
  constructor(
    message: string,
    public code: string,
    public details?: any
  ) {
    super(message);
    this.name = 'bulkTaskError';
  }
}

function handleError(error: any): ErrorResponse {
  // Log error with context
  logger.error('Bulk job error', {
    error,
    timestamp: new Date(),
    // Add relevant context
  });

  if (error instanceof bulkTaskError) {
    return {
      code: error.code,
      message: error.message,
      details: error.details
    };
  }

  // Handle unexpected errors
  return {
    code: 'INTERNAL_ERROR',
    message: 'An unexpected error occurred'
  };
}
```

### 4. Testing Strategy

#### Unit Tests

1. Job Creation
```typescript
describe('createBulkKMLJob', () => {
  it('creates job and relationships in transaction', async () => {
    const taskIds = [1, 2, 3];
    await expect(createBulkKMLJob(taskIds)).resolves.toBeDefined();
    // Verify job and relationships created
  });

  it('rolls back on error', async () => {
    const taskIds = [1, 2, 3];
    // Mock error in creation
    await expect(createBulkKMLJob(taskIds)).rejects.toThrow();
    // Verify nothing was created
  });

  it('validates task status', async () => {
    const taskIds = [1]; // Task in error state
    await expect(createBulkKMLJob(taskIds)).rejects.toThrow(/not completed/);
  });
});
```

2. Status Polling
```typescript
describe('useJobStatus', () => {
  it('polls until complete', async () => {
    // Mock API responses
    const { result } = renderHook(() => useJobStatus('job-1'));
    
    // Verify initial state
    expect(result.current.status).toBe('idle');
    
    // Fast-forward timers
    await act(async () => {
      jest.advanceTimersByTime(1000);
    });
    
    // Verify final state
    expect(result.current.status).toBe('complete');
  });

  it('handles errors with retry', async () => {
    // Mock failed API calls
    const { result } = renderHook(() => useJobStatus('job-1'));
    
    // Verify retries and eventual error
    expect(result.current.status).toBe('error');
    expect(result.current.error).toBeDefined();
  });
});
```

#### Integration Tests

1. Complete Workflow
```typescript
describe('Bulk KML Creation', () => {
  it('creates and processes bulk job', async () => {
    // 1. Set up test data
    const tasks = await createTestTasks();
    
    // 2. Select tasks and trigger bulk job
    const job = await createBulkKMLJob(tasks.map(t => t.id));
    
    // 3. Wait for processing
    const result = await waitForJobCompletion(job.id);
    
    // 4. Verify results
    expect(result.status).toBe('complete');
    expect(result.output).toBeDefined();
  });
});
```

2. Error Scenarios
```typescript
describe('Bulk Job Error Handling', () => {
  it('handles invalid task selection', async () => {
    const tasks = [
      { id: 1, status: 'complete' },
      { id: 2, status: 'error' }
    ];
    
    await expect(createBulkKMLJob(tasks.map(t => t.id)))
      .rejects
      .toThrow();
  });

  it('handles processing failures', async () => {
    // Mock processing error
    const job = await createBulkKMLJob([1]);
    
    const result = await waitForJobCompletion(job.id);
    expect(result.status).toBe('error');
  });
});
```

#### Performance Tests

1. Large Bulk Jobs
```typescript
describe('Bulk Job Performance', () => {
  it('handles large number of tasks', async () => {
    const tasks = await createManyTestTasks(100);
    
    const startTime = Date.now();
    const job = await createBulkKMLJob(tasks.map(t => t.id));
    
    // Verify creation time
    expect(Date.now() - startTime).toBeLessThan(1000);
    
    // Verify processing completes
    const result = await waitForJobCompletion(job.id);
    expect(result.status).toBe('complete');
  });
});
```

## Notes

- All database operations are transactional
- Status polling uses exponential backoff
- Errors are properly logged and propagated
- UI provides clear feedback for all states
- Performance considerations for large jobs
- Robust error handling throughout the flow