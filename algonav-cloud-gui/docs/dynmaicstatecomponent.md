
---

## 1. <PERSON><PERSON><PERSON> der DynamicState-Komponente

Die _DynamicState_-Komponente fasst sämtliche Parameter zusammen, die im Kalman-Filter als einzelne oder mehrdimensionale States auftreten. Typische Beispiele:

- **Hebelarm** (3D oder 6D),
- **IMU-Bias** (Accelerometer, Gyro),
- **Orientierungswinkel** (Roll/Pitch/Yaw),
- **Skalenfaktoren** etc.

<PERSON><PERSON> dieser States kann **verschiedene Modi** haben (fix, Consider-Unsicherheit, Konstante Schätzung, zeitveränderliche Schätzung). Statt für jeden State eine eigene GUI zu bauen, steuert die DynamicState-Komponente das alles in _einer_ UI.

---

## 2. Haupt-<PERSON><PERSON> und `estax`-Verwendung

Ursprünglich standen vier Haupt-Modi im Vordergrund. **Neu** ist, dass **Consider** auch ein teilweises „Wirksamwerden“ pro Achse erlaubt. Das heißt, **auch beim Consider-<PERSON>** kann man `estax` nutzen, um nur bestimmte Achsen mit einer Unsicherheit zu belegen.

Im Einzelnen:

| **Modus**               | **Bedeutung**                                                             | **`estax`**                                           | **`processes.type`** |
| ----------------------- | ------------------------------------------------------------------------- | ----------------------------------------------------- | -------------------- |
| **CONST**               | State fest und exakt bekannt (keine Unsicherheit)                         | leer = [] → keine Achse aktiv                         | `"CONST"`            |
| **CONSIDER**            | State nicht geschätzt, aber mit Unsicherheit (Consider)                   | Achsen, die die Unsicherheit erhalten (z. B. `[0,2]`) | `"CONSIDER"`         |
| **RC** (RandomConstant) | State ist konstant, soll aber geschätzt werden (anfängliche Unsicherheit) | Achsen, die geschätzt werden                          | `"RC"`               |
| **RW** (RandomWalk)     | State zeitveränderlich → Schätzung mit Drift (Random Walk)                | Achsen, die geschätzt werden                          | `"RW"`               |

**Wichtig**:

- Bei **CONSIDER** kann `estax` auch teilweise Achsen enthalten. Nur diese Achsen sind dann _considered_ (mit Unsicherheit).
- Bei **CONST** ist `estax` leer, d. h. alles fix.
- Bei **RC** bzw. **RW** wählt man Achsen, die tatsächlich geschätzt werden.

---

## 3. JSON-Struktur (Data)

Im `data`-Feld wird (unabhängig von der GUI) hinterlegt, wie genau der State vom Kern gehandhabt wird:

```jsonc
{
  "name"    : "XYZ_BIAS",
  "init"    : [0.0, 0.0, 0.0],
  "std0"    : [0.01],
  "estax"   : [0,1], 
  "processes": {
    "type"   : "RW",
    "sqrt_q" : 1e-5
  }
}
```

- **`init`**: Anfangswerte (oder fixierte Werte bei CONST) für alle Achsen. Kann 3D, 6D etc. sein.
- **`std0`**: Anfangsunsicherheit (Consider-/Schätzungs-Unsicherheit)
    - **Kann** ein Array einer Länge = 1 (z. B. `[0.1]`) sein, wenn man für alle aktiven Achsen den gleichen Wert nimmt.
    - **Kann** aber auch ein Array mit so vielen Einträgen sein wie `estax` Achsen, **wenn** man die Unsicherheit je Achse einzeln festlegen will.
- **`estax`**: Indizes der Achsen, die von diesem Modus betroffen sind.
    - Bei **CONST** = `[]` → keine Unsicherheit, keine Schätzung.
    - Bei **CONSIDER** kann `[0,2]` bedeuten: Achse 0 und 2 werden als Consider-Unsicherheit behandelt, Achse 1 nicht.
    - Bei **RC**/**RW**: Achsen, die geschätzt werden.
- **`processes.type`**: `"CONST"`, `"CONSIDER"`, `"RC"`, `"RW"` (oder später z. B. `"GM1"`).
    - Bei **RW** kommt noch `sqrt_q` als zeitlicher Drift-Parameter hinzu.

### Einschränkungen zu `std0` und `sqrt_q`

1. **`std0`** muss **strictly > 0** sein (keine Null, keine negativen Werte), da es eine Standardabweichung darstellt.
2. **`sqrt_q`** ist ebenfalls ≥0\ge 0. Negative Werte wären physikalisch unsinnig. 0 bedeutet: Der Wert soll sich nicht wirklich driftend verändern (also quasi RC), was in der Praxis selten Sinn ergibt, aber formal geht es.
3. **Anzahl der Einträge in `std0`**
    - Entweder **1** Wert, der für alle in `estax` enthaltenen Achsen gilt,
    - oder so viele Werte wie `estax`-Einträge.
    - Falls `init` 6D ist, aber `estax` nur 2 Achsen enthält, kann `std0` also `[0.02, 0.05]` sein, wenn man die zwei Achsen unterschiedlich gewichten will.

---

## 4. JSON-Struktur (GUI)

Im `gui`-Objekt definiert man, **wie** das Eingabe-Formular aussieht. Wichtige Felder:

```jsonc
{
  "component_id": "DynamicState",
  "axes"        : ["X","Y","Z"],
  "visible_axes": [0,1,2],
  
  "unit"        : "m/s²",
  "min_value"   : -1.0, 
  "max_value"   :  1.0,
  "max_digits"  : 3,
  
  // Array: Welche Modi dürfen gewählt werden?
  "process_type": ["CONST","CONSIDER","RC","RW"],

  "label"       : "Accelerometer Bias",
  "tooltip"     : "...",
  "order"       : 99,
  "group"       : "IMU"
}
```

- **`component_id = "DynamicState"`**: Kennzeichnet die Komponente.
- **`axes`**: Beschriftungen pro Achse (Anzahl = Dimension, z. B. 6D).
- **`visible_axes`**: Falls nur ein Ausschnitt der Achsen angezeigt werden soll (z. B. `[2,3,4,5]` aus 6).
- **`process_type`**: _Array_ von erlaubten Modi (z. B. `["CONST","CONSIDER","RC"]`).
    - Der tatsächlich ausgewählte Typ ist dann in `data.processes.type` hinterlegt. Die GUI zeigt also Radio Buttons (oder Dropdown) für alle _erlaubten_ Typen.
- **`unit`, `min_value`, `max_value`, `max_digits`**: Steuern die numerische Eingabe der `init`-Werte.
- Evtl. **`multiply`** für Einheitenumrechnung.
- **`min_value_sqrt_q`** ist in der neuesten Diskussion nicht nötig (Minimum = 0).
- **`std0`** und **`sqrt_q`** werden stets > 0 verlangt (in der GUI müssen negative oder null-Werte abgefangen werden).

---

## 5. GUI-Logik im Detail

1. **Radio Buttons / Modus-Auswahl**
    
    - Basiert auf `process_type` (z. B. 3 oder 4 Auswahlmöglichkeiten).
    - Bei Wechsel ändert sich das Verhalten der restlichen Felder (z. B. wann man `std0` oder `sqrt_q` sieht).
2. **Achsen-Auswahl**
    
    - Falls Modus **CONST** gewählt → `estax = []` (nichts geschätzt, auch keine Consider-Unsicherheit).
    - Falls Modus **CONSIDER** → Hier kann der User _optional_ Häkchen bei einzelnen Achsen setzen, um nur für diese `std0` zu vergeben.
    - Falls Modus **RC** oder **RW** → Checkboxes für zu schätzende Achsen.
    - Das Ergebnis in `data.estax` ist ein Array der aktivierten Achsen.
3. **Unsicherheit**
    
    - **`std0`** wird eingeblendet bei allen Modi außer CONST.
    - `std0` > 0, kann _einen_ Wert enthalten (der für alle Achsen in `estax` oder consider-„Achsen“ gilt) oder soviele Werte wie Achsen.
    - GUI-seitig kann man z. B. einen Umschalter haben: „Apply same std0 to all selected Achsen“ vs. „Set a different std0 per Achse“.
4. **Random Walk**
    
    - Wenn **RW** gewählt, zusätzlich Eingabefeld für `sqrt_q` > 0.

---

## 6. Beispiel für Consider-State mit Teil-Achsen

Angenommen man hat einen 3D-Hebelarm, weiß Achse 0 und Achse 2 mit Unsicherheit, Achse 1 sei exakt:

```jsonc
"data": {
  "init": [0.2, 0.1, -0.3],
  "std0": [0.02, 0.04],  // Zwei Einträge, weil wir Achsen 0 und 2 definieren wollen
  "estax": [0, 2],
  "processes": { "type": "CONSIDER" }
}
```

- Achse 0 → Unsicherheit 0.02 m,
- Achse 1 → gar nicht in `estax`, also 0.0 Unsicherheit (im Sinne von: kein Consider),
- Achse 2 → Unsicherheit 0.04 m.

---

## 7. Fazit

Mit diesen Anpassungen berücksichtigt die DynamicState-Komponente nun:

1. **Consider-State** kann **partiell** Achsen haben (`estax` ist also auch bei Consider relevant).
2. **`process_type`** im GUI (`.json.gui`) wird als Array von erlaubten Modi gehalten – in `data.processes.type` steht dann der tatsächlich gewählte Modus.
3. **Keine negativen oder Nullwerte** für `std0`, keine negativen Werte für `sqrt_q` (Minimalwert = 0).
4. **`std0`** kann 1-Wert-Array oder so viele Werte wie `estax`.

So lassen sich alle gewünschten Konfigurationsfälle abdecken.