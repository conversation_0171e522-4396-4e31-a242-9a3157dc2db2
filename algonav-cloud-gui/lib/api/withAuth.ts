import { NextResponse } from 'next/server';
import { createClient } from "@/utils/supabase/server";

type RouteHandler = (
    userId: string, 
    request: Request, 
    context: { params: Record<string, string | string[]> }
) => Promise<NextResponse>;

export function withAuth(handler: RouteHand<PERSON>) {
    return async function(
        request: Request,
        context: { params: Record<string, string | string[]> }
    ) {
        try {
            const supabase = createClient();
            const { data: { user } } = await supabase.auth.getUser();
            
            if (!user?.id) {
                return NextResponse.json({ error: 'Unauthorized' }, { status: 401 });
            }

            return handler(user.id, request, context);
        } catch (error) {
            return NextResponse.json(
                { error: 'Internal Server Error' }, 
                { status: 500 }
            );
        }
    }
}