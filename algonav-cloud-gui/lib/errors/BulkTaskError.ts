export class bulkTaskError extends Error {
  constructor(
    public code: string,
    message: string,
    public details?: any
  ) {
    super(message);
    this.name = 'bulkTaskError';
  }

  static unknown(error: any): bulkTaskError {
    return new bulkTaskError(
      'UNKNOWN_ERROR',
      error instanceof Error ? error.message : 'An unknown error occurred',
      error
    );
  }
}

export function isbulkTaskError(error: any): error is bulkTaskError {
  return error instanceof bulkTaskError || 
         (error && error.name === 'bulkTaskError') ||
         (error && typeof error.code === 'string' && typeof error.message === 'string');
}
