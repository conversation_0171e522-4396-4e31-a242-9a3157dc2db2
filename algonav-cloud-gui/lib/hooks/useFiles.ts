import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query'
import { api } from '../services/api'

export function useFiles() {
  return useQuery({
      queryKey: ['files'],
      queryFn: api.getFiles,
      retry: 2,
  })
}

export function useDeleteFile() {
  const queryClient = useQueryClient()

  return useMutation({
      mutationFn: api.deleteFile,
      // Optimistic update
      onMutate: async (deletedPath) => {
          await queryClient.cancelQueries({ queryKey: ['files'] })
          const previousFiles = queryClient.getQueryData(['files'])

          queryClient.setQueryData(['files'], (old: any) => ({
              ...old,
              files: old.files.filter((file: any) => file.fullPath !== deletedPath)
          }))

          return { previousFiles }
      },
      onError: (err, _, context) => {
          if (context?.previousFiles) {
              queryClient.setQueryData(['files'], context.previousFiles)
          }
          return err
      },
      onSettled: () => {
          queryClient.invalidateQueries({ queryKey: ['files'] })
      }
  })
}