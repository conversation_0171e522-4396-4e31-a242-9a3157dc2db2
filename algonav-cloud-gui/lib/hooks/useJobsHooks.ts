import { useState, useEffect, useCallback } from 'react';
import { JobItem } from '../../types/jobs';
import { 
  downloadBatchFilesByType, 
  downloadAllBatchFiles 
} from '../../services/jobService';

/**
 * Custom hook for managing job list state (filtering, sorting)
 */
export const useJobsState = (initialJobList: JobItem[]) => { // Renamed initialJobs to initialJobList
  const [jobList, setJobList] = useState<JobItem[]>(initialJobList); // Renamed jobs to jobList
  const [searchTerm, setSearchTerm] = useState('');
  const [orderBy, setOrderBy] = useState<string>(() => {
    return localStorage.getItem('jobsListOrderBy') || 'created_at';
  });
  const [order, setOrder] = useState<'asc' | 'desc'>(() => {
    return (localStorage.getItem('jobsListOrder') as 'asc' | 'desc') || 'desc';
  });

  // Update jobList when initialJobList changes
  useEffect(() => {
    setJobList(initialJobList); // Renamed setJobs to setJobList, initialJobs to initialJobList
  }, [initialJobList]);

  // Save sorting preferences to localStorage
  useEffect(() => {
    localStorage.setItem('jobsListOrder', order);
    localStorage.setItem('jobsListOrderBy', orderBy);
  }, [order, orderBy]);

  // Handle sort column click
  const handleSort = useCallback((property: string) => {
    const isAsc = orderBy === property && order === 'asc';
    const newOrder = isAsc ? 'desc' : 'asc';
    setOrder(newOrder);
    setOrderBy(property);

    const sortedJobList = [...jobList].sort((a, b) => { // Renamed jobs to jobList
      let aValue = a[property as keyof JobItem];
      let bValue = b[property as keyof JobItem];

      if (property === 'global_job_template') {
        aValue = a.jobs[0]?.global_job_template?.name || '';
        bValue = b.jobs[0]?.global_job_template?.name || '';
      }

      if (!aValue) return 1;
      if (!bValue) return -1;

      const comparison = String(aValue).localeCompare(String(bValue));
      return newOrder === 'asc' ? comparison : -comparison;
    });

    setJobList(sortedJobList); // Renamed setJobs to setJobList
  }, [jobList, orderBy, order]); // Renamed jobs to jobList

  // Handle search input change
  const handleSearch = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const searchTerm = event.target.value.toLowerCase();
    setSearchTerm(searchTerm);

    if (!searchTerm.trim()) {
      setJobList(initialJobList); // Renamed setJobs to setJobList, initialJobs to initialJobList
      return;
    }

    const filteredJobList = initialJobList.filter(job => // Renamed batch to job
      job.id.toString().includes(searchTerm) ||
      job.name.toLowerCase().includes(searchTerm) ||
      job.jobs[0]?.global_job_template?.name?.toLowerCase().includes(searchTerm) || // Keep job.jobs as inner structure is unchanged
      job.status.toLowerCase().includes(searchTerm)
    );

    setJobList(filteredJobList); // Renamed setJobs to setJobList
  }, [initialJobList]); // Renamed initialJobs to initialJobList

  return {
    jobList, // Renamed jobs to jobList
    searchTerm,
    orderBy,
    order,
    handleSort,
    handleSearch,
    setJobList // Renamed setJobs to setJobList
  };
};

/**
 * Custom hook for managing download operations
 */
export const useJobsDownload = () => {
  const [downloadingFiles, setDownloadingFiles] = useState<Record<string, boolean>>({});

  // Helper to create a download link
  const createDownloadLink = useCallback((blob: Blob, fileName: string) => {
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.style.display = 'none';
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(a);
  }, []);

  // Download all files of a specific type for a job
  const handleDownloadFileType = useCallback(async (jobId: string, fileType: string) => { // Renamed batchId to jobId
    const key = `${jobId}-${fileType}`; // Use jobId
    setDownloadingFiles(prev => ({ ...prev, [key]: true }));
    
    try {
      const blob = await downloadBatchFilesByType(jobId, fileType); // Pass jobId
      createDownloadLink(blob, `job_${jobId}_${fileType}_files.zip`); // Update filename
    } catch (error) {
      console.error(`Error downloading ${fileType} files:`, error);
      throw error;
    } finally {
      setDownloadingFiles(prev => ({ ...prev, [key]: false }));
    }
  }, [createDownloadLink]);

  // Download all files in the job (formerly batch)
  const handleDownloadAll = useCallback(async (jobId: string) => { // Renamed batchId to jobId
    const key = `${jobId}-all`; // Use jobId
    setDownloadingFiles(prev => ({ ...prev, [key]: true }));
    
    try {
      const blob = await downloadAllBatchFiles(jobId); // Pass jobId
      createDownloadLink(blob, `job_${jobId}_results.zip`); // Update filename
    } catch (error) {
      console.error('Error downloading all files:', error);
      throw error;
    } finally {
      setDownloadingFiles(prev => ({ ...prev, [key]: false }));
    }
  }, [createDownloadLink]);

  return {
    downloadingFiles,
    handleDownloadFileType,
    handleDownloadAll
  };
};
