import { useEffect, useRef } from 'react';

interface PerformanceMetrics {
  componentName: string;
  renderTime: number;
  timestamp: number;
}

/**
 * Hook to monitor component render performance
 * @param componentName - Name of the component being monitored
 * @param enabled - Whether to enable performance monitoring (default: false in production)
 */
export function usePerformanceMonitor(
  componentName: string,
  enabled: boolean = process.env.NODE_ENV === 'development'
) {
  const renderStartTime = useRef<number>(0);
  const renderCount = useRef<number>(0);
  const totalRenderTime = useRef<number>(0);

  useEffect(() => {
    if (!enabled) return;

    renderStartTime.current = performance.now();
    renderCount.current += 1;

    return () => {
      const renderTime = performance.now() - renderStartTime.current;
      totalRenderTime.current += renderTime;
      
      const avgRenderTime = totalRenderTime.current / renderCount.current;

      // Log performance metrics
      console.group(`🚀 Performance Monitor: ${componentName}`);
      console.log(`Render #${renderCount.current}`);
      console.log(`Current render time: ${renderTime.toFixed(2)}ms`);
      console.log(`Average render time: ${avgRenderTime.toFixed(2)}ms`);
      console.log(`Total renders: ${renderCount.current}`);
      console.groupEnd();

      // Warn about slow renders
      if (renderTime > 16) { // 60fps threshold
        console.warn(
          `⚠️ Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms (target: <16ms for 60fps)`
        );
      }
    };
  });

  // Return performance metrics for external use
  return {
    renderCount: renderCount.current,
    averageRenderTime: renderCount.current > 0 ? totalRenderTime.current / renderCount.current : 0,
    totalRenderTime: totalRenderTime.current,
  };
}

/**
 * Hook to measure bundle size impact
 * @param componentName - Name of the component
 * @param dependencies - Array of dependency names to track
 */
export function useBundleAnalyzer(componentName: string, dependencies: string[] = []) {
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.group(`📦 Bundle Analysis: ${componentName}`);
      console.log(`Dependencies: ${dependencies.join(', ')}`);
      console.log(`Component loaded at: ${new Date().toISOString()}`);
      console.groupEnd();
    }
  }, [componentName, dependencies]);
}