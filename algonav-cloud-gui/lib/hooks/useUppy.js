import { useState, useEffect } from 'react';
import Uppy from '@uppy/core';
import XHR from '@uppy/xhr-upload';

export function useUppy(storageEndpoint, onUploadSuccess, authToken) {
  const [uppy, setUppy] = useState(null);

  useEffect(() => {
    const newUppy = new Uppy({
      restrictions: {
        maxNumberOfFiles: 100,
        maxFileSize: 5000 * 1024 * 1024,
      },
    })
    .use(XHR, {
      endpoint: storageEndpoint,
      formData: true,
      fieldName: 'file',
      allowedMetaFields: ['name', 'originalPath'],
      headers: {
        Authorization: `Bearer ${authToken}`
      }
    })
    .on('file-added', (file) => {
      console.log('File added:', file);
      newUppy.setFileMeta(file.id, { 
        name: file.name,
        originalPath: file.meta.relativePath || file.meta.name
      });
    })
    .on('upload-success', (file, response) => {
      console.log('Upload success:', file.name);
      onUploadSuccess();
    });

    setUppy(newUppy);

    return;
  }, [storageEndpoint, onUploadSuccess, authToken]);

  return { uppy };
}