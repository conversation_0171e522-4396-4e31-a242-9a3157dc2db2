# AlgoNav Cloud GUI Project Context

## Project Overview
AlgoNav Cloud GUI is a Next.js web application with Supabase backend that provides a dynamic interface for managing data processing jobs. The system allows users to create and manage processing tasks using a flexible template-based approach.

## Core Components

### 1. Job Processing System
- Hierarchical structure with Jobs (top-level) containing multiple Tasks
- Jobs manage overall processing state and metadata
- Tasks are individual processing units handled by the core worker
- Results tracking through dedicated job_results table

### 2. Dynamic Template System
- Database-driven GUI component system
- Flexible UI generation based on component definitions
- Hierarchical configuration with inheritance support
- Core components:
  - TemplateRenderer: Main template rendering engine
  - InputRenderer: Dynamic component renderer
  - ListGroupRenderer: Groups related inputs

### 3. Data Model

#### Core Tables
1. **jobs**
   - Manages individual processing tasks
   - Links to datasets and templates
   - Tracks status, variables, and results
   - Supports batch processing through batch_id

2. **global_job_templates**
   - Defines processing templates
   - Stores template configuration and variables
   - Supports commented JSON for documentation
   - Links to required files

3. **datasets**
   - Organizes user data
   - Hierarchical categorization
   - Variable overrides for customization
   - File associations through dataset_files

4. **gui_components**
   - Defines UI component types
   - Stores component parameters and validation
   - Enables dynamic interface generation
   - Supports component inheritance

#### Supporting Tables
- categories: Hierarchical data organization
- files: File metadata and storage management
- job_results: Processing output tracking
- Various junction tables for many-to-many relationships

### 4. Technology Stack
- Frontend: Next.js with TypeScript
- Backend: Supabase (PostgreSQL + APIs)
- Authentication: Supabase Auth with RLS policies
- Storage: Supabase Storage with file tracking
- State Management: Custom stores (templateStore)

## Project Goals
1. Provide intuitive interface for managing data processing jobs
2. Enable flexible template-based configuration
3. Support scalable job/task processing architecture
4. Maintain clear separation of concerns between UI and processing logic
5. Ensure secure multi-user environment through RLS

## File Structure Overview
- /app - Next.js application routes and pages
- /components - React components including template system
- /lib - Core libraries, hooks, and stores
- /docs - Project documentation
- /supabase - Database migrations and configuration

## Memory Bank Organization
The Memory Bank will track:
1. Active development context
2. Project progress and milestones
3. Key architectural decisions
4. System patterns and best practices
5. Open questions and issues

## Core Memory Bank Files
- activeContext.md: Current session state and focus
- progress.md: Work completed and next steps
- decisionLog.md: Key architectural and technical decisions
- systemPatterns.md: Identified patterns and practices