# System Patterns and Best Practices

## Database Patterns

### Table Design Pattern
```sql
CREATE TABLE example_table (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL REFERENCES auth.users(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()),
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW())
);
```
- Consistent ID generation
- User ownership tracking
- Automatic timestamp management
- Proper foreign key constraints

### Security Pattern
```sql
-- Enable RLS
ALTER TABLE table_name ENABLE ROW LEVEL SECURITY;

-- User-based access policy
CREATE POLICY "table_policy" ON table_name
    USING (auth.uid() = user_id);

-- Relationship-based access policy
CREATE POLICY "relationship_policy" ON child_table
    USING (EXISTS (
        SELECT 1 FROM parent_table
        WHERE parent_table.id = child_table.parent_id 
        AND parent_table.user_id = auth.uid()
    ));
```
- Row Level Security enabled by default
- User-based ownership policies
- Relationship-based access control
- Service role for administrative access

### File Management Pattern
```sql
-- File metadata tracking
CREATE TABLE files (
    id SERIAL PRIMARY KEY,
    user_id UUID NOT NULL,
    file_path TEXT NOT NULL,
    file_name TEXT NOT NULL,
    file_size INTEGER,
    content_type TEXT
);

-- File associations
CREATE TABLE entity_files (
    entity_id INTEGER NOT NULL,
    file_id INTEGER NOT NULL,
    file_type TEXT,
    PRIMARY KEY (entity_id, file_id)
);
```
- Centralized file metadata
- Type-specific associations
- Ownership validation
- Secure access control

## Template System Patterns

### Component Definition Pattern
```typescript
interface GuiComponent {
    id: string;           // Semantic identifier
    description: string;  // Usage documentation
    parameters: {         // Component configuration
        type: string,
        default?: any,
        validation?: object
    }
}
```
- Semantic naming (e.g., "type-variant-component")
- Comprehensive documentation
- Structured parameters
- Built-in validation

### Property Inheritance Pattern
1. Template Variable Properties (highest priority)
2. GUI Component Parameters (database defaults)
3. Component Default Values (lowest priority)

### State Management Pattern
- Centralized template store
- Immutable state updates
- Validation state tracking
- Event-based updates

### Input Component Architecture Pattern
```typescript
interface InputComponentProps {
  value: any;
  onChange: (value: any) => void;
  name: string;
  gui: {
    label?: string;
    tooltip?: string;
    [key: string]: any;  // Additional GUI configuration
  };
}
```
- Common interface across all input components
- Consistent prop structure
- Built-in tooltip support
- Flexible GUI configuration

### Input Component Hierarchy
```
Template System
└── TemplateRenderer
    └── ListGroupRenderer
        └── InputRenderer
            └── Specific Input Components
```
- Clear component hierarchy
- Separation of concerns
- Dynamic component loading
- Group-based organization

### Dependencies Management Pattern
```typescript
interface DependencyConfig {
  dependson_var?: string;    // Variable name this depends on
  dependson_val?: any[];     // Required values to show component
}
```
- Explicit dependency declaration
- Value-based conditional rendering
- Parent-child relationship handling
- Recursive dependency resolution

### Value Comparison Pattern
```typescript
function isEqual(a: any, b: any): boolean {
  // Deep comparison logic for:
  // - Primitives
  // - Arrays
  // - Objects
  // - Null/Undefined
}
```
- Deep equality comparison
- Type-safe comparisons
- Support for complex data structures
- Consistent value matching

## Job Processing Patterns

### Job-Task Pattern
```typescript
interface Job {
    id: number;
    user_id: string;
    status: string;
    template_id: number;
    dataset_id: number;
    variables: object;
    results?: JobResult[];
}

interface JobResult {
    id: number;
    job_id: number;
    file_path: string;
    metadata: object;
}
```
- Clear status progression
- Result tracking
- Resource association
- User ownership

### Result Management Pattern
```typescript
interface ProcessingResult {
    files: FileInfo[];
    metadata: ProcessingMetadata;
    aggregation?: AggregationData;
}
```
- Structured result format
- Metadata separation
- Optional aggregation

## API Patterns

### Endpoint Structure Pattern
```
/api/[resource]/[id]/[action]
```
- RESTful resource naming
- Consistent action naming
- Clear parameter passing
- Status code usage

### Error Handling Pattern
```typescript
interface ApiError {
    code: string;
    message: string;
    details?: unknown;
}
```
- Consistent error format
- Clear error codes
- Detailed information

## Component Development Patterns

### Input Component Pattern
1. Props Interface Definition
2. Parameter Validation
3. State Management Integration
4. Error Handling
5. Change Event Emission

### Component Organization
```
components/
  └── inputs/         # Base input components
  └── template/       # Template system components
  └── layout/        # Layout components
  └── shared/        # Shared utilities
```

## Best Practices

### Database Operations
1. Use transactions for multi-table operations
2. Implement proper error handling
3. Validate data before insertion
4. Maintain referential integrity
5. Use appropriate indexes

### Security Implementation
1. Enable RLS on all tables
2. Define clear access policies
3. Use service role sparingly
4. Validate ownership in functions
5. Implement proper error handling

### Template Development
1. Use semantic component IDs
2. Document all parameters
3. Provide sensible defaults
4. Implement validation
5. Consider reusability

### Job Processing
1. Track processing status
2. Handle errors gracefully
3. Monitor performance
4. Track results
5. Enable aggregation

### Code Organization
1. Follow consistent structure
2. Document interfaces
3. Implement proper typing
4. Separate concerns
5. Maintain modularity