-- Create job_results table
CREATE TABLE public.job_results (
    id SERIAL PRIMARY KEY,
    job_id INTEGER NOT NULL REFERENCES public.jobs(id) ON DELETE CASCADE,
    file_name TEXT NOT NULL,
    file_path TEXT NOT NULL,
    file_size INTEGER,
    content_type TEXT,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Add index for faster queries
CREATE INDEX idx_job_results_job_id ON public.job_results(job_id);

-- Add RLS policy for job_results
ALTER TABLE public.job_results ENABLE ROW LEVEL SECURITY;

CREATE POLICY job_results_policy ON public.job_results
    USING (EXISTS (
        SELECT 1 FROM public.jobs
        WHERE jobs.id = job_results.job_id AND jobs.user_id = auth.uid()
    ));

-- Grant permissions
GRANT ALL ON TABLE public.job_results TO authenticated;
GRANT ALL ON SEQUENCE public.job_results_id_seq TO authenticated;
