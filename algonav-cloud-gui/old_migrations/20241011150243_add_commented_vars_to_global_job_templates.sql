-- Add the commented_vars column to the global_job_templates table
ALTER TABLE public.global_job_templates ADD COLUMN commented_vars text;

-- Modify the existing update_template_data function
CREATE OR REPLACE FUNCTION public.update_template_data()
R<PERSON><PERSON><PERSON> trigger AS $$
BEGIN
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := public.remove_json_comments(NEW.commented_json);
    END IF;
    IF NEW.commented_vars IS NOT NULL THEN
        NEW.vars := public.remove_json_comments(NEW.commented_vars);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- The existing trigger will now handle both commented_json and commented_vars
-- No need to create a new trigger