CREATE OR REPLACE FUNCTION public.update_batch_status_with_lock(
    p_batch_id INT,
    p_job_status TEXT,
    p_table_name TEXT
) RETURNS TEXT AS $$
DECLARE
    v_current_status TEXT;
    v_completed INT;
    v_failed INT;
    v_new_status TEXT;
BEGIN
    -- Lock the row
    EXECUTE format('SELECT status FROM public.%I WHERE id = $1 FOR UPDATE', p_table_name)
    USING p_batch_id
    INTO v_current_status;

    -- Parse current status or initialize if 'queued'
    IF v_current_status = 'queued' THEN
        v_completed := 0;
        v_failed := 0;
    ELSE
        v_completed := split_part(v_current_status, '/', 1)::INT;
        v_failed := split_part(v_current_status, '/', 2)::INT;
    END IF;

    -- Update counters based on job status
    IF p_job_status = 'complete' THEN
        v_completed := v_completed + 1;
    ELSIF p_job_status = 'error' THEN
        v_failed := v_failed + 1;
    END IF;

    -- Construct new status string
    v_new_status := v_completed || '/' || v_failed;

    -- Update batch status
    EXECUTE format('UPDATE public.%I SET status = $1 WHERE id = $2', p_table_name)
    USING v_new_status, p_batch_id;

    RETURN v_new_status;
END;
$$ LANGUAGE plpgsql;
-- Grant execute permission to authenticated users for the function in the testing schema
GRANT EXECUTE ON FUNCTION public.update_batch_status_with_lock(INT, TEXT, TEXT) TO authenticated;