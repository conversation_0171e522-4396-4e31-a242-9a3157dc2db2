   CREATE OR REPLACE FUNCTION "public"."update_dataset_with_files"(
    "p_dataset_id" integer, 
    "p_description" "text", 
    "p_file_paths" "text"[], 
    "p_file_types" "text"[],
    "p_name" "text", 
    "p_user_id" "uuid", 
    "p_variable_overrides" "jsonb"
    
) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_rows_affected integer;
    v_dataset_exists boolean;
BEGIN
    -- Check if the dataset exists and belongs to the user
    SELECT EXISTS (
        SELECT 1 
        FROM public.datasets 
        WHERE id = p_dataset_id AND user_id = p_user_id
    ) INTO v_dataset_exists;

    IF NOT v_dataset_exists THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    -- Update the dataset
    UPDATE public.datasets
    SET name = p_name,
        description = p_description,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Remove existing file associations
    DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;

    -- Add new file associations
    FOR i IN 1..array_length(p_file_paths, 1) LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Check if the file exists and belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id, file_type)
            VALUES (p_dataset_id, v_file_id, v_file_type);
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
END;
$$;
