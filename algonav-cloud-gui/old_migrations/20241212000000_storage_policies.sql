INSERT INTO storage.buckets (id, name, public)
VALUES 
    ('cloud', 'cloud', false),
    ('results', 'results', false)
ON CONFLICT (id) DO NOTHING;

-- Drop existing policies if they exist
DROP POLICY IF EXISTS "Users have full access to their folder in cloud" ON storage.objects;
DROP POLICY IF EXISTS "Users have full access to their folder in results" ON storage.objects;

-- Create new policies for both buckets
CREATE POLICY "Users have full access to their folder in cloud"
ON storage.objects FOR ALL TO authenticated 
USING (
    bucket_id = 'cloud' 
    AND (auth.uid())::text = (storage.foldername(name))[1]
)
WITH CHECK (
    bucket_id = 'cloud' 
    AND (auth.uid())::text = (storage.foldername(name))[1]
);

CREATE POLICY "Users have full access to their folder in results"
ON storage.objects FOR ALL TO authenticated 
USING (
    bucket_id = 'results' 
    AND (auth.uid())::text = (storage.foldername(name))[1]
)
WITH CHECK (
    bucket_id = 'results' 
    AND (auth.uid())::text = (storage.foldername(name))[1]
);

-- Make sure RLS is enabled
ALTER TABLE storage.objects ENABLE ROW LEVEL SECURITY;