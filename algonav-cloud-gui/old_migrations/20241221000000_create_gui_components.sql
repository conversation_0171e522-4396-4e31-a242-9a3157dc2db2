-- Create the gui_components table to manage GUI component definitions
-- This table will store information about different types of GUI components
-- that can be referenced by the vars column in the global_job_templates table

-- Create the table
CREATE TABLE IF NOT EXISTS gui_components (
    -- The unique identifier for the component, e.g., 'TextField', 'Checkbox'
    id TEXT PRIMARY KEY,
    
    -- A human-readable description of the component and its purpose
    description TEXT,
    
    -- JSON object containing component-specific definitions including:
    -- - type: The data type of the component (e.g., 'string', 'boolean', 'array')
    -- - default: Default value for the component
    -- - min/max: Numerical constraints where applicable
    -- - options: Possible values for selection components
    -- and any other component-specific parameters
    parameters JSONB NOT NULL CHECK (parameters IS NOT NULL),
    
    -- Add timestamps for record keeping
    created_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT TIMEZONE('utc'::text, NOW()) NOT NULL
);

-- Create an index on the id column for faster lookups
CREATE INDEX IF NOT EXISTS idx_gui_components_id ON gui_components(id);

-- Create a trigger to automatically update the updated_at timestamp
CREATE OR REPLACE FUNCTION update_gui_components_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

CREATE TRIGGER trigger_gui_components_updated_at
    BEFORE UPDATE ON gui_components
    FOR EACH ROW
    EXECUTE FUNCTION update_gui_components_updated_at();

-- Add RLS (Row Level Security) policies
ALTER TABLE gui_components ENABLE ROW LEVEL SECURITY;

-- Allow read access to all authenticated users
CREATE POLICY "Allow read access for all authenticated users"
    ON gui_components
    FOR SELECT
    TO authenticated
    USING (true);

-- Allow full access to service role only (for administrative purposes)
CREATE POLICY "Allow full access to service role"
    ON gui_components
    TO service_role
    USING (true)
    WITH CHECK (true);

-- Add helpful comment to the table
COMMENT ON TABLE gui_components IS 'Stores definitions and configurations for GUI components used in the application';
