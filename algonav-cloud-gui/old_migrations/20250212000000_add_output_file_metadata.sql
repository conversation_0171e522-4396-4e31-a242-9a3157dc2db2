-- Add metadata columns to job_results table
ALTER TABLE public.job_results 
ADD COLUMN file_type TEXT,      -- Type from JSON output_files definition
ADD COLUMN visible BOOLEAN DEFAULT true,
ADD COLUMN required BOOLEAN DEFAULT true;

-- Update existing rows to set defaults
UPDATE public.job_results
SET visible = true,
    required = true,
    file_type = CASE 
        WHEN content_type = 'application/vnd.google-earth.kml+xml' THEN 'kml'
        WHEN content_type = 'text/csv' THEN 'csv'
        WHEN content_type = 'application/tix' THEN 'tix'
        ELSE split_part(content_type, '/', 2)
    END;