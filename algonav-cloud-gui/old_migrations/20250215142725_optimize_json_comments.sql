-- Create the new optimized function
CREATE OR REPLACE FUNCTION public.remove_json_comments_optimized(commented_json text)
RETURNS json LANGUAGE plpgsql AS $$
DECLARE
    result text[];
    in_string BOOLEAN := FALSE;
    string_quote CHAR(1) := NULL;
    escape_char BOOLEAN := FALSE;
    in_comment BOOLEAN := FALSE;
    in_multiline_comment BOOLEAN := FALSE;
    i INTEGER := 1;
    c CHAR(1);
    next_c CHAR(1);
    buffer text := '';
BEGIN
    result := ARRAY[]::text[];
    
    WHILE i <= length(commented_json) LOOP
        c := substr(commented_json, i, 1);
        next_c := substr(commented_json, i+1, 1);
        
        -- Handle comments
        IF NOT in_string THEN
            IF c = '/' AND next_c = '/' THEN
                in_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF c = '/' AND next_c = '*' THEN
                in_multiline_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment AND c = E'\n' THEN
                in_comment := FALSE;
                i := i + 1;
                CONTINUE;
            ELSIF in_multiline_comment AND c = '*' AND next_c = '/' THEN
                in_multiline_comment := FALSE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment OR in_multiline_comment THEN
                i := i + 1;
                CONTINUE;
            END IF;
        END IF;

        -- Handle strings
        IF c IN ('"', '''') AND NOT escape_char THEN
            IF in_string AND c = string_quote THEN
                in_string := FALSE;
                string_quote := NULL;
            ELSIF NOT in_string THEN
                in_string := TRUE;
                string_quote := c;
            END IF;
        END IF;

        -- Handle escape characters
        IF c = '\' AND in_string THEN
            escape_char := NOT escape_char;
        ELSE
            escape_char := FALSE;
        END IF;

        -- Append character to buffer
        buffer := buffer || c;
        
        -- Periodically flush buffer to array
        IF length(buffer) >= 1000 THEN
            result := array_append(result, buffer);
            buffer := '';
        END IF;
        
        i := i + 1;
    END LOOP;
    
    -- Append remaining buffer
    IF buffer <> '' THEN
        result := array_append(result, buffer);
    END IF;
    
    -- Return as JSON, preserving all whitespace
    RETURN array_to_string(result, '')::JSON;
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Invalid JSON after removing comments. Error in processing.';
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.remove_json_comments_optimized(text) TO authenticated;

-- Create new trigger function that uses the optimized version
CREATE OR REPLACE FUNCTION public.update_template_data_optimized()
RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := public.remove_json_comments_optimized(NEW.commented_json);
    END IF;
    IF NEW.commented_vars IS NOT NULL THEN
        NEW.vars := public.remove_json_comments_optimized(NEW.commented_vars);
    END IF;
    RETURN NEW;
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.update_template_data_optimized() TO authenticated;

-- Drop old trigger
DROP TRIGGER IF EXISTS update_template_data_trigger ON global_job_templates;

-- Create new trigger with optimized function
CREATE TRIGGER update_template_data_trigger
    BEFORE INSERT OR UPDATE ON global_job_templates
    FOR EACH ROW
    EXECUTE FUNCTION update_template_data_optimized();
