-- Update the trigger function to only process commented fields if they are not null
CREATE OR REPLACE FUNCTION public.update_template_data_optimized()
RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    -- Only process commented_json if it's not null
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := public.remove_json_comments_optimized(NEW.commented_json);
    END IF;
    
    -- Only process commented_vars if it's not null
    IF NEW.commented_vars IS NOT NULL THEN
        NEW.vars := public.remove_json_comments_optimized(NEW.commented_vars);
    END IF;
    
    RETURN NEW;
END;
$$;
