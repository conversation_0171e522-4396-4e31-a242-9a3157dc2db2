-- Fix für die optimierte Funktion zum Entfernen von JSON-<PERSON><PERSON><PERSON>ren, die Leerzeichen in Strings beibehält
CREATE OR REPLACE FUNCTION public.remove_json_comments_optimized(commented_json text)
RETURNS json LANGUAGE plpgsql AS $$
DECLARE
    result text := '';
    in_string BOOLEAN := FALSE;
    string_quote CHAR(1) := NULL;
    escape_char BOOLEAN := FALSE;
    in_comment BOOLEAN := FALSE;
    in_multiline_comment BOOLEAN := FALSE;
    i INTEGER := 1;
    c CHAR(1);
    next_c CHAR(1);
    buffer text := '';
BEGIN
    -- Direkte Zeichenkette verwenden statt Array, um Probleme beim Zusammenfügen zu vermeiden
    WHILE i <= length(commented_json) LOOP
        c := substr(commented_json, i, 1);
        next_c := substr(commented_json, i+1, 1);
        
        -- <PERSON>le comments
        IF NOT in_string THEN
            IF c = '/' AND next_c = '/' THEN
                in_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF c = '/' AND next_c = '*' THEN
                in_multiline_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment AND c = E'\n' THEN
                in_comment := FALSE;
                i := i + 1;
                CONTINUE;
            ELSIF in_multiline_comment AND c = '*' AND next_c = '/' THEN
                in_multiline_comment := FALSE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment OR in_multiline_comment THEN
                i := i + 1;
                CONTINUE;
            END IF;
        END IF;
        -- Handle strings
        IF c IN ('"', '''') AND NOT escape_char THEN
            IF in_string AND c = string_quote THEN
                in_string := FALSE;
                string_quote := NULL;
            ELSIF NOT in_string THEN
                in_string := TRUE;
                string_quote := c;
            END IF;
        END IF;
        -- Handle escape characters
        IF c = '\' AND in_string THEN
            escape_char := NOT escape_char;
        ELSE
            escape_char := FALSE;
        END IF;
        
        -- Direktes Anhängen des Zeichens an das Ergebnis, ähnlich wie in der ursprünglichen Version
        result := result || c;
        
        i := i + 1;
    END LOOP;
    
    -- Return as JSON, preserving all whitespace
    RETURN result::JSON;
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Invalid JSON after removing comments. Error in processing.';
END;
$$;

-- Grant execute permission to authenticated users
GRANT EXECUTE ON FUNCTION public.remove_json_comments_optimized(text) TO authenticated;