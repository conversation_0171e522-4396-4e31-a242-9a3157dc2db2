-- <PERSON><PERSON> den Trigger zurück auf die ursprüngliche remove_json_comments Funktion
-- und beschränke ihn auf Änderungen an commented_json und commented_vars Spalten
DROP TRIGGER IF EXISTS update_template_data_trigger ON global_job_templates;

-- <PERSON><PERSON><PERSON> den <PERSON>gger neu mit der ursprünglichen Funktion
-- und mit der Bedingung, dass er nur läuft, wenn commented_json oder commented_vars geändert werden
CREATE TRIGGER update_template_data_trigger
    BEFORE INSERT OR UPDATE OF commented_json, commented_vars ON global_job_templates
    FOR EACH ROW
    EXECUTE FUNCTION public.update_template_data();

-- Hinweis: Die update_template_data Funktion verwendet die ursprüngliche remove_json_comments Funktion