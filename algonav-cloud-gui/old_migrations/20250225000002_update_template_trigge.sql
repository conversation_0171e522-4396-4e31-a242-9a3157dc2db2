-- Update the trigger function to only process commented fields if they are not null
CREATE OR REPLACE FUNCTION public.update_template_data()
RETURNS trigger LANGUAGE plpgsql AS $$
BEGIN
    -- Only process commented_json if it's not null
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := public.remove_json_comments(NEW.commented_json);
    END IF;
    
    -- Only process commented_vars if it's not null
    IF NEW.commented_vars IS NOT NULL THEN
        NEW.vars := public.remove_json_comments(NEW.commented_vars);
    END IF;
    
    RETURN NEW;
END;
$$;

-- Setze den Trigger zurück auf die ursprüngliche remove_json_comments Funktion
-- und beschränke ihn auf Änderungen an commented_json und commented_vars Spalten
DROP TRIGGER IF EXISTS update_template_data_trigger ON global_job_templates;

-- <PERSON><PERSON><PERSON> den Trigger neu mit der ursprünglichen Funktion
-- und mit der Bedingung, dass er nur läuft, wenn commented_json oder commented_vars geändert werden
CREATE TRIGGER update_template_data_trigger
    BEFORE INSERT OR UPDATE OF commented_json, commented_vars ON global_job_templates
    FOR EACH ROW
    EXECUTE FUNCTION public.update_template_data();
