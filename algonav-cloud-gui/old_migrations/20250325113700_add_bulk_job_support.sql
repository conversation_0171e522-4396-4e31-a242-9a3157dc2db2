-- Add bulk job support
-- Creates bulk job type enum and adds necessary columns to jobs table
-- Create new bulk_job_tasks table for tracking relationships

-- Create enum for bulk job types
CREATE TYPE bulk_job_type AS ENUM ('multi_kml');

-- Add columns to jobs table
ALTER TABLE jobs 
    ADD COLUMN bulk_job_type bulk_job_type,
    ADD COLUMN parent_job_id INTEGER REFERENCES jobs(id);

-- Create bulk job tasks relationship table
CREATE TABLE bulk_job_tasks (
    bulk_job_id INTEGER REFERENCES jobs(id) ON DELETE CASCADE,
    task_id INTEGER REFERENCES jobs(id),
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    PRIMARY KEY (bulk_job_id, task_id)
);

-- Add index for querying bulk jobs by parent
CREATE INDEX idx_jobs_parent_id ON jobs(parent_job_id);

-- Add index to help with task lookups
CREATE INDEX idx_bulk_job_tasks_task_id ON bulk_job_tasks(task_id);

-- Enable RLS
ALTER TABLE bulk_job_tasks ENABLE ROW LEVEL SECURITY;

-- Create policy for bulk_job_tasks
CREATE POLICY "bulk_job_tasks_policy" ON bulk_job_tasks
    USING (
        EXISTS (
            SELECT 1 FROM jobs
            WHERE jobs.id = bulk_job_tasks.bulk_job_id
            AND jobs.user_id = auth.uid()
        )
    );

-- Grant permissions
GRANT ALL ON TABLE bulk_job_tasks TO authenticated;
GRANT ALL ON TABLE bulk_job_tasks TO service_role;