-- Remove dataset_id not-null constraint from jobs table
-- This makes dataset_id optional since some jobs (like bulk jobs) don't need a dataset reference

ALTER TABLE jobs
  ALTER COLUMN dataset_id DROP NOT NULL;

-- Add comment explaining why some jobs don't need a dataset
COMMENT ON COLUMN jobs.dataset_id IS 'Optional reference to a dataset. Some job types (like bulk jobs) operate on multiple tasks rather than a single dataset.';