-- Migration to correct batch_id population for bulk KML jobs
-- Copies the incorrectly assigned parent_job_id to the correct batch_id field
-- and nullifies the parent_job_id for these specific jobs.

BEGIN; -- Start transaction

UPDATE public.jobs
SET
    batch_id = parent_job_id,
    parent_job_id = NULL
WHERE
    bulk_job_type = 'multi_kml' -- Target the specific job type where the error occurred
    AND batch_id IS NULL        -- Only affect rows where batch_id wasn't set
    AND parent_job_id IS NOT NULL; -- Only affect rows where the incorrect field was set

COMMIT; -- Commit transaction