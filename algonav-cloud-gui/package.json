{"private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start"}, "dependencies": {"@emotion/cache": "^11.11.0", "@emotion/react": "^11.11.4", "@emotion/styled": "^11.11.5", "@mui/base": "^5.0.0-beta.68", "@mui/icons-material": "^5.15.21", "@mui/material": "^5.15.21", "@mui/material-nextjs": "^5.15.11", "@mui/x-charts": "^7.28.0", "@mui/x-date-pickers": "^7.27.1", "@mui/x-tree-view": "^7.17.0", "@supabase/auth-helpers-nextjs": "^0.10.0", "@supabase/auth-helpers-react": "^0.5.0", "@supabase/ssr": "latest", "@supabase/supabase-js": "latest", "@tanstack/react-query": "^5.59.16", "@types/big.js": "^6.2.2", "@types/react-beautiful-dnd": "^13.1.8", "@uppy/dashboard": "^4.1.0", "@uppy/drag-drop": "^4.0.2", "@uppy/file-input": "^4.0.1", "@uppy/progress-bar": "^4.0.0", "@uppy/react": "^4.0.2", "@uppy/xhr-upload": "^4.1.0", "autoprefixer": "10.4.17", "axios": "^1.7.7", "big.js": "^6.2.2", "date-fns": "2.30.0", "dayjs": "^1.11.13", "geist": "^1.2.1", "jszip": "^3.10.1", "next": "latest", "postcss": "8.4.33", "react": "18.2.0", "react-beautiful-dnd": "^13.1.1", "react-dom": "18.2.0", "react-dropzone": "^14.2.3", "react-virtualized-auto-sizer": "^1.0.25", "react-window": "^1.8.11", "tailwindcss": "3.4.1", "typescript": "5.3.3", "uuid": "^10.0.0", "zustand": "^5.0.0"}, "devDependencies": {"@types/lodash": "^4.17.14", "@types/node": "20.11.5", "@types/react": "18.2.48", "@types/react-dom": "18.2.18", "encoding": "^0.1.13"}}