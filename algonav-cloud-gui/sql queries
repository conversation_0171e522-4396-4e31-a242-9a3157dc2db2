---- dataset_files table

SET
  search_path TO testing;

CREATE TABLE
  dataset_files (
    dataset_id INT REFERENCES DATASETS (id) ON DELETE CASCADE,
    file_id INT REFERENCES FILES (id),
    file_type TEXT,
    PRIMARY KEY (dataset_id, file_id)
  );

---- RLS
SET search_path TO testing;
ALTER TABLE dataset_files ENABLE ROW LEVEL SECURITY;

---- rls policy for  dataset_files

SET
  search_path TO testing;

CREATE POLICY dataset_files_access_policy ON dataset_files FOR ALL USING (
  EXISTS (
    SELECT
      1
    FROM
      DATASETS
    WHERE
      DATASETS.id = dataset_files.dataset_id
      AND DATASETS.user_id = auth.uid ()
  )
);

REVOKE INSERT, UPDATE, DELETE ON dataset_files FROM public;






----- custom trigger to automatically sync dataset_files table with datasets table

SET
  search_path TO testing;
CREATE OR REPLACE FUNCTION sync_dataset_files() R<PERSON>URNS TRIGGER AS $$
BEGIN
  -- <PERSON>öschen alter Einträge
  DELETE FROM testing.dataset_files WHERE dataset_id = NEW.id;
  
  -- Einfügen neuer Einträge
  INSERT INTO testing.dataset_files (dataset_id, file_id, file_type)
  SELECT 
    NEW.id,
    (file_id::text)::integer,
    jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
  FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
       jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
  RETURN NEW;
END;
$$ LANGUAGE plpgsql;
CREATE OR REPLACE TRIGGER sync_dataset_files_trigger
AFTER INSERT OR UPDATE ON testing.DATASETS
FOR EACH ROW EXECUTE FUNCTION sync_dataset_files();

-- Switch to the Testing schema
SET search_path TO testing;



-- Create function to remove JSON comments
CREATE OR REPLACE FUNCTION remove_json_comments(commented_json TEXT)
RETURNS JSONB AS $$
DECLARE
    json_without_comments TEXT := '';
    in_string BOOLEAN := FALSE;
    string_quote CHAR(1) := NULL;
    escape_char BOOLEAN := FALSE;
    in_comment BOOLEAN := FALSE;
    in_multiline_comment BOOLEAN := FALSE;
    i INTEGER := 1;
    c CHAR(1);
    next_c CHAR(1);
BEGIN
    WHILE i <= length(commented_json) LOOP
        c := substr(commented_json, i, 1);
        next_c := substr(commented_json, i+1, 1);
        
        IF NOT in_string AND NOT in_comment AND NOT in_multiline_comment THEN
            IF c = '/' AND next_c = '/' THEN
                in_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF c = '/' AND next_c = '*' THEN
                in_multiline_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF c IN ('"', '''') THEN
                in_string := TRUE;
                string_quote := c;
            END IF;
        ELSIF in_comment THEN
            IF c = E'\n' THEN
                in_comment := FALSE;
            END IF;
            i := i + 1;
            CONTINUE;
        ELSIF in_multiline_comment THEN
            IF c = '*' AND next_c = '/' THEN
                in_multiline_comment := FALSE;
                i := i + 2;
                CONTINUE;
            END IF;
            i := i + 1;
            CONTINUE;
        ELSIF in_string THEN
            IF c = '\' AND NOT escape_char THEN
                escape_char := TRUE;
            ELSIF c = string_quote AND NOT escape_char THEN
                in_string := FALSE;
                string_quote := NULL;
            ELSE
                escape_char := FALSE;
            END IF;
        END IF;
        
        json_without_comments := json_without_comments || c;
        i := i + 1;
    END LOOP;
    
    RETURN json_without_comments::JSONB;
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Invalid JSON after removing comments. Processed string: %', json_without_comments;
END;
$$ LANGUAGE plpgsql;

-- Create trigger function
CREATE OR REPLACE FUNCTION testing.update_template_data() RETURNS TRIGGER AS $$
BEGIN
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := testing.remove_json_comments(NEW.commented_json);
    END IF;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger
CREATE OR REPLACE TRIGGER update_template_data_trigger
BEFORE INSERT OR UPDATE ON testing.GLOBAL_JOB_TEMPLATES
FOR EACH ROW EXECUTE FUNCTION testing.update_template_data();
