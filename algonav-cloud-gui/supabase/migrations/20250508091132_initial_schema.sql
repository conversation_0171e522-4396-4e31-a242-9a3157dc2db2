create type "public"."bulk_job_type" as enum ('multi_kml');

create sequence "public"."batches_id_seq";

create sequence "public"."categories_id_seq";

create sequence "public"."datasets_id_seq";

create sequence "public"."files_id_seq";

create sequence "public"."global_job_templates_id_seq";

create sequence "public"."job_results_id_seq";

create sequence "public"."jobs_id_seq";

create table "public"."batches" (
    "id" integer not null default nextval('batches_id_seq'::regclass),
    "user_id" uuid not null,
    "name" character varying(255) not null,
    "description" text,
    "status" character varying(50) not null,
    "created_at" timestamp without time zone not null default CURRENT_TIMESTAMP,
    "updated_at" timestamp without time zone not null default CURRENT_TIMESTAMP
);


alter table "public"."batches" enable row level security;

create table "public"."bulk_job_tasks" (
    "bulk_job_id" integer not null,
    "task_id" integer not null,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP
);


alter table "public"."bulk_job_tasks" enable row level security;

create table "public"."categories" (
    "id" integer not null default nextval('categories_id_seq'::regclass),
    "user_id" uuid not null,
    "name" character varying(255) not null,
    "description" text,
    "parent_category_id" integer,
    "variable_overrides" jsonb
);


alter table "public"."categories" enable row level security;

create table "public"."category_files" (
    "category_id" integer not null,
    "file_id" integer not null,
    "file_type" text
);


alter table "public"."category_files" enable row level security;

create table "public"."category_job_templates" (
    "category_id" integer not null,
    "global_job_template_id" integer not null
);


alter table "public"."category_job_templates" enable row level security;

create table "public"."dataset_files" (
    "dataset_id" integer not null,
    "file_id" integer not null,
    "file_type" text
);


alter table "public"."dataset_files" enable row level security;

create table "public"."datasets" (
    "id" integer not null default nextval('datasets_id_seq'::regclass),
    "user_id" uuid not null,
    "category_id" integer,
    "name" character varying(255) not null,
    "description" text,
    "variable_overrides" jsonb
);


alter table "public"."datasets" enable row level security;

create table "public"."files" (
    "id" integer not null default nextval('files_id_seq'::regclass),
    "user_id" uuid not null,
    "dataset_id" integer,
    "bucket_name" character varying(255) not null,
    "file_path" character varying(255) not null,
    "file_name" character varying(255) not null,
    "file_size" integer,
    "content_type" character varying(255),
    "created_at" timestamp without time zone not null default CURRENT_TIMESTAMP
);


alter table "public"."files" enable row level security;

create table "public"."global_job_templates" (
    "id" integer not null default nextval('global_job_templates_id_seq'::regclass),
    "user_id" uuid not null,
    "name" character varying(255) not null,
    "description" text,
    "template_data" jsonb,
    "vars" jsonb,
    "commented_json" text,
    "commented_vars" text
);


alter table "public"."global_job_templates" enable row level security;

create table "public"."gui_components" (
    "id" text not null,
    "description" text,
    "parameters" jsonb not null,
    "created_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "updated_at" timestamp with time zone not null default timezone('utc'::text, now()),
    "component_name" text not null
);


alter table "public"."gui_components" enable row level security;

create table "public"."job_results" (
    "id" integer not null default nextval('job_results_id_seq'::regclass),
    "job_id" integer not null,
    "file_name" text not null,
    "file_path" text not null,
    "file_size" integer,
    "content_type" text,
    "created_at" timestamp with time zone default CURRENT_TIMESTAMP,
    "file_type" text,
    "visible" boolean default true,
    "required" boolean default true
);


alter table "public"."job_results" enable row level security;

create table "public"."job_template_files" (
    "job_template_id" integer not null,
    "file_id" integer not null,
    "file_type" text
);


alter table "public"."job_template_files" enable row level security;

create table "public"."jobs" (
    "id" integer not null default nextval('jobs_id_seq'::regclass),
    "user_id" uuid not null,
    "name" character varying(255) not null,
    "description" text,
    "global_job_template_id" integer not null,
    "dataset_id" integer,
    "job_json" jsonb,
    "status" character varying(50) not null,
    "created_at" timestamp without time zone not null default CURRENT_TIMESTAMP,
    "updated_at" timestamp without time zone not null default CURRENT_TIMESTAMP,
    "batch_id" integer,
    "workervars" jsonb,
    "vars" jsonb,
    "result" jsonb,
    "bulk_job_type" bulk_job_type
);


alter table "public"."jobs" enable row level security;

alter sequence "public"."batches_id_seq" owned by "public"."batches"."id";

alter sequence "public"."categories_id_seq" owned by "public"."categories"."id";

alter sequence "public"."datasets_id_seq" owned by "public"."datasets"."id";

alter sequence "public"."files_id_seq" owned by "public"."files"."id";

alter sequence "public"."global_job_templates_id_seq" owned by "public"."global_job_templates"."id";

alter sequence "public"."job_results_id_seq" owned by "public"."job_results"."id";

alter sequence "public"."jobs_id_seq" owned by "public"."jobs"."id";

CREATE UNIQUE INDEX batches_pkey ON public.batches USING btree (id);

CREATE UNIQUE INDEX bulk_job_tasks_pkey ON public.bulk_job_tasks USING btree (bulk_job_id, task_id);

CREATE UNIQUE INDEX categories_pkey ON public.categories USING btree (id);

CREATE UNIQUE INDEX category_files_pkey ON public.category_files USING btree (category_id, file_id);

CREATE UNIQUE INDEX category_job_templates_pkey ON public.category_job_templates USING btree (category_id, global_job_template_id);

CREATE UNIQUE INDEX dataset_files_pkey ON public.dataset_files USING btree (dataset_id, file_id);

CREATE UNIQUE INDEX datasets_pkey ON public.datasets USING btree (id);

CREATE UNIQUE INDEX files_pkey ON public.files USING btree (id);

CREATE UNIQUE INDEX global_job_templates_pkey ON public.global_job_templates USING btree (id);

CREATE UNIQUE INDEX gui_components_pkey ON public.gui_components USING btree (id);

CREATE INDEX idx_bulk_job_tasks_task_id ON public.bulk_job_tasks USING btree (task_id);

CREATE INDEX idx_gui_components_id ON public.gui_components USING btree (id);

CREATE INDEX idx_job_results_job_id ON public.job_results USING btree (job_id);

CREATE UNIQUE INDEX job_results_pkey ON public.job_results USING btree (id);

CREATE UNIQUE INDEX job_template_files_pkey ON public.job_template_files USING btree (job_template_id, file_id);

CREATE UNIQUE INDEX jobs_pkey ON public.jobs USING btree (id);

alter table "public"."batches" add constraint "batches_pkey" PRIMARY KEY using index "batches_pkey";

alter table "public"."bulk_job_tasks" add constraint "bulk_job_tasks_pkey" PRIMARY KEY using index "bulk_job_tasks_pkey";

alter table "public"."categories" add constraint "categories_pkey" PRIMARY KEY using index "categories_pkey";

alter table "public"."category_files" add constraint "category_files_pkey" PRIMARY KEY using index "category_files_pkey";

alter table "public"."category_job_templates" add constraint "category_job_templates_pkey" PRIMARY KEY using index "category_job_templates_pkey";

alter table "public"."dataset_files" add constraint "dataset_files_pkey" PRIMARY KEY using index "dataset_files_pkey";

alter table "public"."datasets" add constraint "datasets_pkey" PRIMARY KEY using index "datasets_pkey";

alter table "public"."files" add constraint "files_pkey" PRIMARY KEY using index "files_pkey";

alter table "public"."global_job_templates" add constraint "global_job_templates_pkey" PRIMARY KEY using index "global_job_templates_pkey";

alter table "public"."gui_components" add constraint "gui_components_pkey" PRIMARY KEY using index "gui_components_pkey";

alter table "public"."job_results" add constraint "job_results_pkey" PRIMARY KEY using index "job_results_pkey";

alter table "public"."job_template_files" add constraint "job_template_files_pkey" PRIMARY KEY using index "job_template_files_pkey";

alter table "public"."jobs" add constraint "jobs_pkey" PRIMARY KEY using index "jobs_pkey";

alter table "public"."batches" add constraint "batches_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."batches" validate constraint "batches_user_id_fkey";

alter table "public"."bulk_job_tasks" add constraint "bulk_job_tasks_bulk_job_id_fkey" FOREIGN KEY (bulk_job_id) REFERENCES jobs(id) ON DELETE CASCADE not valid;

alter table "public"."bulk_job_tasks" validate constraint "bulk_job_tasks_bulk_job_id_fkey";

alter table "public"."bulk_job_tasks" add constraint "bulk_job_tasks_task_id_fkey" FOREIGN KEY (task_id) REFERENCES jobs(id) not valid;

alter table "public"."bulk_job_tasks" validate constraint "bulk_job_tasks_task_id_fkey";

alter table "public"."categories" add constraint "categories_parent_category_id_fkey" FOREIGN KEY (parent_category_id) REFERENCES categories(id) not valid;

alter table "public"."categories" validate constraint "categories_parent_category_id_fkey";

alter table "public"."categories" add constraint "categories_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."categories" validate constraint "categories_user_id_fkey";

alter table "public"."category_files" add constraint "category_files_category_id_fkey" FOREIGN KEY (category_id) REFERENCES categories(id) ON DELETE CASCADE not valid;

alter table "public"."category_files" validate constraint "category_files_category_id_fkey";

alter table "public"."category_files" add constraint "category_files_file_id_fkey" FOREIGN KEY (file_id) REFERENCES files(id) not valid;

alter table "public"."category_files" validate constraint "category_files_file_id_fkey";

alter table "public"."category_job_templates" add constraint "category_job_templates_category_id_fkey" FOREIGN KEY (category_id) REFERENCES categories(id) not valid;

alter table "public"."category_job_templates" validate constraint "category_job_templates_category_id_fkey";

alter table "public"."category_job_templates" add constraint "category_job_templates_global_job_template_id_fkey" FOREIGN KEY (global_job_template_id) REFERENCES global_job_templates(id) not valid;

alter table "public"."category_job_templates" validate constraint "category_job_templates_global_job_template_id_fkey";

alter table "public"."dataset_files" add constraint "dataset_files_dataset_id_fkey" FOREIGN KEY (dataset_id) REFERENCES datasets(id) ON DELETE CASCADE not valid;

alter table "public"."dataset_files" validate constraint "dataset_files_dataset_id_fkey";

alter table "public"."dataset_files" add constraint "dataset_files_file_id_fkey" FOREIGN KEY (file_id) REFERENCES files(id) ON DELETE CASCADE not valid;

alter table "public"."dataset_files" validate constraint "dataset_files_file_id_fkey";

alter table "public"."datasets" add constraint "datasets_category_id_fkey" FOREIGN KEY (category_id) REFERENCES categories(id) not valid;

alter table "public"."datasets" validate constraint "datasets_category_id_fkey";

alter table "public"."datasets" add constraint "datasets_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."datasets" validate constraint "datasets_user_id_fkey";

alter table "public"."files" add constraint "files_dataset_id_fkey" FOREIGN KEY (dataset_id) REFERENCES datasets(id) not valid;

alter table "public"."files" validate constraint "files_dataset_id_fkey";

alter table "public"."files" add constraint "files_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."files" validate constraint "files_user_id_fkey";

alter table "public"."global_job_templates" add constraint "global_job_templates_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."global_job_templates" validate constraint "global_job_templates_user_id_fkey";

alter table "public"."gui_components" add constraint "gui_components_component_name_not_empty" CHECK ((component_name <> ''::text)) not valid;

alter table "public"."gui_components" validate constraint "gui_components_component_name_not_empty";

alter table "public"."gui_components" add constraint "gui_components_parameters_check" CHECK ((parameters IS NOT NULL)) not valid;

alter table "public"."gui_components" validate constraint "gui_components_parameters_check";

alter table "public"."job_results" add constraint "job_results_job_id_fkey" FOREIGN KEY (job_id) REFERENCES jobs(id) ON DELETE CASCADE not valid;

alter table "public"."job_results" validate constraint "job_results_job_id_fkey";

alter table "public"."job_template_files" add constraint "job_template_files_file_id_fkey" FOREIGN KEY (file_id) REFERENCES files(id) not valid;

alter table "public"."job_template_files" validate constraint "job_template_files_file_id_fkey";

alter table "public"."job_template_files" add constraint "job_template_files_job_template_id_fkey" FOREIGN KEY (job_template_id) REFERENCES global_job_templates(id) ON DELETE CASCADE not valid;

alter table "public"."job_template_files" validate constraint "job_template_files_job_template_id_fkey";

alter table "public"."jobs" add constraint "jobs_batch_id_fkey" FOREIGN KEY (batch_id) REFERENCES batches(id) not valid;

alter table "public"."jobs" validate constraint "jobs_batch_id_fkey";

alter table "public"."jobs" add constraint "jobs_dataset_id_fkey" FOREIGN KEY (dataset_id) REFERENCES datasets(id) not valid;

alter table "public"."jobs" validate constraint "jobs_dataset_id_fkey";

alter table "public"."jobs" add constraint "jobs_global_job_template_id_fkey" FOREIGN KEY (global_job_template_id) REFERENCES global_job_templates(id) not valid;

alter table "public"."jobs" validate constraint "jobs_global_job_template_id_fkey";

alter table "public"."jobs" add constraint "jobs_user_id_fkey" FOREIGN KEY (user_id) REFERENCES auth.users(id) not valid;

alter table "public"."jobs" validate constraint "jobs_user_id_fkey";

set check_function_bodies = off;

CREATE OR REPLACE FUNCTION public.create_dataset_with_files(p_user_id uuid, p_name text, p_description text, p_category_id integer, p_variable_overrides jsonb, p_file_paths text[])
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_dataset_id integer;
    v_file_path text;
    v_file_id integer;
    v_files_count integer := 0;
    v_associated_files_count integer := 0;
BEGIN
    -- Insert the dataset
    INSERT INTO public.datasets (user_id, name, description, category_id, variable_overrides)
    VALUES (p_user_id, p_name, p_description, p_category_id, p_variable_overrides)
    RETURNING id INTO v_dataset_id;

    -- Check if the dataset was actually inserted
    IF v_dataset_id IS NULL THEN
        RAISE EXCEPTION 'Failed to insert dataset';
    END IF;

    RAISE NOTICE 'Dataset created successfully with ID: %', v_dataset_id;

    -- Count the number of files provided
    v_files_count := array_length(p_file_paths, 1);

    -- Associate files with the dataset
    FOREACH v_file_path IN ARRAY p_file_paths LOOP
        -- Get the file ID from the file table, ensuring it belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id)
            VALUES (v_dataset_id, v_file_id);
            v_associated_files_count := v_associated_files_count + 1;
            RAISE NOTICE 'File associated with dataset. Dataset ID: %, File ID: %', v_dataset_id, v_file_id;
        ELSE
            RAISE WARNING 'File not found or user does not have permission: %', v_file_path;
        END IF;
    END LOOP;

    RAISE NOTICE 'Associated % out of % files with the dataset', v_associated_files_count, v_files_count;

    RETURN json_build_object(
        'dataset_id', v_dataset_id,
        'associated_files_count', v_associated_files_count,
        'total_files_count', v_files_count
    );
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise
        RAISE NOTICE 'Error in create_dataset_with_files: %', SQLERRM;
        RAISE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.create_dataset_with_files(p_user_id uuid, p_name text, p_description text, p_category_id integer, p_variable_overrides jsonb, p_file_paths text[], p_file_types text[])
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_dataset_id integer;
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_files_count integer := 0;
    v_associated_files_count integer := 0;
BEGIN
    -- Insert the dataset
    INSERT INTO public.datasets (user_id, name, description, category_id, variable_overrides)
    VALUES (p_user_id, p_name, p_description, p_category_id, p_variable_overrides)
    RETURNING id INTO v_dataset_id;

    -- Count the number of files provided
    v_files_count := array_length(p_file_paths, 1);

    -- Associate files with the dataset
    FOR i IN 1..v_files_count LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Get the file ID from the file table, ensuring it belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id, file_type)
            VALUES (v_dataset_id, v_file_id, v_file_type);
            v_associated_files_count := v_associated_files_count + 1;
        END IF;
    END LOOP;

    RETURN json_build_object(
        'dataset_id', v_dataset_id,
        'associated_files_count', v_associated_files_count,
        'total_files_count', v_files_count
    );
END;
$function$
;

CREATE OR REPLACE FUNCTION public.handle_storage_change()
 RETURNS trigger
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    file_owner_id uuid;
    file_name text;
    folder_name text;
BEGIN
    -- Extract the first folder name from the file path
    folder_name := split_part(NEW.name, '/', 1);
    
    -- Check if the folder name is a valid UUID and exists in the auth.users table
    IF EXISTS (
        SELECT 1 FROM auth.users WHERE id::text = folder_name
    ) THEN
        file_owner_id := folder_name::uuid;
    ELSE
        -- If not a valid user UUID, set to NULL or handle as needed
        file_owner_id := NULL;
    END IF;

    -- Extract the file name from the path
    file_name := split_part(NEW.name, '/', -1);

    IF (TG_OP = 'INSERT') THEN
        INSERT INTO public.FILES (user_id, bucket_name, file_path, file_name, file_size, content_type)
        VALUES (
            file_owner_id,
            TG_ARGV[0]::text,
            NEW.name,
            file_name,
            COALESCE((NEW.metadata->>'size')::bigint, 0),
            COALESCE(NEW.metadata->>'mimetype', 'application/octet-stream')
        );
    ELSIF (TG_OP = 'DELETE') THEN
        DELETE FROM public.FILES WHERE bucket_name = TG_ARGV[0]::text AND file_path = OLD.name;
    END IF;

    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.remove_json_comments(commented_json text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    json_without_comments TEXT := '';
    in_string BOOLEAN := FALSE;
    string_quote CHAR(1) := NULL;
    escape_char BOOLEAN := FALSE;
    in_comment BOOLEAN := FALSE;
    in_multiline_comment BOOLEAN := FALSE;
    i INTEGER := 1;
    c CHAR(1);
    next_c CHAR(1);
BEGIN
    WHILE i <= length(commented_json) LOOP
        c := substr(commented_json, i, 1);
        next_c := substr(commented_json, i+1, 1);
        
        -- Handle comments
        IF NOT in_string THEN
            IF c = '/' AND next_c = '/' THEN
                in_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF c = '/' AND next_c = '*' THEN
                in_multiline_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment AND c = E'\n' THEN
                in_comment := FALSE;
                i := i + 1;
                CONTINUE;
            ELSIF in_multiline_comment AND c = '*' AND next_c = '/' THEN
                in_multiline_comment := FALSE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment OR in_multiline_comment THEN
                i := i + 1;
                CONTINUE;
            END IF;
        END IF;

        -- Handle strings
        IF c IN ('"', '''') AND NOT escape_char THEN
            IF in_string AND c = string_quote THEN
                in_string := FALSE;
                string_quote := NULL;
            ELSIF NOT in_string THEN
                in_string := TRUE;
                string_quote := c;
            END IF;
        END IF;

        -- Handle escape characters
        IF c = '\' AND in_string THEN
            escape_char := NOT escape_char;
        ELSE
            escape_char := FALSE;
        END IF;

        -- Append character to result
        json_without_comments := format('%s%s', json_without_comments, c);
        i := i + 1;
    END LOOP;
    
    -- Return as JSON, preserving all whitespace
    RETURN json_without_comments::JSON;
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Invalid JSON after removing comments. Processed string: %', json_without_comments;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.remove_json_comments_optimized(commented_json text)
 RETURNS json
 LANGUAGE plpgsql
AS $function$
DECLARE
    result text := '';
    in_string BOOLEAN := FALSE;
    string_quote CHAR(1) := NULL;
    escape_char BOOLEAN := FALSE;
    in_comment BOOLEAN := FALSE;
    in_multiline_comment BOOLEAN := FALSE;
    i INTEGER := 1;
    c CHAR(1);
    next_c CHAR(1);
BEGIN
    -- Zeichenweise durch den JSON-String gehen
    WHILE i <= length(commented_json) LOOP
        c := substr(commented_json, i, 1);
        next_c := CASE WHEN i < length(commented_json) THEN substr(commented_json, i+1, 1) ELSE '' END;
        
        -- Kommentare behandeln - aber nur wenn wir nicht in einem String sind
        IF NOT in_string THEN
            IF c = '/' AND next_c = '/' THEN
                in_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF c = '/' AND next_c = '*' THEN
                in_multiline_comment := TRUE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment AND c = E'\n' THEN
                in_comment := FALSE;
                result := result || c; -- Zeilenumbruch beibehalten
                i := i + 1;
                CONTINUE;
            ELSIF in_multiline_comment AND c = '*' AND next_c = '/' THEN
                in_multiline_comment := FALSE;
                i := i + 2;
                CONTINUE;
            ELSIF in_comment OR in_multiline_comment THEN
                i := i + 1;
                CONTINUE;
            END IF;
        END IF;
        
        -- Strings verarbeiten
        IF c IN ('"', '''') AND NOT escape_char THEN
            IF in_string AND c = string_quote THEN
                in_string := FALSE;
                string_quote := NULL;
            ELSIF NOT in_string THEN
                in_string := TRUE;
                string_quote := c;
            END IF;
        END IF;
        
        -- Escape-Zeichen behandeln
        IF c = '\' AND in_string THEN
            escape_char := NOT escape_char;
        ELSE
            escape_char := FALSE;
        END IF;
        
        -- Zeichen zum Ergebnis hinzufügen
        result := result || c;
        
        i := i + 1;
    END LOOP;
    
    -- Return as JSON, preserving all whitespace
    RETURN result::JSON;
EXCEPTION
    WHEN others THEN
        RAISE EXCEPTION 'Invalid JSON after removing comments. Error in processing.';
END;
$function$
;

CREATE OR REPLACE FUNCTION public.set_job_status_with_check_and_lock(jobid integer, oldstatus text, newstatus text, table_name text DEFAULT 'jobs'::text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    affected_rows TEXT;
    query TEXT;
BEGIN
    -- Dynamically construct the SELECT query
    query := format('SELECT status FROM public.%I WHERE id = $1 FOR NO KEY UPDATE NOWAIT', table_name);

    -- Execute the dynamic query
    EXECUTE query INTO affected_rows USING jobid;

    IF affected_rows != oldstatus THEN
        RETURN 'Status mismatch';
    END IF;

    -- Dynamically construct the UPDATE query
    query := format('UPDATE public.%I SET status = $1 WHERE id = $2', table_name);

    -- Execute the dynamic UPDATE query
    EXECUTE query USING newstatus, jobid;

    RETURN 'Success';

EXCEPTION
    WHEN OTHERS THEN
        -- Return the exception message
        RETURN SQLERRM;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.sync_category_files()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    file_id_value INTEGER;
BEGIN
    SET search_path TO public;
    -- Verify ownership for each file_id in the variable_overrides
    FOR file_id_value IN 
        SELECT (file_id::text)::integer
        FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
             jsonb_array_elements_text(elem->'file_ids') AS file_id
    LOOP
        PERFORM verify_file_ownership(file_id_value, 'category', NEW.id);
    END LOOP;

    DELETE FROM public.category_files WHERE category_id = NEW.id;
  
    INSERT INTO public.category_files (category_id, file_id, file_type)
    SELECT 
        NEW.id,
        (file_id::text)::integer,
        jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
    FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
         jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.sync_dataset_files()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    file_id_value INTEGER;
BEGIN
  SET search_path TO public;
    -- Verify ownership for each file_id in the variable_overrides
    FOR file_id_value IN 
        SELECT (file_id::text)::integer
        FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
             jsonb_array_elements_text(elem->'file_ids') AS file_id
    LOOP
        PERFORM verify_file_ownership(file_id_value, 'dataset', NEW.id);
    END LOOP;

    -- Delete old entries
    DELETE FROM dataset_files WHERE dataset_id = NEW.id;
  
    -- Insert new entries
    INSERT INTO dataset_files (dataset_id, file_id, file_type)
    SELECT 
        NEW.id,
        (file_id::text)::integer,
        jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
    FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
         jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.sync_job_template_files()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
DECLARE
    file_id INTEGER;
BEGIN
    SET search_path TO public;

    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        -- Verify ownership for each file_id in the template_data
        FOR file_id IN 
            SELECT (jsonb_array_elements_text(elem->'file_ids'))::integer
            FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem
        LOOP
            PERFORM verify_file_ownership(file_id, 'job_template', NEW.id);
        END LOOP;

        -- Delete existing entries and insert new ones
        DELETE FROM public.job_template_files WHERE job_template_id = NEW.id;
        
        INSERT INTO public.job_template_files (job_template_id, file_id, file_type)
        SELECT
            NEW.id,
            (jsonb_array_elements_text(elem->'file_ids'))::integer,
            elem->>'name'
        FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem;

        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_batch_status_with_lock(p_batch_id integer, p_job_status text, p_table_name text)
 RETURNS text
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_current_status TEXT;
    v_completed INT;
    v_failed INT;
    v_total INT;
    v_new_status TEXT;
BEGIN
    -- Lock the row
    EXECUTE format('SELECT status FROM public.%I WHERE id = $1 FOR UPDATE', p_table_name)
    USING p_batch_id
    INTO v_current_status;

    -- Get total number of jobs in batch
    EXECUTE format('SELECT COUNT(*) FROM public.jobs WHERE batch_id = $1')
    USING p_batch_id
    INTO v_total;

    -- Parse current status or initialize if 'queued'
    IF v_current_status = 'queued' THEN
        v_completed := 0;
        v_failed := 0;
    ELSE
        v_completed := split_part(v_current_status, '/', 1)::INT;
        v_failed := split_part(v_current_status, '/', 2)::INT;
    END IF;

    -- Update counters based on job status
    IF p_job_status = 'complete' THEN
        v_completed := v_completed + 1;
    ELSIF p_job_status = 'error' THEN
        v_failed := v_failed + 1;
    END IF;

    -- Construct new status string with total jobs
    v_new_status := v_completed || '/' || v_failed || '/' || v_total;

    -- Update batch status
    EXECUTE format('UPDATE public.%I SET status = $1 WHERE id = $2', p_table_name)
    USING v_new_status, p_batch_id;

    RETURN v_new_status;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_dataset_with_files(p_dataset_id integer, p_description text, p_file_paths text[], p_file_types text[], p_name text, p_user_id uuid, p_variable_overrides jsonb)
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_rows_affected integer;
    v_dataset_exists boolean;
BEGIN
    -- Check if the dataset exists and belongs to the user
    SELECT EXISTS (
        SELECT 1 
        FROM public.datasets 
        WHERE id = p_dataset_id AND user_id = p_user_id
    ) INTO v_dataset_exists;

    IF NOT v_dataset_exists THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    -- Update the dataset
    UPDATE public.datasets
    SET name = p_name,
        description = p_description,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Remove existing file associations
    DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;

    -- Add new file associations
    FOR i IN 1..array_length(p_file_paths, 1) LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Check if the file exists and belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id, file_type)
            VALUES (p_dataset_id, v_file_id, v_file_type);
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_dataset_with_files(p_dataset_id integer, p_user_id uuid, p_name text, p_description text, p_category_id integer, p_variable_overrides jsonb, p_file_ids integer[])
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
  v_file_id integer;
BEGIN
  -- Update the dataset
  UPDATE public.datasets
  SET name = p_name,
      description = p_description,
      category_id = p_category_id,
      variable_overrides = p_variable_overrides
  WHERE id = p_dataset_id AND user_id = p_user_id;

  -- Remove existing file associations
  DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;

  -- Add new file associations
  FOREACH v_file_id IN ARRAY p_file_ids LOOP
    INSERT INTO public.dataset_files (dataset_id, file_id)
    VALUES (p_dataset_id, v_file_id);
  END LOOP;

  RETURN json_build_object('dataset_id', p_dataset_id);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_dataset_with_files(p_dataset_id integer, p_user_id uuid, p_name text, p_description text, p_category_id integer, p_variable_overrides jsonb, p_file_paths text[])
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_rows_affected integer;
BEGIN
    -- Update the dataset
    UPDATE public.datasets
    SET name = p_name,
        description = p_description,
        category_id = p_category_id,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Check if the dataset was actually updated
    IF v_rows_affected = 0 THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    RAISE NOTICE 'Dataset updated successfully. Rows affected: %', v_rows_affected;

    -- Remove existing file associations
    DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;
    
    RAISE NOTICE 'Existing file associations removed for dataset ID: %', p_dataset_id;

    -- Add new file associations
    FOREACH v_file_path IN ARRAY p_file_paths LOOP
        -- Get the file ID from the file table
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id)
            VALUES (p_dataset_id, v_file_id);
            RAISE NOTICE 'File associated with dataset. Dataset ID: %, File ID: %', p_dataset_id, v_file_id;
        ELSE
            RAISE WARNING 'File not found or user does not have permission: %', v_file_path;
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise
        RAISE NOTICE 'Error in update_dataset_with_files: %', SQLERRM;
        RAISE;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_dataset_with_files(p_dataset_id integer, p_user_id uuid, p_name text, p_description text, p_category_id integer, p_variable_overrides jsonb, p_file_paths text[], p_file_types text[])
 RETURNS json
 LANGUAGE plpgsql
 SECURITY DEFINER
AS $function$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_rows_affected integer;
    v_dataset_exists boolean;
BEGIN
    -- Check if the dataset exists and belongs to the user
    SELECT EXISTS (
        SELECT 1 
        FROM public.datasets 
        WHERE id = p_dataset_id AND user_id = p_user_id
    ) INTO v_dataset_exists;

    IF NOT v_dataset_exists THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    -- Update the dataset
    UPDATE public.datasets
    SET name = p_name,
        description = p_description,
        category_id = p_category_id,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Remove existing file associations
    DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;

    -- Add new file associations
    FOR i IN 1..array_length(p_file_paths, 1) LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Check if the file exists and belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id, file_type)
            VALUES (p_dataset_id, v_file_id, v_file_type);
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_gui_components_updated_at()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    NEW.updated_at = TIMEZONE('utc'::text, NOW());
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_template_data()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Only process commented_json if it's not null
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := public.remove_json_comments(NEW.commented_json);
    END IF;
    
    -- Only process commented_vars if it's not null
    IF NEW.commented_vars IS NOT NULL THEN
        NEW.vars := public.remove_json_comments(NEW.commented_vars);
    END IF;
    
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.update_template_data_optimized()
 RETURNS trigger
 LANGUAGE plpgsql
AS $function$
BEGIN
    -- Only process commented_json if it's not null
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := public.remove_json_comments_optimized(NEW.commented_json);
    END IF;
    
    -- Only process commented_vars if it's not null
    IF NEW.commented_vars IS NOT NULL THEN
        NEW.vars := public.remove_json_comments_optimized(NEW.commented_vars);
    END IF;
    
    RETURN NEW;
END;
$function$
;

CREATE OR REPLACE FUNCTION public.verify_file_ownership(p_file_id integer, p_object_type text, p_object_id integer)
 RETURNS void
 LANGUAGE plpgsql
AS $function$
DECLARE
    v_file_owner uuid;
    v_object_owner uuid;
BEGIN
    SET search_path TO public;
    -- Get the owner of the file
    SELECT user_id INTO v_file_owner
    FROM files
    WHERE id = p_file_id;

    -- Get the owner of the object (dataset, category, or job_template)
    CASE p_object_type
        WHEN 'dataset' THEN
            SELECT user_id INTO v_object_owner
            FROM datasets
            WHERE id = p_object_id;
        WHEN 'category' THEN
            SELECT user_id INTO v_object_owner
            FROM categories
            WHERE id = p_object_id;
        WHEN 'job_template' THEN
            SELECT user_id INTO v_object_owner
            FROM global_job_templates
            WHERE id = p_object_id;
    END CASE;
    -- Compare the owners
    
    IF v_file_owner != v_object_owner THEN
        RAISE EXCEPTION 'File (ID: %) does not belong to the owner of the % (ID: %)', 
                        p_file_id, p_object_type, p_object_id;
    END IF;
    
END;
$function$
;

grant delete on table "public"."batches" to "anon";

grant insert on table "public"."batches" to "anon";

grant references on table "public"."batches" to "anon";

grant select on table "public"."batches" to "anon";

grant trigger on table "public"."batches" to "anon";

grant truncate on table "public"."batches" to "anon";

grant update on table "public"."batches" to "anon";

grant delete on table "public"."batches" to "authenticated";

grant insert on table "public"."batches" to "authenticated";

grant references on table "public"."batches" to "authenticated";

grant select on table "public"."batches" to "authenticated";

grant trigger on table "public"."batches" to "authenticated";

grant truncate on table "public"."batches" to "authenticated";

grant update on table "public"."batches" to "authenticated";

grant delete on table "public"."batches" to "service_role";

grant insert on table "public"."batches" to "service_role";

grant references on table "public"."batches" to "service_role";

grant select on table "public"."batches" to "service_role";

grant trigger on table "public"."batches" to "service_role";

grant truncate on table "public"."batches" to "service_role";

grant update on table "public"."batches" to "service_role";

grant delete on table "public"."bulk_job_tasks" to "anon";

grant insert on table "public"."bulk_job_tasks" to "anon";

grant references on table "public"."bulk_job_tasks" to "anon";

grant select on table "public"."bulk_job_tasks" to "anon";

grant trigger on table "public"."bulk_job_tasks" to "anon";

grant truncate on table "public"."bulk_job_tasks" to "anon";

grant update on table "public"."bulk_job_tasks" to "anon";

grant delete on table "public"."bulk_job_tasks" to "authenticated";

grant insert on table "public"."bulk_job_tasks" to "authenticated";

grant references on table "public"."bulk_job_tasks" to "authenticated";

grant select on table "public"."bulk_job_tasks" to "authenticated";

grant trigger on table "public"."bulk_job_tasks" to "authenticated";

grant truncate on table "public"."bulk_job_tasks" to "authenticated";

grant update on table "public"."bulk_job_tasks" to "authenticated";

grant delete on table "public"."bulk_job_tasks" to "service_role";

grant insert on table "public"."bulk_job_tasks" to "service_role";

grant references on table "public"."bulk_job_tasks" to "service_role";

grant select on table "public"."bulk_job_tasks" to "service_role";

grant trigger on table "public"."bulk_job_tasks" to "service_role";

grant truncate on table "public"."bulk_job_tasks" to "service_role";

grant update on table "public"."bulk_job_tasks" to "service_role";

grant delete on table "public"."categories" to "anon";

grant insert on table "public"."categories" to "anon";

grant references on table "public"."categories" to "anon";

grant select on table "public"."categories" to "anon";

grant trigger on table "public"."categories" to "anon";

grant truncate on table "public"."categories" to "anon";

grant update on table "public"."categories" to "anon";

grant delete on table "public"."categories" to "authenticated";

grant insert on table "public"."categories" to "authenticated";

grant references on table "public"."categories" to "authenticated";

grant select on table "public"."categories" to "authenticated";

grant trigger on table "public"."categories" to "authenticated";

grant truncate on table "public"."categories" to "authenticated";

grant update on table "public"."categories" to "authenticated";

grant delete on table "public"."categories" to "service_role";

grant insert on table "public"."categories" to "service_role";

grant references on table "public"."categories" to "service_role";

grant select on table "public"."categories" to "service_role";

grant trigger on table "public"."categories" to "service_role";

grant truncate on table "public"."categories" to "service_role";

grant update on table "public"."categories" to "service_role";

grant delete on table "public"."category_files" to "anon";

grant insert on table "public"."category_files" to "anon";

grant references on table "public"."category_files" to "anon";

grant select on table "public"."category_files" to "anon";

grant trigger on table "public"."category_files" to "anon";

grant truncate on table "public"."category_files" to "anon";

grant update on table "public"."category_files" to "anon";

grant delete on table "public"."category_files" to "authenticated";

grant insert on table "public"."category_files" to "authenticated";

grant references on table "public"."category_files" to "authenticated";

grant select on table "public"."category_files" to "authenticated";

grant trigger on table "public"."category_files" to "authenticated";

grant truncate on table "public"."category_files" to "authenticated";

grant update on table "public"."category_files" to "authenticated";

grant delete on table "public"."category_files" to "service_role";

grant insert on table "public"."category_files" to "service_role";

grant references on table "public"."category_files" to "service_role";

grant select on table "public"."category_files" to "service_role";

grant trigger on table "public"."category_files" to "service_role";

grant truncate on table "public"."category_files" to "service_role";

grant update on table "public"."category_files" to "service_role";

grant delete on table "public"."category_job_templates" to "anon";

grant insert on table "public"."category_job_templates" to "anon";

grant references on table "public"."category_job_templates" to "anon";

grant select on table "public"."category_job_templates" to "anon";

grant trigger on table "public"."category_job_templates" to "anon";

grant truncate on table "public"."category_job_templates" to "anon";

grant update on table "public"."category_job_templates" to "anon";

grant delete on table "public"."category_job_templates" to "authenticated";

grant insert on table "public"."category_job_templates" to "authenticated";

grant references on table "public"."category_job_templates" to "authenticated";

grant select on table "public"."category_job_templates" to "authenticated";

grant trigger on table "public"."category_job_templates" to "authenticated";

grant truncate on table "public"."category_job_templates" to "authenticated";

grant update on table "public"."category_job_templates" to "authenticated";

grant delete on table "public"."category_job_templates" to "service_role";

grant insert on table "public"."category_job_templates" to "service_role";

grant references on table "public"."category_job_templates" to "service_role";

grant select on table "public"."category_job_templates" to "service_role";

grant trigger on table "public"."category_job_templates" to "service_role";

grant truncate on table "public"."category_job_templates" to "service_role";

grant update on table "public"."category_job_templates" to "service_role";

grant select on table "public"."dataset_files" to PUBLIC;

grant delete on table "public"."dataset_files" to "anon";

grant insert on table "public"."dataset_files" to "anon";

grant references on table "public"."dataset_files" to "anon";

grant select on table "public"."dataset_files" to "anon";

grant trigger on table "public"."dataset_files" to "anon";

grant truncate on table "public"."dataset_files" to "anon";

grant update on table "public"."dataset_files" to "anon";

grant delete on table "public"."dataset_files" to "authenticated";

grant insert on table "public"."dataset_files" to "authenticated";

grant references on table "public"."dataset_files" to "authenticated";

grant select on table "public"."dataset_files" to "authenticated";

grant trigger on table "public"."dataset_files" to "authenticated";

grant truncate on table "public"."dataset_files" to "authenticated";

grant update on table "public"."dataset_files" to "authenticated";

grant delete on table "public"."dataset_files" to "service_role";

grant insert on table "public"."dataset_files" to "service_role";

grant references on table "public"."dataset_files" to "service_role";

grant select on table "public"."dataset_files" to "service_role";

grant trigger on table "public"."dataset_files" to "service_role";

grant truncate on table "public"."dataset_files" to "service_role";

grant update on table "public"."dataset_files" to "service_role";

grant delete on table "public"."datasets" to "anon";

grant insert on table "public"."datasets" to "anon";

grant references on table "public"."datasets" to "anon";

grant select on table "public"."datasets" to "anon";

grant trigger on table "public"."datasets" to "anon";

grant truncate on table "public"."datasets" to "anon";

grant update on table "public"."datasets" to "anon";

grant delete on table "public"."datasets" to "authenticated";

grant insert on table "public"."datasets" to "authenticated";

grant references on table "public"."datasets" to "authenticated";

grant select on table "public"."datasets" to "authenticated";

grant trigger on table "public"."datasets" to "authenticated";

grant truncate on table "public"."datasets" to "authenticated";

grant update on table "public"."datasets" to "authenticated";

grant delete on table "public"."datasets" to "service_role";

grant insert on table "public"."datasets" to "service_role";

grant references on table "public"."datasets" to "service_role";

grant select on table "public"."datasets" to "service_role";

grant trigger on table "public"."datasets" to "service_role";

grant truncate on table "public"."datasets" to "service_role";

grant update on table "public"."datasets" to "service_role";

grant delete on table "public"."files" to "anon";

grant insert on table "public"."files" to "anon";

grant references on table "public"."files" to "anon";

grant select on table "public"."files" to "anon";

grant trigger on table "public"."files" to "anon";

grant truncate on table "public"."files" to "anon";

grant update on table "public"."files" to "anon";

grant delete on table "public"."files" to "authenticated";

grant insert on table "public"."files" to "authenticated";

grant references on table "public"."files" to "authenticated";

grant select on table "public"."files" to "authenticated";

grant trigger on table "public"."files" to "authenticated";

grant truncate on table "public"."files" to "authenticated";

grant update on table "public"."files" to "authenticated";

grant delete on table "public"."files" to "service_role";

grant insert on table "public"."files" to "service_role";

grant references on table "public"."files" to "service_role";

grant select on table "public"."files" to "service_role";

grant trigger on table "public"."files" to "service_role";

grant truncate on table "public"."files" to "service_role";

grant update on table "public"."files" to "service_role";

grant delete on table "public"."global_job_templates" to "anon";

grant insert on table "public"."global_job_templates" to "anon";

grant references on table "public"."global_job_templates" to "anon";

grant select on table "public"."global_job_templates" to "anon";

grant trigger on table "public"."global_job_templates" to "anon";

grant truncate on table "public"."global_job_templates" to "anon";

grant update on table "public"."global_job_templates" to "anon";

grant delete on table "public"."global_job_templates" to "authenticated";

grant insert on table "public"."global_job_templates" to "authenticated";

grant references on table "public"."global_job_templates" to "authenticated";

grant select on table "public"."global_job_templates" to "authenticated";

grant trigger on table "public"."global_job_templates" to "authenticated";

grant truncate on table "public"."global_job_templates" to "authenticated";

grant update on table "public"."global_job_templates" to "authenticated";

grant delete on table "public"."global_job_templates" to "service_role";

grant insert on table "public"."global_job_templates" to "service_role";

grant references on table "public"."global_job_templates" to "service_role";

grant select on table "public"."global_job_templates" to "service_role";

grant trigger on table "public"."global_job_templates" to "service_role";

grant truncate on table "public"."global_job_templates" to "service_role";

grant update on table "public"."global_job_templates" to "service_role";

grant delete on table "public"."gui_components" to "anon";

grant insert on table "public"."gui_components" to "anon";

grant references on table "public"."gui_components" to "anon";

grant select on table "public"."gui_components" to "anon";

grant trigger on table "public"."gui_components" to "anon";

grant truncate on table "public"."gui_components" to "anon";

grant update on table "public"."gui_components" to "anon";

grant delete on table "public"."gui_components" to "authenticated";

grant insert on table "public"."gui_components" to "authenticated";

grant references on table "public"."gui_components" to "authenticated";

grant select on table "public"."gui_components" to "authenticated";

grant trigger on table "public"."gui_components" to "authenticated";

grant truncate on table "public"."gui_components" to "authenticated";

grant update on table "public"."gui_components" to "authenticated";

grant delete on table "public"."gui_components" to "service_role";

grant insert on table "public"."gui_components" to "service_role";

grant references on table "public"."gui_components" to "service_role";

grant select on table "public"."gui_components" to "service_role";

grant trigger on table "public"."gui_components" to "service_role";

grant truncate on table "public"."gui_components" to "service_role";

grant update on table "public"."gui_components" to "service_role";

grant delete on table "public"."job_results" to "anon";

grant insert on table "public"."job_results" to "anon";

grant references on table "public"."job_results" to "anon";

grant select on table "public"."job_results" to "anon";

grant trigger on table "public"."job_results" to "anon";

grant truncate on table "public"."job_results" to "anon";

grant update on table "public"."job_results" to "anon";

grant delete on table "public"."job_results" to "authenticated";

grant insert on table "public"."job_results" to "authenticated";

grant references on table "public"."job_results" to "authenticated";

grant select on table "public"."job_results" to "authenticated";

grant trigger on table "public"."job_results" to "authenticated";

grant truncate on table "public"."job_results" to "authenticated";

grant update on table "public"."job_results" to "authenticated";

grant delete on table "public"."job_results" to "service_role";

grant insert on table "public"."job_results" to "service_role";

grant references on table "public"."job_results" to "service_role";

grant select on table "public"."job_results" to "service_role";

grant trigger on table "public"."job_results" to "service_role";

grant truncate on table "public"."job_results" to "service_role";

grant update on table "public"."job_results" to "service_role";

grant delete on table "public"."job_template_files" to PUBLIC;

grant insert on table "public"."job_template_files" to PUBLIC;

grant select on table "public"."job_template_files" to PUBLIC;

grant update on table "public"."job_template_files" to PUBLIC;

grant delete on table "public"."job_template_files" to "anon";

grant insert on table "public"."job_template_files" to "anon";

grant references on table "public"."job_template_files" to "anon";

grant select on table "public"."job_template_files" to "anon";

grant trigger on table "public"."job_template_files" to "anon";

grant truncate on table "public"."job_template_files" to "anon";

grant update on table "public"."job_template_files" to "anon";

grant delete on table "public"."job_template_files" to "authenticated";

grant insert on table "public"."job_template_files" to "authenticated";

grant references on table "public"."job_template_files" to "authenticated";

grant select on table "public"."job_template_files" to "authenticated";

grant trigger on table "public"."job_template_files" to "authenticated";

grant truncate on table "public"."job_template_files" to "authenticated";

grant update on table "public"."job_template_files" to "authenticated";

grant delete on table "public"."job_template_files" to "service_role";

grant insert on table "public"."job_template_files" to "service_role";

grant references on table "public"."job_template_files" to "service_role";

grant select on table "public"."job_template_files" to "service_role";

grant trigger on table "public"."job_template_files" to "service_role";

grant truncate on table "public"."job_template_files" to "service_role";

grant update on table "public"."job_template_files" to "service_role";

grant delete on table "public"."jobs" to "anon";

grant insert on table "public"."jobs" to "anon";

grant references on table "public"."jobs" to "anon";

grant select on table "public"."jobs" to "anon";

grant trigger on table "public"."jobs" to "anon";

grant truncate on table "public"."jobs" to "anon";

grant update on table "public"."jobs" to "anon";

grant delete on table "public"."jobs" to "authenticated";

grant insert on table "public"."jobs" to "authenticated";

grant references on table "public"."jobs" to "authenticated";

grant select on table "public"."jobs" to "authenticated";

grant trigger on table "public"."jobs" to "authenticated";

grant truncate on table "public"."jobs" to "authenticated";

grant update on table "public"."jobs" to "authenticated";

grant delete on table "public"."jobs" to "service_role";

grant insert on table "public"."jobs" to "service_role";

grant references on table "public"."jobs" to "service_role";

grant select on table "public"."jobs" to "service_role";

grant trigger on table "public"."jobs" to "service_role";

grant truncate on table "public"."jobs" to "service_role";

grant update on table "public"."jobs" to "service_role";

create policy "batches_policy"
on "public"."batches"
as permissive
for all
to public
using ((auth.uid() = user_id));


create policy "bulk_job_tasks_policy"
on "public"."bulk_job_tasks"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM jobs
  WHERE ((jobs.id = bulk_job_tasks.bulk_job_id) AND (jobs.user_id = auth.uid())))));


create policy "categories_policy"
on "public"."categories"
as permissive
for all
to public
using ((auth.uid() = user_id));


create policy "category_files_access_policy"
on "public"."category_files"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM categories
  WHERE ((categories.id = category_files.category_id) AND (categories.user_id = auth.uid())))));


create policy "category_job_templates_policy"
on "public"."category_job_templates"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM categories
  WHERE ((categories.id = category_job_templates.category_id) AND (categories.user_id = auth.uid())))));


create policy "dataset_files_access_policy"
on "public"."dataset_files"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM (datasets d
     JOIN files f ON ((f.id = dataset_files.file_id)))
  WHERE ((d.id = dataset_files.dataset_id) AND (d.user_id = auth.uid()) AND (f.user_id = auth.uid())))));


create policy "datasets"
on "public"."datasets"
as permissive
for all
to authenticated
using ((auth.uid() = user_id));


create policy "files_policy2"
on "public"."files"
as permissive
for all
to public
using ((auth.uid() = user_id));


create policy "global_job_templates_policy"
on "public"."global_job_templates"
as permissive
for all
to public
using ((auth.uid() = user_id));


create policy "Allow full access to service role"
on "public"."gui_components"
as permissive
for all
to service_role
using (true)
with check (true);


create policy "Allow read access for all authenticated users"
on "public"."gui_components"
as permissive
for select
to authenticated
using (true);


create policy "job_results_policy"
on "public"."job_results"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM jobs
  WHERE ((jobs.id = job_results.job_id) AND (jobs.user_id = auth.uid())))));


create policy "job_template_files_access_policy"
on "public"."job_template_files"
as permissive
for all
to public
using ((EXISTS ( SELECT 1
   FROM global_job_templates
  WHERE ((global_job_templates.id = job_template_files.job_template_id) AND (global_job_templates.user_id = auth.uid())))));


create policy "jobs_policy"
on "public"."jobs"
as permissive
for all
to public
using ((auth.uid() = user_id));


CREATE TRIGGER sync_category_files_trigger AFTER INSERT OR UPDATE ON public.categories FOR EACH ROW EXECUTE FUNCTION sync_category_files();

CREATE TRIGGER sync_job_template_files_trigger AFTER INSERT OR UPDATE ON public.global_job_templates FOR EACH ROW EXECUTE FUNCTION sync_job_template_files();

CREATE TRIGGER update_template_data_trigger BEFORE INSERT OR UPDATE OF commented_json, commented_vars ON public.global_job_templates FOR EACH ROW EXECUTE FUNCTION update_template_data();

CREATE TRIGGER trigger_gui_components_updated_at BEFORE UPDATE ON public.gui_components FOR EACH ROW EXECUTE FUNCTION update_gui_components_updated_at();


CREATE POLICY "Users have full access to their folder in cloud" ON storage.objects TO authenticated USING (((bucket_id = 'cloud'::text) AND ((auth.uid())::text = (storage.foldername(name))[1]))) WITH CHECK (((bucket_id = 'cloud'::text) AND ((auth.uid())::text = (storage.foldername(name))[1])));

CREATE POLICY "Users have full access to their folder in results" ON storage.objects TO authenticated USING (((bucket_id = 'results'::text) AND ((auth.uid())::text = (storage.foldername(name))[1]))) WITH CHECK (((bucket_id = 'results'::text) AND ((auth.uid())::text = (storage.foldername(name))[1])));
