-- Migration: Fix task timestamp batching issue
-- Date: 2025-06-01
-- Description: Adds automatic updated_at timestamp updates to prevent batching appearance
-- 
-- Problem: Tasks were completing individually but showing identical timestamps,
-- creating the appearance of batch completion instead of real-time individual completion.
--
-- Solution: Add database triggers to automatically update updated_at timestamps
-- on any row modification, ensuring accurate completion time tracking.

-- Clean up any existing duplicate functions first
DROP FUNCTION IF EXISTS update_updated_at_column() CASCADE;

-- <PERSON>reate function to update timestamp
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = CURRENT_TIMESTAMP;
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger for tasks table
DROP TRIGGER IF EXISTS update_tasks_updated_at ON tasks;
CREATE TRIGGER update_tasks_updated_at 
    BEFORE UPDATE ON tasks 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create trigger for jobs table  
DROP TRIGGER IF EXISTS update_jobs_updated_at ON jobs;
CREATE TRIGGER update_jobs_updated_at 
    BEFORE UPDATE ON jobs 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Update the RPC function to explicitly include timestamp updates
-- This provides redundancy in case triggers are disabled
DROP FUNCTION IF EXISTS public.set_task_status_with_check_and_lock;

CREATE OR REPLACE FUNCTION public.set_task_status_with_check_and_lock(
    taskid INT,
    oldstatus TEXT,
    newstatus TEXT,
    table_name TEXT
) RETURNS TEXT AS $$
DECLARE
    affected_rows TEXT;
    query TEXT;
BEGIN
    -- Dynamically construct the SELECT query with row locking
    query := format('SELECT status FROM public.%I WHERE id = $1 FOR NO KEY UPDATE NOWAIT', table_name);

    -- Execute the dynamic query
    EXECUTE query INTO affected_rows USING taskid;

    -- Check if the current status matches the expected old status
    IF affected_rows != oldstatus THEN
        RETURN 'Status mismatch';
    END IF;

    -- Dynamically construct the UPDATE query with explicit timestamp update
    -- This ensures timestamp is updated even if triggers are disabled
    query := format('UPDATE public.%I SET status = $1, updated_at = CURRENT_TIMESTAMP WHERE id = $2', table_name);

    -- Execute the dynamic UPDATE query
    EXECUTE query USING newstatus, taskid;

    RETURN 'Success';

EXCEPTION
    WHEN OTHERS THEN
        -- Return the exception message for debugging
        RETURN SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Grant execute permission to authenticated users
-- Note: These grants may already exist, but are included for completeness
GRANT EXECUTE ON FUNCTION public.set_task_status_with_check_and_lock(INT, TEXT, TEXT, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION public.update_updated_at_column() TO authenticated;

-- Add comments for documentation
-- Note: Comments are optional and may not be preserved in all environments
COMMENT ON FUNCTION update_updated_at_column() IS 'Automatically updates updated_at timestamp on row modifications';
COMMENT ON TRIGGER update_tasks_updated_at ON tasks IS 'Ensures updated_at is set on task status changes';
COMMENT ON TRIGGER update_jobs_updated_at ON jobs IS 'Ensures updated_at is set on job status changes';
COMMENT ON FUNCTION set_task_status_with_check_and_lock(INT, TEXT, TEXT, TEXT) IS 'Atomically updates task status with timestamp and status validation';
