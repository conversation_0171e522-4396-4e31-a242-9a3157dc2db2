-- Migration to populate the log_fields table with initial data from logs_in_database.json

-- Time group
INSERT INTO "public"."log_fields" ("group_name", "field_data") VALUES
('time', '[
    {
        "field": "T_NANOS",
        "caption": "Nano-Seconds since GPS-Epoch 0",
        "precision": "i64",
        "settings": [
            {"name":"cols", "data": ["GPS_NanoSeconds"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%19ll"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "T_SEC",
        "caption": "Seconds since GPS-Epoch 0",
        "precision": "f64",
        "settings": [
            {"name":"cols", "data": ["GPS_Seconds"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%17.6f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "T_WEEK_SOW",
        "caption": "GPS Week, Seconds of the Week",
        "precision": "f64",
        "settings": [
            {"name":"cols", "data": ["GPS_Week","GPS_SOW"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%04d","%13.6f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "T_YMDHMS",
        "caption": "Date and Time",
        "precision": "f32",
        "settings": [
            {"name":"cols", "data": ["Year","Month","Day","Hour","Minute","Second"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%04d","%2d","%2d","%2d","%2d","%9.6f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "T_SEC_SINCE_START",
        "caption": "Seconds since data set start",
        "precision": "f64",
        "settings": [
            {"name":"cols", "data": ["SecSinceStart"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%13.6f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "T_Y_DOY_SOD",
        "caption": "Year, day of year, seconds of the day",
        "precision": "f64",
        "settings": [
            {"name":"cols", "data": ["Year","DoY","SOD"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%04d","%03d","%12.6f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "T_WEEK_DOW_SOD",
        "caption": "GPS Week, Day of the week, seconds of the day",
        "precision": "f64",
        "settings": [
            {"name":"cols", "data": ["Year","DoW","SOD"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%04d","%1d","%12.6f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    }
]');

-- Position group
INSERT INTO "public"."log_fields" ("group_name", "field_data") VALUES
('position', '[
    {
        "field": "LLH_DEG",
        "caption": "Latitude [°] / Longitude [°] / ell. Height [m]",
        "precision": "f64",
        "settings": [
            {"name":"cols_ps", "data": ["","","Latitude","Longitude","h_ell"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%14.9f","%14.9f","%10.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "LLH_DMS",
        "caption": "Latitude[dms] / Longitude[dms] / ell. Height [m]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","Lat_deg","Lat_min","Lat_sec","Lon_deg","Lon_min","Lon_sec","h_ell"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%3d","%2d","%9.6f","%4d","%2d","%9.6f","%10.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "XYZ",
        "caption": "ECEF cartesian XYZ [m]",
        "precision": "f64",
        "settings": [
            {"name":"cols_ps", "data": ["","","ECEF_X","ECEF_Y","ECEF_Z"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%14.5f","%14.5f","%14.5f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "SIGMA_NED",
        "caption": "Position Uncertainty in North/East/Height [m]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","Sig_North","Sig_East","Sig_Height"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%8.4f","%8.4f","%8.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "SIGMA_FRD",
        "caption": "Position Uncertainty in Vehicle-Front/Right/Vertical [m]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","Sig_Front","Sig_Right","Sig_Vert"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%8.4f","%8.4f","%8.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "SIGMA_XYZ",
        "caption": "Position Uncertainty ECEF cartesian XYZ [m]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","Sig_X","Sig_Y","Sig_Z"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%8.4f","%8.4f","%8.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    }
]');

-- Velocity group
INSERT INTO "public"."log_fields" ("group_name", "field_data") VALUES
('velocity', '[
    {
        "field": "V_NED",
        "caption": "North/East/Down velocity [m/s]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","vNorth","vEast","vDown"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%10.4f","%10.4f","%10.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "V_ENU",
        "caption": "East/North/Up velocity [m/s]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","vEast","vNorth","vUp"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%10.4f","%10.4f","%10.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "V_FRD",
        "caption": "Vehicle-Front/Right/Down velocity [m/s]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","vFront","vRight","vDown"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%10.4f","%10.4f","%10.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "V_RFU",
        "caption": "Vehicle-Right/Front/Up velocity [m/s]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","vRight","vFront","vUp"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%10.4f","%10.4f","%10.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "V_XYZ",
        "caption": "ECEF cartesian velocity X/Y/Z [m/s]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","vX","vY","vZ"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%10.4f","%10.4f","%10.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "SIGMA_VXYZ",
        "caption": "Velocity Uncertainty ECEF cartesian [m/s]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","Sig_vX","Sig_vY","Sig_vZ"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%8.4f","%8.4f","%8.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "SIGMA_VFRD",
        "caption": "Velocity Uncertainty in Vehicle-Front/Right/Vertical [m/s]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","Sig_vFront","Sig_vRight","Sig_vVert"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%8.4f","%8.4f","%8.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "SIGMA_VNED",
        "caption": "Velocity Uncertainty in North/East/Height [m/s]",
        "precision": "f32",
        "settings": [
            {"name":"cols_ps", "data": ["","","Sig_vNorth","Sig_vEast","Sig_vHeight"], "gui": {"component_id": "logsetting_cols_ps"}},
            {"name":"ecc_FRD", "data": [0.0,0.0,0.0], "gui": {"component_id": "logsetting_ecc_FRD"}},
            {"name":"ascii_format", "data": ["%8.4f","%8.4f","%8.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    }
]');

-- Attitude group
INSERT INTO "public"."log_fields" ("group_name", "field_data") VALUES
('attitude', '[
    {
        "field": "RPY_DEG",
        "caption": "Roll / Pitch / Heading [°]",
        "precision": "f64",
        "settings": [
            {"name":"cols", "data": ["Roll","Pitch","Heading"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%9.4f","%9.4f","%9.4f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "RPY",
        "caption": "Roll / Pitch / Heading [rad]",
        "precision": "f64",
        "settings": [
            {"name":"cols", "data": ["Roll","Pitch","Heading"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%9.6f","%9.6f","%9.6f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    }
]');

-- Gravity group (empty in the original data)
INSERT INTO "public"."log_fields" ("group_name", "field_data") VALUES
('gravity', '[]');

-- Sensor DGNSSX group
INSERT INTO "public"."log_fields" ("group_name", "field_data") VALUES
('sensor_DGNSSX', '[
    {
        "field": "BL",
        "caption": "Effective Baseline Length [km]",
        "precision": "f32",
        "settings": [
            {"name":"cols", "data": ["BaselineLength"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%7.2f"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    }
]');

-- Sensor V group
INSERT INTO "public"."log_fields" ("group_name", "field_data") VALUES
('sensor_V', '[
    {
        "field": "SCF",
        "caption": "Estimated Scale Factor [ticks/m]",
        "precision": "f32",
        "settings": [
            {"name":"cols", "data": ["Odo_Scale"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%9g"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    }
]');

-- Sensor POSLEV group (empty in the original data)
INSERT INTO "public"."log_fields" ("group_name", "field_data") VALUES
('sensor_POSLEV', '[]');

-- Sensor ODO group (empty in the original data)
INSERT INTO "public"."log_fields" ("group_name", "field_data") VALUES
('sensor_ODO', '[]');

-- IMU_N group
INSERT INTO "public"."log_fields" ("group_name", "field_data") VALUES
('imu_n', '[
    {
        "field": "ACC_BIAS",
        "caption": "Accelerometer Bias in IMU-frame [m/s^2]",
        "precision": "f32",
        "settings": [
            {"name":"cols", "data": ["AccB_x","AccB_y","AccB_z"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%9g","%9g","%9g"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    },
    {
        "field": "ACC_BIAS:STDFULL",
        "caption": "Accelerometer Bias uncertainty in IMU-frame [m/s^2]",
        "precision": "f32",
        "settings": [
            {"name":"cols", "data": ["Sig_AccB_x","Sig_AccB_y","Sig_AccB_z"], "gui": {"component_id": "logsetting_cols"}},
            {"name":"ascii_format", "data": ["%7g","%7g","%7g"], "gui": {"component_id": "logsetting_ascii_format"}}
        ]
    }
]');