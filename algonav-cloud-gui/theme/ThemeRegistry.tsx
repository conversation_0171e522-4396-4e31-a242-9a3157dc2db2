'use client';

import { ThemeProvider, CssBaseline } from '@mui/material';
import { AppRouterCacheProvider } from '@mui/material-nextjs/v14-appRouter';
import theme from './theme';

export default function ThemeRegistry({ children, fontFamily }: { children: React.ReactNode, fontFamily: string }) {
  const customTheme = theme(fontFamily);
  return (
    <AppRouterCacheProvider>
      <ThemeProvider theme={customTheme}>
        <CssBaseline />
        {children}
      </ThemeProvider>
    </AppRouterCacheProvider>
  );
}