# Bulk Job Creation Refactor Plan

## Analysis Summary

*   The current bulk job creation logic resides in `/api/jobs/bulk/kml/route.ts`.
*   It currently aggregates specific output file IDs (`tix4kml`) from selected tasks into the `workervars` field of the new bulk job.
*   It does **not** aggregate the original `vars` from the individual tasks as required by the new specification.
*   The `vars` column in the `jobs` table is `jsonb`.
*   The new requirement is to aggregate original task variables into the main `vars` field (merged with template variables) and aggregate output file IDs into the `workervars` field, both using a new array-based structure with `links: ["bulk"]`.

## Relevant Files

*   **File to be Modified:** `algonav-cloud-gui/app/api/jobs/bulk/kml/route.ts`
*   **Reviewed for Context:**
    *   `algonav-cloud-gui/components/Job.tsx` (Handles task display and selection trigger)
    *   `algonav-cloud-gui/lib/hooks/useJobHooks.ts` (Manages task selection state)

## Revised Plan

1.  **Modify API Route (`algonav-cloud-gui/app/api/jobs/bulk/kml/route.ts`):**
    *   **Fetch Template Data:** Fetch the job template (ID 19) including its `vars` and `template_data`.
    *   **Fetch Enhanced Task Data:** Fetch the selected tasks (`jobs` table) including their `id`, `vars`, and `job_results`.
    *   **Aggregate Original Variables:**
        *   Initialize a structure (e.g., `Map<string, any[]>`) for aggregated original variables.
        *   Iterate through each fetched task and its `vars`.
        *   Append the `data` for each variable to the corresponding array in the aggregation structure, keyed by `name`.
    *   **Aggregate Output File IDs:**
        *   Initialize a structure (e.g., `Map<string, number[]>`) for aggregated output file IDs.
        *   Iterate through each fetched task and its `job_results`.
        *   Determine the variable name (e.g., `outputfile_<file_type>`).
        *   Append the `job_results.id` to the corresponding array in the aggregation structure.
    *   **Construct New `vars` Payload:**
        *   Start with the variables fetched from the template (`template.vars`).
        *   Format the aggregated original variables into the new structure: `[{ name: varName, links: ["bulk"], data: aggregatedDataArray }, ...]`.
        *   Merge the formatted aggregated variables with the template variables using the `mergeVars` function (adjust if needed for precedence).
    *   **Construct New `workervars` Payload:**
        *   Create an empty array for the `workervars` payload.
        *   Iterate through the aggregated output file ID structure.
        *   For each `name`, create an object `{ name: outputVarName, links: ["bulk"], data: aggregatedFileIdArray }` and add it to the `workervars` array.
    *   **Refactor Existing Logic:**
        *   Remove the old `taskResultData` generation.
        *   Remove the old `workerVars` object creation (`{ name: "TASK_RESULT", ... }`).
        *   Remove the static variable definitions (`varsArray`).
    *   **Update Database Insertion:** Modify the `supabase.from('jobs').insert(...)` call to use the newly constructed `vars` (merged template + aggregated original task vars) and the new `workervars` (aggregated file IDs in the new format).

2.  **Revised Mermaid Diagram:**

    ```mermaid
    sequenceDiagram
        participant Client
        participant API Route (/api/jobs/bulk/kml)
        participant Supabase DB

        Client->>+API Route: POST /api/jobs/bulk/kml (taskIds)
        API Route->>+Supabase DB: Fetch template (SELECT vars, template_data FROM global_job_templates WHERE id=19)
        Supabase DB-->>-API Route: Return template data (templateVars)
        API Route->>+Supabase DB: Fetch tasks (SELECT id, vars, job_results FROM jobs WHERE id IN (...))
        Supabase DB-->>-API Route: Return tasks data
        API Route->>API Route: Aggregate original task 'vars' (aggOrigVars: name -> [data1, ...])
        API Route->>API Route: Aggregate 'job_results' IDs (aggFileIds: outputfile_type -> [id1, ...])
        API Route->>API Route: Format aggOrigVars into bulk structure (formattedOrigVars: [{name:..., links:["bulk"], data:...}, ...])
        API Route->>API Route: Merge templateVars and formattedOrigVars into final 'vars' payload (finalVars)
        API Route->>API Route: Format aggFileIds into final 'workervars' payload (finalWorkervars: [{name:..., links:["bulk"], data:...}, ...])
        API Route->>+Supabase DB: Insert new bulk job (INSERT INTO jobs (..., vars, workervars, ...) VALUES (..., finalVars, finalWorkervars, ...))
        Supabase DB-->>-API Route: Return new bulk job ID
        API Route->>+Supabase DB: Insert relationships (INSERT INTO bulk_job_tasks (bulk_job_id, task_id))
        Supabase DB-->>-API Route: Confirm relationships insertion
        API Route-->>-Client: Response { success: true, id: newbulkTaskId }