INSERT INTO "public"."global_job_templates" ("id", "user_id", "name", "description", "template_data", "vars", "commented_json", "commented_vars") VALUES ('1', '85e9cf80-d9fa-4487-a188-05dcf85aecfc', 'Road Vehicle: tightly GNSS+IMU (NetworkPPK)', null, '{"process": [{"app": "MOD_GNSS_OBS_READER", "args": {"output": "~>GNSSRAW_ROVER", "rnxfile": "$inputfile_rinex_rover$"}, "name": "Read_Rover_Observations"}, {"app": "MOD_GNSS_OBS_READER", "args": {"dt": "$BASESTATION_DT$", "output": "~>GNSSRAW_BASE", "rnxfile": "$inputfileA_rinex_base$"}, "name": "Read_Base_Observations"}, {"app": "MOD_GNSS_PREC_READER", "args": {"type": "$PRECISE_FILES_TYPE$", "clkfiles": "$inputfileA_clk$", "sp3files": "$inputfileA_sp3$", "out_clocks": "~>GNSS_CLOCKS", "out_orbits": "~>GNSS_ORBITS"}, "name": "Read_Precise_Files"}, {"app": "MOD_GNSS_ANTEX_READER", "args": {"output": "~>ANTEX", "in_rawobs": "<~GNSSRAW_ROVER", "sat_antex_file": "$inputfile_atx_satellites$"}, "name": "Read_ANTEX"}, {"app": "MOD_DGNSS_ESC_SELECT", "args": {"in_clocks": "<~GNSS_CLOCKS", "in_orbits": "<~GNSS_ORBITS", "enable_gnss": "$GNSSES$", "out_esc_base": "~~>ESC_BASE@base", "disable_gprns": [], "out_esc_rover": "~>ESC_ROVER", "in_rawobs_base": "<~~GNSSRAW_BASE@base", "in_rawobs_rover": "<~GNSSRAW_ROVER", "freqs_priority_lists": {"C": [[2], [7, 6]], "E": [[1], [5, 7, 8]], "G": [[1], [2, 5]], "R": [[1], [2]]}}, "name": "DGNSS_ESC_Selection"}, {"app": "MOD_GNSS_PREPARE", "args": {"in_esc": "<~ESC_ROVER", "out_pp": "~>PP_ROVER", "out_spp": "~>SPP_ROVER", "in_antex": "<~ANTEX", "settings": {"rco_factor": 0.0, "switch_rcc": 1.0, "show_charts": true, "elev_mask_deg": 5.0, "switch_pcosat": 0.0, "switch_pwusat": 0.0, "switch_ztddry": 1.0, "tof_iterations": 2, "use_if_for_spp": false, "spp_ot_thresh_m": 50.0, "spp_min_num_sats": 5, "csd_thresh_meters": 0.05, "pivot_ignore_av_obs": true, "set_outliers_to_nan": true, "pivot_elev_bonus_deg": 1.0, "min_sun_sat_angle_deg": 0.0, "pivot_min_elevation_deg": 10.0, "correct_codephase_with_rcb": 1.0, "correct_codephase_with_scb": 1.0, "disable_phase_during_eclipse": false, "spp_ot_thresh_m_apriori_perep": 1000.0}, "in_clocks": "<~GNSS_CLOCKS", "in_orbits": "<~GNSS_ORBITS", "in_rawobs": "<~GNSSRAW_ROVER", "base_station_mode": false}, "name": "Prepare-Rover"}, {"app": "MOD_GNSS_PREPARE", "args": {"in_esc": "<~ESC_BASE", "out_pp": "~>PP_BASE", "out_spp": "~>SPP_BASE", "in_antex": "<~ANTEX", "settings": {"dNED": [0.0, 0.0, 0.0], "dXYZ": [-0.5295, 0.6149, 0.3656], "rco_factor": 0.0, "switch_rcc": 1.0, "show_charts": true, "elev_mask_deg": 5.0, "switch_pcosat": 0.0, "switch_pwusat": 0.0, "switch_ztddry": 1.0, "tof_iterations": 2, "use_if_for_spp": false, "spp_ot_thresh_m": 50.0, "spp_min_num_sats": 5, "csd_thresh_meters": 0.05, "set_outliers_to_nan": true, "base_station_arp_xyz": "$ARP_XYZ$", "min_sun_sat_angle_deg": 0.0, "correct_codephase_with_rcb": 1.0, "correct_codephase_with_scb": 1.0, "disable_phase_during_eclipse": false, "spp_ot_thresh_m_apriori_perep": 100.0}, "in_clocks": "<~GNSS_CLOCKS", "in_orbits": "<~GNSS_ORBITS", "in_rawobs": "<~GNSSRAW_BASE", "base_station_mode": true}, "name": "Prepare-Base"}, {"app": "MOD_SDCSYN_EXT_TT", "args": {"output": "~>ODODATA", "sdf_tt": "<~SPP_ROVER.T", "const_data": [0.0, 0.0, 0.0], "dt_seconds": 1.0, "field_out_tt": "T", "field_out_data": "ticks"}, "name": "Synth-Odo-Generator"}, {"app": "MOD_SDC_FROM_TIX", "args": {"output": "~>IMUDATA", "input_file": "$inputfile_tix_imu$"}, "name": "Read-IMU-from-TIX"}, {"app": "MOD_STARLING", "args": {"outputs": [{"name": "EKFLog", "sinks": ["~>TC_EKF"], "fields": ["i64|T", "u16|T_WEEK", "f64|T_GPSSOW", "d|LLH", "d|V_NED", "d|V_FRD", "d|RPY", "d|COG", "f32|IMUN:ACC_BIAS", "f32|IMUN:ACC_BIAS:STDFULL", "f32|IMUN:GYR_BIAS", "f32|IMUN:GYR_BIAS:STDFULL", "f32|dgnssX:FX", "f32|dgnssX:FXWL", "f32|dgnssX:ELEV", "f32|dgnssX:AMB:NAN", "f32|dgnssX:AMB:STDFULL", "f32|dgnssX:TRO:NAN", "f32|dgnssX:TRO:STDFULL", "f32|dgnssX:ION:NAN", "f32|dgnssX:ION:STDFULL", "u32|dgnssX:NUM_OBS_ACCUMULATED", "u32|dgnssX:NUM_IN_ACCUMULATED", "u32|dgnssX:NUM_OUT_ACCUMULATED", "u8 |dgnssX:NUM_OBS_CURRENT", "u8 |dgnssX:NUM_IN_CURRENT", "u8 |dgnssX:NUM_OUT_CURRENT", "u16|dgnssX:BLOCKID", "f32|cons:RPY", "f32|cons:RPY:STDFULL", "f32|cons:LEV", "f32|cons:LEV:STDFULL", "f32|cons:OMEGA", "f32|cons:OMEGA:STDFULL", "f32|POSN:STDFULL", "f32|VELN:STDFULL", "f32|ATTN:STDFULL"], "sensor": "dgnssX", "trigger": "EKF_AFTER_CLEANUP"}, {"dt": "$USERLOG.dt$", "name": "RTSLog_for_CSV", "sinks": ["~>TC_RTS_FULL"], "fields": "$USERLOG.fields$", "trigger": "$USERLOG.trigger$"}, {"dt": "$KML_DATARATE_DT$", "name": "RTSLog_for_KML", "sinks": ["~>TC_RTS4KML"], "fields": ["d|LLH"], "trigger": "RTS_TIME"}, {"name": "GNSS-Info-and-Stats", "sinks": ["~>DGNSS_STATS"], "fields": ["i32|dgnssX:GPRNS", "i32|dgnssX:NUMFREQS", "i32|dgnssX:GNSSES", "i32|dgnssX:CHANNELS", "i32|dgnssX:FREQS", "f64|dgnssX:FREQSHZ", "f64|dgnssX:LAMBDA", "u32|dgnssX:NUM_OBS_ACCUMULATED", "u32|dgnssX:NUM_IN_ACCUMULATED", "u32|dgnssX:NUM_OUT_ACCUMULATED", "u32|dgnssX:NUM_STUCK_ACCUMULATED"], "sensor": "dgnssX", "trigger": "EKF_AFTER_PASS"}], "sensors": [{"mvrs": {}, "name": "dgnssX", "type": "DGNSSX", "in_spp": "<~SPP_ROVER", "states": ["$GNSS_LEVERARM_FRD$"], "settings": {"ot_code": {"Huber_k": ["$$", [["huberc", [3]]], [0.0, 0.7, 1.0, 1.2, 1.5, 2.0, 2.5, 3.0, 10.0]], "ot_threshold": ["$CODE_OT_STRICTNESS_LEVEL$->", 5.0, 4.0, 3.0, 2.5, 2.2, 1.9, 1.7], "MDB_inflation_max": 1000.0, "min_num_obs_to_keep": 1, "MDB_inflation_factor": ["$$", [["mdbc", [3]]], [0.0, 0.1, 0.15, 0.2, 0.3, 0.5, 0.7, 1.0]], "max_num_obs_to_remove": 9999, "variance_inflation_at_min": 10000.0}, "verbose": [20, 50, 54, 30], "amb_std0": 10000, "ion_std0": [0.5, 0.5, 0.5], "ot_phase": {"Huber_k": ["$$", [["huberp", [4]]], [0.0, 0.7, 1.0, 1.2, 1.5, 2.0, 2.5, 3.0, 10.0]], "ot_threshold": ["$PHASE_OT_STRICTNESS_LEVEL$->", 5.0, 4.0, 3.0, 2.5, 2.2, 1.9, 1.7], "MDB_inflation_max": 1000.0, "min_num_obs_to_keep": 0, "MDB_inflation_factor": ["$$", [["mdbp", [3]]], [0.0, 0.1, 0.15, 0.2, 0.3, 0.5, 0.65, 0.8, 0.9, 1.0]], "max_num_obs_to_remove": 9999, "variance_inflation_at_min": 1.0}, "tro_std0": 0.003, "ion_estax": 0, "ion_sqrtq": 0.001, "ot_fixing": {"ot_threshold": 3.0, "all_or_nothing": false, "min_num_obs_to_keep": 0, "max_num_obs_to_remove": 9999}, "std0_code": "$CODE_OBSERVATION_NOISE$", "tec_sqrtq": "$IONOSPHERE_SYSTEMNOISE$", "tro_estax": "$TROPOSPHERE_ESTAX$", "tro_sqrtq": "$TROPOSPHERE_SYSTEMNOISE$", "ambrts_slc": {"sinks": ["~>DGNSS_AMBRTS"]}, "fix_mu_max": ["$$", [["mumax", [5]]], [0.5, 0.6, 0.7, 0.8, 0.9, 1.0]], "fix_ncands": 2, "fix_par_P0": 0.995, "fixing_slc": {"name": "FX_MANUALNAME", "sinks": ["~>DGNSS_FX"]}, "std0_phase": "$PHASE_OBSERVATION_NOISE$", "fix_use_par": false, "phase_usage": {"C": "$FIXING_STRATEGY$", "E": "$FIXING_STRATEGY$", "G": "$FIXING_STRATEGY$", "R": "FLOAT2"}, "Huber_thresh": 0.1, "Huber_maxiter": 4, "disable_phase": "$DISABLE_PHASE$", "elev_mask_deg": ["$$", [["elm", [0]]], [10, 12.5, 15, 17.5, 20.0]], "disable_gnsses": [], "iono_mm_per_km": ["$$", [["ionostd", [9]]], [0.0, 0.25, 0.5, 0.75, 1.0, 1.5, 2.0, 3.0, 5.0, 10.0, 100.0]], "only_wl_fixing": false, "adaptivity_code": {"enabled": true, "max_factor": 5.0, "min_factor": 0.2, "attenuation": 0.001, "convergence": 0.65, "max_residual": 10.0, "min_residual": 0.1}, "fix_max_pdop_sq": 12.0, "adaptivity_phase": {"enabled": false, "max_factor": 3.0, "min_factor": 0.33, "attenuation": 0.0005, "convergence": 0.65}, "enable_wl_fixing": ["$$", [["wlfx", [0]]], [false, true]], "fix_min_num_sats": "$AMBFIX_MIN_NUMSAT$", "fix_pot_threshold": 10.0, "prioritize_rts_fk": true, "rts_fixing_always": false, "rts_fixing_passes": [], "ddbias_window_size": ["$$", [["ddbws", [4]]], [0, 10, 100, 300, 1000, 3000]], "min_num_sat_noskip": ["$$", [["nnoskip", [0]]], [1, 2, 3, 4, 5, 6]], "fix_pseudo_variance": 0.0001, "phase_ot_for_reinit": 0.0, "elev_mask_fixing_deg": ["$$", [["elmfx", [0]]], [10, 12.5, 15, 17.5, 20.0]], "fix_validation_params": ["$AMBIGUITY_FIXING_STRICTNESS_LEVEL$->", [2.0, 10000000000, 3.0, 10000000000, 10000000000, 10000000000, 10000000000, 10000000000, 3.0, 0.05, 10000000000, 10000000000], [1.6, 10000000000, 2.6, 10000000000, 10000000000, 10000000000, 10000000000, 10000000000, 2.5, 0.04, 10000000000, 10000000000], [1.3, 10000000000, 2.3, 10000000000, 10000000000, 10000000000, 10000000000, 10000000000, 2.0, 0.035, 10000000000, 10000000000], [1.0, 10000000000, 2.0, 10000000000, 10000000000, 10000000000, 10000000000, 10000000000, 1.5, 0.03, 10000000000, 10000000000], [0.8, 10000000000, 1.8, 10000000000, 10000000000, 10000000000, 10000000000, 10000000000, 1.3, 0.025, 10000000000, 10000000000], [0.7, 10000000000, 1.5, 10000000000, 10000000000, 10000000000, 10000000000, 10000000000, 1.1, 0.02, 10000000000, 10000000000], [0.6, 10000000000, 1.2, 10000000000, 10000000000, 10000000000, 10000000000, 10000000000, 1.0, 0.016, 10000000000, 10000000000]], "lockindetector_enable": true, "lockindetector_thresh": 20.0, "inflation_for_few_sats": [], "noise_mapping_exponent": 1.0, "validation_required_wl": ["$$", [["usewifwl", [1]]], [false, true]], "adaptivity_min_num_sats": 8, "immediate_fixing_always": true, "immediate_fixing_passes": [], "otar_position_inflation": [100.0, 2.0], "validation_required_ftp": ["$$", [["usewif", []]], [true, false]], "validation_required_wif": ["$$", [["usewif", [0]]], [false, true]], "allowed_fixcand_removals": 1, "ddbias_max_residual_code": 0.005, "fix_shrink_hightolerance": false, "fix_validation_params_wl": [1.3, 10000000000, 2.3, 10000000000, 10000000000, 10000000000, 10000000000, 10000000000, 2.0, 0.08, 10000000000, 0.2], "lockindetectorAB_max_rms": 4.0, "lockindetector_fact_dist": 1.0, "amb_reinit_on_fix_outlier": 2, "ddbias_max_residual_phase": 0.0, "validation_include_wl_obs": false, "lockindetector_min_distance": 10.0, "outliertesting_min_num_sats": 0, "enable_ionosphere_estimation": "$IONOSPHERE_ESTIMATION$", "lockindetectorAB_min_num_sat": 5, "lockindetector_max_spp_stdev": 2.0, "validation_include_float_obs": false, "amb_sigma_after_downgrade_cyc": 0.01, "ambiguity_stuckness_threshold": ["$$", [["stucky", [6]]], [0.0, 3.0, 4.0, 5.0, 7.5, 10, 15]], "phase_outlier_immediate_reinit": true, "store_accumulated_fix_knowledge": true, "store_ambiguity_upon_outlier_reset": true}, "in_pp_rover": "<~PP_ROVER", "in_esc_rover": "<~ESC_ROVER", "colin_pp_base": "<~~PP_BASE@base", "colin_esc_base": "<~~ESC_BASE@base"}, {"rpy": "$CONS_RPY$", "name": "cons", "type": "V", "input": "<~...', '{"vars": [{"data": "./ATX/igs20.atx", "name": "inputfile_atx_satellites"}, {"data": ["dummy!"], "name": "inputfileA_rinex_base", "links": ["base"]}, {"data": true, "name": "DEMO_OUTPUTS"}, {"data": false, "name": "DEV_OUTPUTS"}, {"data": false, "name": "FORWARD_TC"}, {"data": 2, "name": "PASSES_TC"}, {"gui": {"group": "Differential GNSS", "items": [{"label": "GPS", "value": "G"}, {"label": "Galileo", "value": "E"}, {"label": "GLONASS", "value": "R"}, {"label": "Beidou", "value": "C"}], "label": "Select Constellations:", "order": 100, "tooltip": "select all constellations to be included in the differential GNSS processing. At least one constellation is required!", "max_checked": 999, "min_checked": 1, "component_id": "CheckboxGroup", "msg_below_min": "Please select at least one constellation!"}, "data": ["G", "E", "R"], "name": "GNSSES"}, {"gui": {"group": "Differential GNSS", "items": [{"label": "Single-Frequency Float", "value": "FLOAT1"}, {"label": "Single-Frequency with Ambiguity Fixing", "value": "FIX1"}, {"label": "Double-Frequency Float", "value": "FLOAT2"}, {"label": "Double-Frequency with Ambiguity Fixing", "value": "FIX2"}], "label": "Select the carrier-phase processing type:", "order": 101, "tooltip": "Select, how carrier phase observations will be used for the positioning.", "component_id": "RadioButtons"}, "data": "FIX2", "name": "FIXING_STRATEGY"}, {"gui": {"group": "Differential GNSS", "label": "Disable all Carrier-Phase observations", "order": 102, "tooltip": "Carrier phase observations heavily increase GNSS positioning accuracy and precision. Only disable, if you know what you're doing!", "component_id": "Checkbox"}, "data": false, "name": "DISABLE_PHASE"}, {"gui": {"group": "Differential GNSS", "label": "Enable Ionosphere Estimation", "order": 103, "tooltip": "Suggested for larger baselines (single base station: @baselines >5 km, network of base stations: at avg. baselines >30 km)", "component_id": "Checkbox"}, "data": false, "name": "IONOSPHERE_ESTIMATION"}, {"gui": {"group": "Differential GNSS", "items": [{"label": "no variation", "value": 0}, {"label": "very low", "value": 0.002}, {"label": "low", "value": 0.005}, {"label": "average", "value": 0.01}, {"label": "high", "value": 0.02}, {"label": "very high", "value": 0.04}, {"label": "extreme", "value": 0.1}], "label": "Expected level of ionospheric activity:", "component_id": "DiscreteSlider", "dependson_var": "IONOSPHERE_ESTIMATION"}, "data": 0.002, "name": "IONOSPHERE_SYSTEMNOISE"}, {"gui": {"group": "Differential GNSS", "items": [{"label": "Disabled", "value": 0}, {"label": "Zenith only", "value": 1}, {"label": "Zenith and horizontal gradients", "value": 7}], "label": "Tropospheric estimation:", "order": 104, "tooltip": "---", "component_id": "RadioButtons"}, "data": 0, "name": "TROPOSPHERE_ESTAX"}, {"gui": {"group": "Differential GNSS", "items": [{"label": "no variation", "value": 0}, {"label": "very low", "value": 0.000002}, {"label": "low", "value": 0.000005}, {"label": "average", "value": 0.00001}, {"label": "high", "value": 0.00002}, {"label": "very high", "value": 0.00005}, {"label": "extreme", "value": 0.0001}], "label": "Expected level of tropospheric activity (e.g. weather changes):", "component_id": "DiscreteSlider", "dependson_val": [1, 7], "dependson_var": "TROPOSPHERE_ESTAX"}, "data": 0.000002, "name": "TROPOSPHERE_SYSTEMNOISE"}, {"gui": {"unit": "m", "align": "right", "group": "Differential GNSS", "label": "Noise floor of double-differenced pseudorange observations", "order": 105, "integer": false, "tooltip": "As an orientation: low cost receivers can have a noise of up to 5 meters; geodetic receivers may reach 0.5 m", "multiply": 1, "max_value": 50, "min_value": 0.1, "max_digits": 2, "component_id": "NumberField"}, "data": 0.5, "name": "CODE_OBSERVATION_NOISE"}, {"gui": {"group": "Differential GNSS", "label": "Pseudorange outlier-testing:", "order": 106, "tooltip": "Suggested setting: Normal. Use a stricter setting, if large GNSS errors appear to show up in your results.", "component_id": "StrictnessSlider"}, "data": 3, "name": "CODE_OT_STRICTNESS_LEVEL"}, {"gui": {"unit": "mm", "align": "right", "group": "Differential GNSS", "label": "Noise floor of double-differenced phase observations", "order": 107, "integer": false, "tooltip": "As an orientation: low cost receivers (e.g. smartphones) can have a noise of up to 30 mm; geodetic receivers can reach 5 mm of noise floor, sometimes better", "multiply": 1000, "max_value": 100, "min_value": 1, "max_digits": 1, "component_id": "NumberField"}, "data": 0.007, "name": "PHASE_OBSERVATION_NOISE"}, {"gui": {"group": "Differential GNSS", "label": "Carrier-phase outlier-testing:", "order": 108, "tooltip": "Suggested setting: Normal. Use a stricter setting, if large GNSS errors appear to show up in your results.", "component_id": "StrictnessSlider"}, "data": 3, "name": "PHASE_OT_STRICTNESS_LEVEL"}, {"gui": {"group": "Differential GNSS", "label": "Phase Ambiguity Fixing Strictness:", "order": 120, "tooltip": "When fixing DGNSS phase ambiguities, there is always a trade-off between availability (of fixes) and their reliability. Suggested: Normal.", "component_id": "StrictnessSlider"}, "data": 3, "name": "AMBIGUITY_FIXING_STRICTNESS_LEVEL"}, {"gui": {"step": 1, "group": "Differential GNSS", "label": "Minimum number of satellites for ambiguity fixing:", "order": 121, "tooltip": "Having a larger minimum increases fixing robustness, but reduces the overall number of ambiguity fixes. Suggested for 1/2/3 fixable constellations: 4/6/7", "max_value": 15, "min_value": 4, "component_id": "IntegerSlider"}, "data": 6, "name": "AMBFIX_MIN_NUMSAT"}, {"gui": {"group": "Differential GNSS", "items": [{"label": "do not change", "value": 0}, {"label": " 1 second", "value": 1}, {"label": " 2 seconds", "value": 2}, {"label": " 5 seconds", "value": 5}, {"label": "10 seconds", "value": 10}, {"label": "15 seconds", "value": 15}, {"label": "20 seconds", "value": 20}, {"label": "30 seconds", "value": 30}, {"label": "60 seconds", "value": 60}], "label": "Downsample GNSS base station data:", "order": 150, "tooltip": "For testing, how much a lower (than the available) data rate will affect the DGNSS results.", "component_id": "DiscreteSlider"}, "data": 0, "name": "BASESTATION_DT"}, {"gui": {"axes": ["Front", "Right", "Down"], "name": "LEV", "unit": "m", "group": "IMU", "label": "IMU-GNSS lever arm", "order": 200, "max_value": 50, "min_value": -50, "max_digits": 3, "description": "Lever arm from the IMU center of observations to the GNSS antenna phase center. The IMU center of observations is always the (virtual) intersection point of the three accelerometers. The 3-D lever arm is defined along the principal vehicle coordinate axes Front/Right/Down. Note: the Down-component is <b>negative</b>, if the GNSS antenna is sitting <b>above</b> the IMU.", "component_id": "DynamicState", "process_type": "RC"}, "data": {"init": [0.365, 0.1289, -1.3], "name": "LEV", "std0": [0.01], "estax": [], "processes": {"type": "RC"}, "unified_uncertainty": true}, "name": "GNSS_LEVERARM_FRD"}, {"gui": {"group": "IMU", "order": 201, "component_id": "IMUAxisOrientation"}, "data": "FLU", "name": "IMU_AXIS_ORIENTATION"}, {"gui": {"unit": "m/s/sqrt(s)", "align": "right", "label": "Accelerometers' velocity random walk", "order": 202, "integer": false, "tooltip": "The velocity random walk reflects the noise floor of the used accelerometers. Manufacturers commonly specify this value in their spec sheet.", "multiply": 1, "max_value": 0.1, "min_value": 0.0000001, "component_id": "NumberField"}, "data": 0.001, "name": "IMU_VELOCITY_RANDOMWALK"}, {"gui": {"unit": "deg/sqrt(h)", "align": "right", "label": "Gyroscopes' angular random walk", "order": 203, "integer": false, "tooltip": "The angular random walk reflects the noise floor of the used gyroscopes. Manufacturers commonly specify this value in their spec sheet.", "multiply": 3437.74677, "max_value": 10000, "min_value": 0.0001, "max_digits": 4, "component_id": "NumberField"}, "data": 0.00002, "name": "IMU_ANGULAR_RANDOMWALK"}, {"gui": {"axes": ["IMU-X", "IMU-Y", "IMU-Z"], "name": "ACC_BIAS", "unit": "m/s²", "group": "IMU", "label": "Accelerometer Bias", "order": 204, "tooltip": "Leave the initial values zero, unless you have external knowledge (as from calibrations). The initial unceratinty and the characteristics of the time-variability (random walk) can be found in the IMU specifications.", "max_value": 1, "min_value": -1, "max_digits": 7, "description": "Biases for the three accelerometers (X/Y/Z axes as indicated on the IMU or in the IMU specifications!).", "component_id": "DynamicState", "process_type": "RW", "max_value_sqrt_q": 0.01, "min_value_sqrt_q": -0.01}, "data": {"init": [0, 0, 0], "name": "ACC_BIAS", "std0": [0.0001], "estax": [0, 1, 2], "processes": {"type": "RW", "sqrt_q": 0.00001}, "unified_uncertainty": true}, "name": "IMU_ACC_BIAS"}, {"gui": {"axes": ["IMU-X", "IMU-Y", "IMU-Z"], "name": "GYR_BIAS", "unit": "rad/s", "group": "IMU", "label": "Gyroscope Bias", "order": 205, "tooltip": "Leave the initial values zero, unless you have external knowledge (as from calibrations). The initial unceratinty and the characteristics of the time-variability (random walk) can be found in the IMU specifications.", "max_value": 0.1, "min_value": -0.1, "max_digits": 7, "description": "Biases for the three gyroscopes (X/Y/Z axes as indicated on the IMU or in the IMU specifications!).", "component_id": "DynamicState", "process_type": "RW", "max_value_sqrt_q": 0.001, "min_value_sqrt_q": -0.001}, "data": {"init": [-0.002091, 0.000617, -0.0026], "name": "GYR_BIAS", "std0": [0.00001], "estax": [0, 1, 2], "processes": {"type": "RW", "sqrt_q": 0.0000003}, "unified_uncertainty": true}, "name": "IMU_GYR_BIAS"}, {"gui": {"group": "Motion Constraint", "items": [{"label": "Zero lateral motion of rear axle", "value": 1}, {"label": "Zero vertical motion at vehicle center", "value": 2}], "label": "Select active motion constraints:", "order": 295, "tooltip": "---", "min_checked": 0, "component_id": "CheckboxGroup"}, "data": [1, 2], "name": "CONS_ACTIVE_AXES"}, {"gui": {"axes": ["DUMMY", "Lateral Zero-Motion", "Vertical Zero-Motion"], "unit": "%", "alig...', null, null), ('2', '85e9cf80-d9fa-4487-a188-05dcf85aecfc', 'Road Vehicle: tightly PPP+IMU', null, null, null, null, null), ('3', '85e9cf80-d9fa-4487-a188-05dcf85aecfc', 'Road Vehicle: loosely GNSS+IMU (NetworkPPK)', null, null, null, null, null), ('4', '85e9cf80-d9fa-4487-a188-05dcf85aecfc', 'Road Vehicle: loosely PPP+IMU', null, null, null, null, null), ('5', '85e9cf80-d9fa-4487-a188-05dcf85aecfc', 'Road Vehicle: GNSS only (Network-PPK)', null, null, null, null, null), ('6', '85e9cf80-d9fa-4487-a188-05dcf85aecfc', 'Road Vehicle: GNSS only (PPP)', null, null, null, null, null);