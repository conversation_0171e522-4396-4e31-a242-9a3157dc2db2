[

  { "name": "inputfile_atx_satellites"    ,   "data": "/mnt/d/tests/igs20.atx" },

  { "name": "OUTPUTFOLDER"      ,   "data": "./demo_outputs"         },
  { "name": "DEVOUTPUTFOLDER"   ,   "data": "./dev_outputs"          },
  { "name": "PRECISE_FILES_TYPE",   "data": "FINAL"                  },

  { "name": "DSNAME"         ,      "links"  : ["F"]           ,      "data": [
    "268a",
    "268b",
    "269a1",
    "269a2",
    "269b",
    "269c",
    "269d",
    "270a",
    "270b",
    "270c",
    "270d",
    "270e",
    "270f",
    "274a1",
    "274a2",
    "274b",
    "274c",
    "274d",
    "274e1",
    "274e2"
    ] },
    
  // hier nur als dummy, aber wichtig und von GUI zuy implementieren: Datensatzabhängiger Wert, der Zeitintervall einschränkt.
  { "name": "PROCESSING_FROM"         ,      "links"  : ["F"]           ,      "data": [ [  0],[  0], [         0],[1411288898],  [  0],[  0], [  0],[  0],[  0],[  0],[         0], [  0],[  0],  [         0],[1411717116],  [  0],[  0], [  0],[         0],[1411732831]] },  //  0 is in 6.1.1980
  { "name": "PROCESSING_UNTIL"        ,      "links"  : ["F"]           ,      "data": [ [2e9],[2e9], [1411288936],[1411291066],  [2e9],[2e9], [2e9],[2e9],[2e9],[2e9],[1411380352], [2e9],[2e9],  [1411717155],[       2e9],  [2e9],[2e9], [2e9],[1411732869],[       2e9]] },  //2e9 is in year 2043
  
  { "name": "INIT_HEADING_DEGRESS_FORWARD"    ,      "links"  : ["F"]      ,      "data":  [-165.062,  -39.216,    1.998,  -100.177,   -2.475, -169.024, -164.567,    3.735,  -84.978,  161.686,  71.406,    5.237,  -11.873,  -61.045,  -89.164,   65.281,  -34.462,   149.767,  -140.549,  92.569] },
  
  { "name": "INIT_HEADING_DEGRESS_REVERSE"    ,      "links"  : ["F"]      ,      "data":  [-38.615 ,    4.334,  -99.936,    -1.776, -168.297, -164.604,    3.925,  -85.717,  158.716,   71.114,  76.357,  -12.571,  153.129,  -88.609,   66.398,  -35.054,  149.723,  -142.716,    93.024,   6.145] },

  { "name": "ZUPT_INTERVALS",               "links": ["F"],  "data": 
  [
    [[1411208204.2],[1411209087.4],[1411209369.6],[1411209391.2],[1411209443.8],[1411209488.6],[1411209562.2],[1411209597.4],[1411209801.4],[1411209811.2],[1411210813.8],[1411210838.2],[1411210922.6],[1411211034.0],[1411211181.2],[1411211204.6],[1411211268.4],[1411211319.4],[1411211532.8],[1411211891.0]],
    [[1411228881.2],[1411229045.0],[1411229049.4],[1411229068.0],[1411229287.4],[1411229298.2],[1411229529.4],[1411229565.0],[1411230754.8],[1411230764.0],[1411230852.0],[1411230908.4],[1411231210.0],[1411231429.0]],
    [[1411285910.0],[1411286033.4],[1411286037.8],[1411286086.4],[1411286299.2],[1411286318.2],[1411286783.0],[1411286826.6],[1411286846.6],[1411286918.8],[1411287153.8],[1411287175.2],[1411287218.6],[1411287247.0],[1411287322.6],[1411287372.8],[1411287428.8],[1411287436.4],[1411287715.2],[1411287729.0],[1411287889.6],[1411287905.0],[1411288448.2],[1411288488.0],[1411288754.0],[1411288780.0],[1411288851.2],[1411288868.0],[1411288900.0],[1411288935.8]],
    [[1411288898.2],[1411288934.8],[1411288973.8],[1411288997.2],[1411289036.2],[1411289071.4],[1411289137.4],[1411289159.2],[1411289212.2],[1411289230.6],[1411289395.6],[1411289424.4],[1411289472.4],[1411289527.8],[1411289654.0],[1411289717.6],[1411290460.2],[1411290494.8],[1411291025.4],[1411291065.8]],
    [[1411291930.2],[1411292047.4],[1411292433.2],[1411292444.6],[1411292593.8],[1411292599.8],[1411292880.2],[1411292925.0],[1411293320.8],[1411293332.8],[1411293926.6],[1411293943.4],[1411294230.4],[1411294272.0],[1411294423.2],[1411294439.6],[1411294627.6],[1411294657.4],[1411294724.4],[1411294739.0],[1411294867.2],[1411294948.0]],
    [[1411294977.2],[1411294985.6],[1411295112.0],[1411295124.0],[1411295425.0],[1411295438.6],[1411295892.4],[1411295899.4],[1411295940.6],[1411295953.4],[1411296409.2],[1411296480.2],[1411296784.0],[1411296792.0],[1411296926.6],[1411296949.2],[1411297202.4],[1411297240.4],[1411297349.4],[1411297385.8],[1411297390.2],[1411298065.0]],
    [[1411298093.2],[1411298474.6],[1411298861.6],[1411298924.6],[1411298968.0],[1411298984.4],[1411299302.4],[1411299384.4],[1411299451.2],[1411299463.4],[1411299504.8],[1411299525.2],[1411299785.0],[1411299799.6],[1411300416.6],[1411300443.0],[1411300500.0],[1411300646.6],[1411300687.0],[1411300712.0],[1411301110.8],[1411301117.6],[1411301415.8],[1411301541.0]],
    [[1411367806.0],[1411367887.8],[1411368128.2],[1411368142.2],[1411368312.4],[1411368321.6],[1411368326.0],[1411368340.6],[1411368345.0],[1411368656.2],[1411368662.6],[1411368689.4],[1411369703.2],[1411369712.8],[1411370036.8],[1411370066.6],[1411370325.6],[1411370345.6],[1411370360.2],[1411370409.0],[1411370414.2],[1411370425.2],[1411370468.0],[1411370495.4],[1411370658.6],[1411370706.8],[1411370756.6],[1411370787.2],[1411371005.2],[1411371013.0],[1411371168.6],[1411371227.2],[1411371234.4],[1411371380.4],[1411371384.8],[1411371393.0]],
    [[1411371424.2],[1411371519.6],[1411371524.0],[1411371530.2],[1411372056.0],[1411372091.0],[1411373595.0],[1411373606.2],[1411374160.6],[1411374170.6],[1411374335.2],[1411374368.6],[1411374369.0],[1411374532.0],[1411374757.6],[1411374832.0]],
    [[1411374850.2],[1411374898.0],[1411374902.4],[1411375244.2],[1411375248.6],[1411375298.8],[1411375528.6],[1411375562.4],[1411375747.0],[1411375765.0],[1411375824.2],[1411375841.2],[1411376625.2],[1411376657.0],[1411377174.0],[1411377184.4],[1411377717.0],[1411377734.4],[1411378119.6],[1411378144.6],[1411378330.6],[1411378339.6],[1411378785.0],[1411378792.0],[1411378798.2],[1411378818.8],[1411378823.2],[1411378928.0]],
    [[1411379066.2],[1411379294.0]],
    [[1411400470.0],[1411400495.0],[1411400806.4],[1411400917.6],[1411401797.8],[1411401818.6],[1411401862.4],[1411401919.8]],
    [[1411407880.0],[1411408355.6],[1411409355.4],[1411409369.4],[1411410208.0],[1411410298.8]],
    [[1411715230.2],[1411715330.4],[1411715660.2],[1411715675.2],[1411716847.2],[1411716877.8],[1411716955.8],[1411716989.0],[1411717117.2],[1411717154.0]],
    [[1411717116.2],[1411717153.2],[1411717291.6],[1411717307.8],[1411717428.0],[1411717447.8],[1411717523.4],[1411717531.8],[1411717599.2],[1411717645.4],[1411717656.6],[1411717662.6],[1411717747.2],[1411717794.8],[1411717875.4],[1411717890.2],[1411718033.6],[1411718069.2],[1411718209.4],[1411718256.4],[1411718412.4],[1411718434.0],[1411718469.4],[1411718522.0],[1411718654.8],[1411718704.6],[1411718745.4],[1411718759.6],[1411718794.2],[1411718803.0],[1411718859.4],[1411718893.0],[1411718909.2],[1411718978.6],[1411719036.6],[1411719073.4],[1411719117.0],[1411719138.8],[1411719257.8],[1411719271.6],[1411719303.4],[1411719326.6],[1411719403.0],[1411719425.0],[1411719621.6],[1411719896.0]],
    [[1411719913.0],[1411720066.8],[1411720205.0],[1411720255.8],[1411720362.6],[1411720413.6],[1411720440.0],[1411720458.8],[1411721135.8],[1411721143.2],[1411721213.4],[1411721266.4],[1411721291.4],[1411721317.0],[1411721402.8],[1411721410.2],[1411721430.8],[1411721486.2],[1411721557.2],[1411721579.4],[1411722199.6],[1411722211.2],[1411722752.4],[1411722791.4],[1411722862.2],[1411722872.2],[1411722942.4],[1411723021.8]],
    [[1411723051.0],[1411723163.4],[1411723416.2],[1411723463.0],[1411723592.8],[1411723619.2],[1411723649.0],[1411723692.4],[1411723805.4],[1411723844.8],[1411723949.4],[1411723992.8],[1411724052.2],[1411724105.8],[1411724188.2],[1411724200.8],[1411724268.0],[1411724288.6],[1411724317.8],[1411724324.2],[1411724424.0],[1411724464.6],[1411724709.6],[1411724747.0],[1411724776.6],[1411724826.2],[1411724924.0],[1411724934.4],[1411724982.8],[1411725010.4],[1411725037.2],[1411725091.0],[1411725148.6],[1411725161.2],[1411725314.8],[1411725379.0],[1411725409.8],[1411725416.0],[1411725498.4],[1411725509.2],[1411725527.8],[1411725539.4],[1411725578.4],[1411725607.0],[1411725708.0],[1411725727.2],[1411725764.0],[1411725832.4],[1411725856.0],[1411725879.0],[1411726125.4],[1411726143.6],[1411726462.0],[1411726637.8]],
    [[1411726654.0],[1411726913.6],[1411726919.0],[1411726986.0],[1411727102.4],[1411727109.0],[1411727139.2],[1411727176.8],[1411727222.6],[1411727231.4],[1411727296.6],[1411727341.6],[1411727469.0],[1411727502.4],[1411727644.6],[1411727664.6],[1411727768.8],[1411727794.8],[1411727832.6],[1411727839.2],[1411727960.6],[1411727993.8],[1411728122.2],[1411728155.0],[1411728185.4],[1411728197.4],[1411728315.2],[1411728353.2],[1411728390.4],[1411728398.6],[1411728489.2],[1411728515.4],[1411728681.6],[1411728713.2],[1411728745.6],[1411728758.4],[1411728848.4],[1411728875.8],[1411729048.0],[1411729073.2],[1411729290.2],[1411729329.8],[1411729468.2],[1411729482.8],[1411729527.8],[1411729558.8],[1411729621.0],[1411729649.6],[1411729828.0],[1411729848.2],[1411729908.4],[1411729915.2],[1411730061.6],[1411730168.8]],
    [[1411730188.0],[1411730316.2],[1411731159.0],[1411731293.0],[1411731331.8],[1411731343.2],[1411731415.2],[1411731436.8],[1411731614.6],[1411731625.6],[1411731664.8],[1411731671.2],[1411731740.2],[1411731756.2],[1411732267.4],[1411732294.2],[1411732831.8],[1411732868.8]],
    [[1411732832.0],[1411732868.0],[1411733433.0],[1411733444.6],[1411733512.6],[1411733531.2],[1411733572.4],[1411733583.6],[1411733678.0],[1411733690.8],[1411735279.0],[1411735627.8]]
  ]
  },

  // Hinweis zu Zeitstempeln: es gibt mehrere Möglichkeiten diese anzugeben:
  // 1: INT64-Ganzzahl                    --> Absolute GPS-Zeit in Nanosekunden (seit 6.1.1980)
  // 2: [double] (also in einem Array!)   --> Absolute GPS-Zeit in Sekunden     (seit 6.1.1980)
  // 3: [int,double]                      --> GPS-Woche (intern: uint16), GPS Sekunden seit Wochenbeginn (Sonntag 00:00:00), Beispiel: [1234, 123456.789]
  // 4: [int,int,int]                     --> GPS-Woche (intern: uint16), Ganze GPS Sekunden seit Wochenbeginn (Sonntag 00:00:00), Nanosekunden.  Beispiel: [1234, 123456, 123456789] -> entspricht [1234, 123456.123456789]
  // 5: [yy,mm,dd,HH,MM,SS.ssss]          --> Kalendertag und Uhrzeit: Year/Month/Day/Hour/Minute als int, dann Sekunden als double.


  { "name"   : "inputfileA_clk" ,       "links"  : ["F"],     "data": [
      ["./CODE_final/COD0MGXFIN_20242680000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242680000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242690000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242690000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242690000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242690000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242690000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_30S_CLK.CLK"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_30S_CLK.CLK"]
      ]  
  },



  { "name"   : "inputfileA_sp3"   ,       "links"  : ["F"],     "data": [
      ["./CODE_final/COD0MGXFIN_20242680000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242680000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242690000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242690000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242690000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242690000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242690000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242700000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_05M_ORB.SP3"],
      ["./CODE_final/COD0MGXFIN_20242740000_01D_05M_ORB.SP3"]
  ] },





  // hier nur als Hinweis zu inputfile_* bzw. inputfileA_*, wie ein inputfile_tix_imu auch aussehen kann, sobald File-Variablen möglich sind:
  // aus:
  //  "./MTI/268a_IMU.TIX"
  //
  // wird dann:
  //   {
  //     "filename": "268a_IMU.TIX",
  //     "path"    : "./MTI",
  //     "somevar" : 1.0,
  //     "foo"     : [["bar"]]
  //   }


  { "name": "inputfile_tix_imu" ,       "links"  : ["F"]           ,      "data":  [
    "./MTI/268a_IMU.TIX",
    "./MTI/268b_IMU.TIX",
    "./MTI/269a_IMU.TIX",
    "./MTI/269a_IMU.TIX",
    "./MTI/269b_IMU.TIX",
    "./MTI/269c_IMU.TIX",
    "./MTI/269d_IMU.TIX",
    "./MTI/270a_IMU.TIX",
    "./MTI/270b_IMU.TIX",
    "./MTI/270c_IMU.TIX",
    "./MTI/270d_IMU.TIX",
    "./MTI/270e_IMU.TIX",
    "./MTI/270f_IMU.TIX",
    "./MTI/274a_IMU.TIX",
    "./MTI/274a_IMU.TIX",
    "./MTI/274b_IMU.TIX",
    "./MTI/274c_IMU.TIX",
    "./MTI/274d_IMU.TIX",
    "./MTI/274e_IMU.TIX",
    "./MTI/274e_IMU.TIX"
  ]   },


  { "name": "inputfile_rinex_rover" ,       "links"  : [["F",[0,1]]]           ,      "data":  [
    "./RoverChina_hinten_1Hz/268a.24o",
    "./RoverChina_hinten_1Hz/268b.24o",
    "./RoverChina_hinten_1Hz/269a.24o",
    "./RoverChina_hinten_1Hz/269a.24o",
    "./RoverChina_hinten_1Hz/269b.24o",
    "./RoverChina_hinten_1Hz/269c.24o",
    "./RoverChina_hinten_1Hz/269d.24o",
    "./RoverChina_hinten_1Hz/270a.24o",    
    "./RoverChina_hinten_1Hz/270b.24o", 
    "./RoverChina_hinten_1Hz/270c.24o",
    "./RoverChina_hinten_1Hz/270d.24o",
    "./RoverChina_hinten_1Hz/270e.24o",
    "./RoverChina_hinten_1Hz/270f.24o",
    "./RoverChina_hinten_1Hz/274a.24o",
    "./RoverChina_hinten_1Hz/274a.24o",
    "./RoverChina_hinten_1Hz/274b.24o",
    "./RoverChina_hinten_1Hz/274c.24o",
    "./RoverChina_hinten_1Hz/274d.24o",
    "./RoverChina_hinten_1Hz/274e.24o",
    "./RoverChina_hinten_1Hz/274e.24o"
  ]   },


  // Note: ARP_XYZ == [] will essentially not use this information (and instead, use the coordinate from the RINEX Header (APPROX_POSITION_XYZ))
  // Hier werden die SAPOS-Stationen normalerweise mit APPROX_POSITION_XYZ gefuettert, bei IGS FFMJ war das aber nicht möglich (die Koordinaten tatsächlich nur auf 10cm genau...) Daher nur für diese Station explizit in ETRF2014 (was nah an SAPOS/GREF sein sollte)
  { "name": "ARP_XYZ" ,       "links"  : ["F"]     ,      "data": [
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [],
      [4053456.1689,  617729.4333, 4869395.5142],    // Frankfurt-Daten mit IGS-Station FFMJ: mit DGNSS relativ zu Kloppenheim ermittelte Koordinate
      [4053456.1689,  617729.4333, 4869395.5142],    // Frankfurt-Daten mit IGS-Station FFMJ: mit DGNSS relativ zu Kloppenheim ermittelte Koordinate
      [4053456.1689,  617729.4333, 4869395.5142],    // Frankfurt-Daten mit IGS-Station FFMJ: mit DGNSS relativ zu Kloppenheim ermittelte Koordinate
      [4053456.1689,  617729.4333, 4869395.5142],    // Frankfurt-Daten mit IGS-Station FFMJ: mit DGNSS relativ zu Kloppenheim ermittelte Koordinate
      [4053456.1689,  617729.4333, 4869395.5142],    // Frankfurt-Daten mit IGS-Station FFMJ: mit DGNSS relativ zu Kloppenheim ermittelte Koordinate
      []
    ]
  },


  { "name": "inputfileA_rinex_base" ,       "links"  : ["F",["base",[]]]     ,      "data": [
      ["./SAPOS10/268a-0450.24o"],   // 268a   //  0
      ["./SAPOS10/268b-0450.24o"],   // 268b   //  1
      ["./SAPOS10/269x-0450.24o"],   // 269a1  //  2
      ["./SAPOS10/269x-0450.24o"],   // 269a2  //  3
      ["./SAPOS10/269x-0450.24o"],   // 269b   //  4
      ["./SAPOS10/269x-0450.24o"],   // 269c   //  5
      ["./SAPOS10/269x-0450.24o"],   // 269d   //  6
      ["./SAPOS10/270x-0450.24o"],   // 270a   //  7
      ["./SAPOS10/270x-0450.24o"],   // 270b   //  8
      ["./SAPOS10/270x-0450.24o"],   // 270c   //  9
      ["./SAPOS10/270x-0450.24o"],   // 270d   // 10
      ["./SAPOS10/270e-0450.24o"],   // 270e   // 11
      ["./SAPOS10/270f-0450.24o"],   // 270f   // 12
      ["./SAPOS10/274x-0450.24o"],   // 274a1  // 13 
      ["./IGS_FFM/274a.24o"],  // ["./SAPOS10/274x-0451.24o"],   // 274a2  // 14 
      ["./IGS_FFM/274b.24o"],  // ["./SAPOS10/274x-0451.24o"],   // 274b   // 15
      ["./IGS_FFM/274c.24o"],  // ["./SAPOS10/274x-0451.24o"],   // 274c   // 16 
      ["./IGS_FFM/274d.24o"],  // ["./SAPOS10/274x-0451.24o"],   // 274d   // 17
      ["./IGS_FFM/274e.24o"],  // ["./SAPOS10/274x-0450.24o"],   // 274e1  // 18
      ["./SAPOS10/274x-0450.24o"]    // 274e2  // 19 
    ]
  }
  

]
  
