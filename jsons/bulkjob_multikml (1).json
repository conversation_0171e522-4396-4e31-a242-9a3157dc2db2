{

    "required_files" : ["outputfile_tix4kml"],
    "required_vars"  : ["OUTPUTFOLDER","OUTPUTFILENAME","JOB_METAINFO.processed_timestamp","DSNAME"],
  



    "vars": [
        { "name": "KML_NAME"         ,   "data": "AlgoNav's tightly-coupled DGNSS+IMU Solution"}
    ],


    
    "process": [
    {
        "name"    : "Read-RTS4KML-from-TIX",
        "app"     : "MOD_SDC_FROM_TIX",
        "args"    : {
            "input_file": "$outputfile_tix4kml$",
            "output"    : "~>TC_RTS4KML"
        }
    },


    {
        "name"    : "RTS_into_MultiKML",
        "app"     : "MOD_SDC2KML",
        "args"    : {
            "enable"  : true,
            "colin"        : "<~~TC_RTS4KML@bulk",
            "output_files" : [
                {
                    "path"    : "$OUTPUTFOLDER$",
                    "filename": "$OUTPUTFILENAME$",
                    "type"    : "multikml",
                    "visible" : false,
                    "required": true
                }
            ],          
            "kml_name"                          : "$KML_NAME$",  //"tightly-coupled DGNSS+IMU Solution",
            "document_include"                  : "<open>1</open><gx:balloonVisibility>0</gx:balloonVisibility>",
            "placemark_include"                 : "",
            "placemark_name"                    : "dataset: ",
            "placemark_names"                   : "$[]DSNAME@bulk$",    // will internally create a json-array with all entries TASK_RESULT.dataset.name (over all collected inputs, in this case "@TR": task results)
            "multicolors"                       : ["FF0062FC", "FFFF0000", "FF00FF00", "FF0000FF", "FFFFFF00", "FFFF00FF", "FF00FFFF", "FFFFFFFF"],
            "placemark_description"             : "processed $JOB_METAINFO.processed_timestamp$; source file: ",
            "placemark_descriptions"            : "$[]outputfile_tix4kml@bulk$",
            "static"                            : false,
            "add_links_to_placemark_name"       : false,
            "add_links_to_placemark_description": false,
            "linestyle_width"         : 1,
            "folders"                 : [],
            "linestyle_color"         : "FF00FF00",
            "color_mode"              : "normal",   // "random",   // "random" or "normal"(default)
            "linestring_extrude"      : 0,
            "linestring_tessellate"   : 0,
            "linestring_altitudeMode" : "clampToGround"   //"absolute"
        }
    } 
    ],

    


    // main DAC-Process settings (if any)
    "settings": {  }
      
}
