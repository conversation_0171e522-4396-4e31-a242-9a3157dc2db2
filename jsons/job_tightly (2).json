{

// ==============================================================================================================================
// ==============================================================================================================================
// ============================================                                   ===============================================
// ===========================================         VARIABLE DEFINITIONS         =============================================
// ============================================                                   ===============================================
// ==============================================================================================================================
// ==============================================================================================================================

// all variables, that need to be provided from "outside" for this template to work properly.
// Note that this does *not* list the Variables of TYPE 2 (because static anyways = hard-coded!) and also *not* those of TYPE 3 (GUI-variables).
// The GUI-Variables will usually be replaced by the GUI, filling the actual user choice into "data" - but this is not a striuct requirement:
// the template will also execute properly just using the default "data" as defined below without any modification by the GUI.

  "required_files" : ["inputfileA_clk","inputfileA_sp3","inputfile_tix_imu","inputfile_rinex_rover","inputfileA_rinex_base","inputfile_atx_satellites"],
  "required_vars"  : ["OUTPUTFOLDER","DEVOUTPUTFOLDER","ATXFILE","DSNAME","PROCESSING_FROM","PROCESSING_UNTIL","INIT_HEADING_DEGRESS_FORWARD","INIT_HEADING_DEGRESS_REVERSE","ZUPT_INTERVALS","ARP_XYZ","PRECISE_FILES_TYPE"],



  "vars": [


    // =====================================================================================
    // VARIABLES TYPE 1a: Variables *not* defined here, but only to be inserted from outside
    // =====================================================================================
    // Variables of type 1a should appear in the "required_vars" or "required_files" lists, in order
    // to be able to confirm the validity of the current variable set (if all required variables were in fact defined)
    // Note, that at this position in the template, Variables of Type 1a do not exist, and the Type 1a is just mentioned here for 
    // the sake of completeness.



    // =====================================================================================================
    // VARIABLES TYPE 1b: defining no "data" (or simply a dummy), but relevant Meta-Information like "links"
    // =====================================================================================================
    // Variables, which only appear here for the simple reason, that meta-information *other* than "data" have to be present for the template to execute properly.
    // A typical example (like in the following line) will be the presence of a non-empty "links"-array!.
    // Note that an external definition *may* override the entire variable (in development environment), also replacing e.g. "links"  : ["base"]  with "links"  : ["F","base"].
    // However, the GUI shall *only* replace "data", and keep anything else.
    { "name": "inputfileA_rinex_base" ,       "links"  : ["base"]     ,      "data": ["dummy!"] },



    // =======================================================================================================
    // VARIABLES TYPE 2: STATIC variables, which are never defined/overriden by the GUI
    // =======================================================================================================
    { "name": "DEMO_OUTPUTS"     ,   "data"   :  true                },    
    { "name": "DEV_OUTPUTS"      ,   "data"   :  true                },    
    { "name": "FORWARD_TC"       ,   "links": [["fw",[1]]],      "data"   :  [true,false]                 },      // { "name": "FORWARD_TC"       ,   "data"   :  true                 },    
    { "name": "PASSES_TC"        ,   "data"   :  4                    },    


    // =======================================================================================================
    // VARIABLES TYPE 3: GUI variables
    // =======================================================================================================
    // GUI variables contain the "gui" key with information on how to display the variable in the GUI.
    // The "data" entry as defined here will be used as a default for the GUI. The GUI will regularly replace "data" with the actual user selection of a parameter.


    // ---------------------------------------------------------------------------------------------------
    // DGNSS
    // ---------------------------------------------------------------------------------------------------
    {
      "name"            : "GNSSES",
      "data"            : ["G","E","R"],  
      // "data"            : ["G","E"],  
      // "links":[["gnsses",[1,3,5,7]]],   "data": [["G"],["G","E"],["G","C"],["G","R"],["G","E","C"],["G","E","R"],["G","C","R"],["G","E","R","C"]],  
      // "links":[["gnsses",[1,4,5,7]]],   "data": [["G"],["G","E"],["G","C"],["G","R"],["G","E","C"],["G","E","R"],["G","C","R"],["G","E","R","C"]],  
      // "links":[["gnsses",[5]]],   "data": [["G"],["G","E"],["G","C"],["G","R"],["G","E","C"],["G","E","R"],["G","C","R"],["G","E","R","C"]],  
      "gui"             : {
        "component_id" : "CheckboxGroup",
        "group"        : "Differential GNSS",
        "order"        : 100,
        "label"        : "Select Constellations:",
        "tooltip"      : "select all constellations to be included in the differential GNSS processing. At least one constellation is required!",
        "msg_below_min": "Please select at least one constellation!",
        "min_checked"  : 1,
        "max_checked"  : 999,
        "items"        : [
          {"label": "GPS"       , "value": "G"},
          {"label": "Galileo"   , "value": "E"},
          {"label": "GLONASS"   , "value": "R"},
          {"label": "Beidou"    , "value": "C"}
        ]
      }
    },
    {
      "name"            : "FIXING_STRATEGY",
      "data"            : "FIX2",
      // "data"            : "FLOAT2",
      "gui"             : {
        "component_id": "RadioButtons",
        "group"       : "Differential GNSS",
        "order"       : 101,
        "label"       : "Select the carrier-phase processing type:",
        "tooltip"     : "Select, how carrier phase observations will be used for the positioning.",
        "items"       : [
          {"label": "Single-Frequency Float"                 , "value": "FLOAT1"},
          {"label": "Single-Frequency with Ambiguity Fixing" , "value": "FIX1"  },
          {"label": "Double-Frequency Float"                 , "value": "FLOAT2"},
          {"label": "Double-Frequency with Ambiguity Fixing" , "value": "FIX2"  }
        ]
      }
    },
    {
      "name"            : "DISABLE_PHASE",
      "data"            : false,  
      "gui"             : {
        "component_id"   : "Checkbox",
        "group"          : "Differential GNSS",
        "order"          : 102,
        "label"          : "Disable all Carrier-Phase observations",
        "tooltip"        : "Carrier phase observations heavily increase GNSS positioning accuracy and precision. Only disable, if you know what you're doing!"
        // "value_checked"  : true,   // sollte als default so definiert werden, wird nur bei Bedarf anders gesetzt
        // "value_unchecked": false   // sollte als default so definiert werden, wird nur bei Bedarf anders gesetzt
      }
    },
    
    
    
    {
      "name"            : "IONOSPHERE_ESTIMATION",      
      "data"            : false,  
      // "data"            : true,  
      "gui"             : {
        "component_id"   : "Checkbox",
        "group"          : "Differential GNSS",
        "order"          : 103,
        "label"          : "Enable Ionosphere Estimation",
        "tooltip"        : "Suggested for larger baselines (single base station: @baselines >5 km, network of base stations: at avg. baselines >30 km)"
      }
    },
    {
      "name"            : "IONOSPHERE_SYSTEMNOISE",
      "data"            : 0.002,     // (original) unit is TEC-units per sqrt(s) - default 0.01 TECU/sqrt(s) equals 0.6 TECU/sqrt(hour)
      "gui"             : {
        "component_id": "DiscreteSlider",
        "group"       : "Differential GNSS",
        "dependson_var": "IONOSPHERE_ESTIMATION",
        //"dependson_val": [true],                   // sofern dependson_var auf einen der in diesem Array gegebenen Werte gesetzt ist, soll diese Komponente sichtbar/aktiv sein. Als default (fuer Standardfall checkbox) soll "[true]" gesetzt sein!
        "label"       : "Expected level of ionospheric activity:",
        "items"       : [
          {"label": "no variation", "value":  0    },
          {"label": "very low"    , "value":  0.002},
          {"label": "low"         , "value":  0.005},
          {"label": "average"     , "value":  0.01 },
          {"label": "high"        , "value":  0.02 },
          {"label": "very high"   , "value":  0.04 },
          {"label": "extreme"     , "value":  0.1  }
        ]
      }
    },


    {
      "name"            : "TROPOSPHERE_ESTAX",
      "data"            : 0,  
      "gui"             : {
        "component_id": "RadioButtons",
        "group"       : "Differential GNSS",
        "order"       : 104,
        "label"       : "Tropospheric estimation:",
        "tooltip"     : "---",
        "items"       : [
          {"label": "Disabled"                        , "value": 0},
          {"label": "Zenith only"                     , "value": 1},
          {"label": "Zenith and horizontal gradients" , "value": 7}
        ]
      }
    },
    {
      "name"            : "TROPOSPHERE_SYSTEMNOISE",
      "data"            : 2e-6,     // unit is m/sqrt(s)
      "gui"             : {
        "component_id": "DiscreteSlider",
        "group"       : "Differential GNSS",
        "dependson_var": "TROPOSPHERE_ESTAX",
        "dependson_val": [1,7],                 // sofern dependson_var auf einen der in diesem Array gegebenen Werte gesetzt ist, soll diese Komponente sichtbar/aktiv sein
        "label"       : "Expected level of tropospheric activity (e.g. weather changes):",
        "items"       : [
          {"label": "no variation", "value":  0   },
          {"label": "very low"    , "value":  2e-6},   // 2e-6 m/sqrt(s) = 0.12 mm/sqrt(h)
          {"label": "low"         , "value":  5e-6},
          {"label": "average"     , "value":  1e-5},
          {"label": "high"        , "value":  2e-5},
          {"label": "very high"   , "value":  5e-5},  // 5e-5 m/sqrt(s) = 3 mm/sqrt(h)
          {"label": "extreme"     , "value":  1e-4}   // 1e-4 m/sqrt(s) = 6 mm/sqrt(h)  
        ]
      }
    },





    {
      "name"            : "CODE_OBSERVATION_NOISE",
      "data"            : 0.5,     // (original) unit is meters!
      "gui"             : {
        "component_id": "NumberField",
        "group"       : "Differential GNSS",
        "order"       : 105,
        "label"       : "Noise floor of double-differenced pseudorange observations",
        "tooltip"     : "As an orientation: low cost receivers can have a noise of up to 5 meters; geodetic receivers may reach 0.5 m",
        "unit"        : "m",
        "align"       : "right",   // default bei "left"
        "integer"     : false,
        "min_value"   : 0.1,
        "max_value"   : 50.0,
        "max_digits"  : 2,
        "multiply"    : 1.0
      }
    },
    {
      "name"            : "CODE_OT_STRICTNESS_LEVEL",
      "data"            : 3,     // 0 (very relaxed) ... 3 (normal) ... 6 (very strict)      // "links": ["otc"], "data": [0,1,2,3,4,5,6],  
      "gui"             : {
        "component_id": "StrictnessSlider",
        "group"       : "Differential GNSS",
        "order"       : 106,
        "label"       : "Pseudorange outlier-testing:",
        "tooltip"     : "Suggested setting: Normal. Use a stricter setting, if large GNSS errors appear to show up in your results."
        // "items" given by default in data base (from "very relaxed" to "very strict" - values 0 to 6)
      }
    },  


    {
      "name"            : "PHASE_OBSERVATION_NOISE",
      "data"            : 0.007,       // (original) unit is meters!
      "gui"             : {
        "component_id": "NumberField",
        "group"       : "Differential GNSS",
        "order"       : 107,
        "unit"        : "mm",
        "align"       : "right",   // default bei "left"
        "integer"     : false,
        "label"       : "Noise floor of double-differenced phase observations",
        "tooltip"     : "As an orientation: low cost receivers (e.g. smartphones) can have a noise of up to 30 mm; geodetic receivers can reach 5 mm of noise floor, sometimes better",
        "min_value"   : 1,
        "max_value"   : 100.0,
        "max_digits"  : 1,
        "multiply"    : 1000.0   // Faktor, mit dem der eigentliche Wert (hier in Metern!) in der GUI in eine andere Einheit übersetzt wird (hier Millimeter!). "data" muss sein wie vom Kern erwartet (also in Meter!)
      }
    },
    {
      "name"            : "PHASE_OT_STRICTNESS_LEVEL",
      "data"            : 3,                       
      "gui"             : {
        "component_id": "StrictnessSlider",
        "group"       : "Differential GNSS",
        "order"       : 108,
        "label"       : "Carrier-phase outlier-testing:",
        "tooltip"     : "Suggested setting: Normal. Use a stricter setting, if large GNSS errors appear to show up in your results."
        // "items" given by default in data base (from "very relaxed" to "very strict" - values 0 to 6)
      }
    },   
    
    

    {
      "name"            : "AMBIGUITY_FIXING_STRICTNESS_LEVEL",
      "data"            : 3,          // "links": ["fxs"], "data": [0,1,2,3,4,5,6],  
      // "links": ["fxs"], "data": [0,1,2,3,4,5,6],  
      "gui"             : {
        "component_id": "StrictnessSlider",
        "group"       : "Differential GNSS",
        "order"       : 120,
        "label"       : "Phase Ambiguity Fixing Strictness:",
        "tooltip"     : "When fixing DGNSS phase ambiguities, there is always a trade-off between availability (of fixes) and their reliability. Suggested: Normal."
        // "items"       : [
        //   {"label": "very relaxed"        , "value":  0},
        //   {"label": "more relaxed"        , "value":  1},
        //   {"label": "somewhat relaxed"    , "value":  2},
        //   {"label": "normal (recommended)", "value":  3},
        //   {"label": "somewhat strict"     , "value":  4},
        //   {"label": "more strict"         , "value":  5},
        //   {"label": "very srict"          , "value":  6}
        // ]
      }
    },   
    

    {
      "name"            : "AMBFIX_MIN_NUMSAT",
      // "data"            : 10,  
      "data"            : 6,  
      // "links": ["fxns"], "data": [5,6,7],  
      "gui"             : {
        "component_id": "IntegerSlider",
        "group"       : "Differential GNSS",
        "order"       : 121,
        "label"       : "Minimum number of satellites for ambiguity fixing:",
        "tooltip"     : "Having a larger minimum increases fixing robustness, but reduces the overall number of ambiguity fixes. Suggested for 1/2/3 fixable constellations: 4/6/7",
        "min_value"   : 4,
        "max_value"   : 15,
        "step"        : 1    
      }
    },


    {
      "name"            : "BASESTATION_DT",
      "data"            : 0,  
      "gui"             : {
        "component_id": "DiscreteSlider",
        "group"       : "Differential GNSS",
        "order"       : 150,
        "label"       : "Downsample GNSS base station data:",
        "tooltip"     : "For testing, how much a lower (than the available) data rate will affect the DGNSS results.",
        "items"       : [
          {"label": "do not change" , "value":  0},
          {"label": " 1 second"     , "value":  1},
          {"label": " 2 seconds"    , "value":  2},
          {"label": " 5 seconds"    , "value":  5},
          {"label": "10 seconds"    , "value": 10},
          {"label": "15 seconds"    , "value": 15},
          {"label": "20 seconds"    , "value": 20},
          {"label": "30 seconds"    , "value": 30},
          {"label": "60 seconds"    , "value": 60}
        ]
      }
    },

    
    // ---------------------------------------------------------------------------------------------------
    // IMU
    // ---------------------------------------------------------------------------------------------------
    {
      "name"            : "GNSS_LEVERARM_FRD",
      "data"            : {
        "name"     : "LEV",
        "init"     : [0.365,   0.1289,  -1.300],
        "std0"     : [0.01],                // GUI-Komponente erfragt (erstmal) nur *einen* Weert fuer die "initial uncertainty" - der soll fuer alle Achsen gelten. Wichtig: dennoch den Wert als json-Array der Länge 1 darstellen.
        "estax"    : [],                 // axes to be esitmated as defined by some CheckBox-Group. 0 = first axis in "axes", etc. Example: [0,1] for the lever arm means: only estimate Front and Right axes.
        "processes": {"type":"RC"}    // for random walk: "processes"  :{"type":"RW", "sqrt_q":1.234}; for consider state: {"type":"CONSIDER"}
      },
      "gui"             : {
        "component_id": "DynamicState",
        "group"       : "IMU",
        "order"       : 200,
        "axes"        : ["Front","Right","Down"],
        "unit"        : "m",
        "min_value"   : -50.0,
        "max_value"   : 50.0,
        "max_digits"  : 3,
        "process_type": "RC",        // can only be "RC" (random constant), "CONSIDER" (consider state), or "RW" (random walk). Only when "RW", the GUI component must add a NumberField for the signal noise in [unit]/sqrt(s) to be placed in "data":"processes":"sqrt_q"
        "name"        : "LEV",
        "label"       : "IMU-GNSS lever arm",
        "description" : "Lever arm from the IMU center of observations to the GNSS antenna phase center. The IMU center of observations is always the (virtual) intersection point of the three accelerometers. The 3-D lever arm is defined along the principal vehicle coordinate axes Front/Right/Down. Note: the Down-component is <b>negative</b>, if the GNSS antenna is sitting <b>above</b> the IMU."
        // "tooltip"     : "lever arm in meters, measured from the IMU's centre of observations <b>to</b> the GNSS antenna phase centre"
      }
    },

    {
      "name"            : "IMU_AXIS_ORIENTATION",
      "data"            : "FLU",  // 3-character string, reflecting the IMU's X/Y/Z axis directions, which must be right-handed! Valid characters are: F=Front, B=Back, L=Left, R=Right, U=Up, D=Down. Examples: "FLU","RFU","FRD",...
      "gui"             : {
        "component_id"   : "IMUAxisOrientation",
        "group"          : "IMU",
        "order"          : 201
      }
    },

    {
      "name"            : "IMU_VELOCITY_RANDOMWALK",
      "data"            : 1e-3, 
      "gui"             : {
        "component_id": "NumberField",
        "order"       : 202,
        "unit"        : "m/s/sqrt(s)",
        "align"       : "right",
        "integer"     : false,
        "label"       : "Accelerometers' velocity random walk",
        "tooltip"     : "The velocity random walk reflects the noise floor of the used accelerometers. Manufacturers commonly specify this value in their spec sheet.",
        "min_value"   : 1e-7,
        "max_value"   : 1e-1,
        "multiply"    : 1.0 
      }
    },      
    {
      "name"            : "IMU_ANGULAR_RANDOMWALK",
      "data"            : 2e-5, 
      "gui"             : {
        "component_id": "NumberField",
        "order"       : 203,
        "unit"        : "deg/sqrt(h)",
        "align"       : "right",
        "integer"     : false,
        "label"       : "Gyroscopes' angular random walk",
        "tooltip"     : "The angular random walk reflects the noise floor of the used gyroscopes. Manufacturers commonly specify this value in their spec sheet.",
        "min_value"   : 1e-4,
        "max_value"   : 1e4,
        "max_digits"  : 4,
        "multiply"    : 3437.74677   // <--- 180/pi*sqrt(3600)
      }
    },   
    
    
    {
      "name"            : "IMU_ACC_BIAS",
      "data"            : {
        "name"     : "ACC_BIAS",
        "init"     : [0.0,0.0,0.0],
        "std0"     : [1e-4],                // GUI-Komponente erfragt (erstmal) nur *einen* Weert fuer die "initial uncertainty" - der soll fuer alle Achsen gelten. Wichtig: dennoch den Wert als json-Array der Länge 1 darstellen.
        "estax"    : [0,1,2],                 // axes to be esitmated as defined by some CheckBox-Group. 0 = first axis in "axes", etc. Example: [0,1] for the lever arm means: only estimate Front and Right axes.
        "processes": {"type":"RW","sqrt_q":1e-5}    // for random walk: "processes"  :{"type":"RW", "sqrt_q":1.234}; for consider state: {"type":"CONSIDER"}
      },
      "gui"             : {
        "component_id": "DynamicState",
        "group"       : "IMU",
        "order"       : 204,
        "axes"        : ["IMU-X","IMU-Y","IMU-Z"],
        "unit"        : "m/s²",
        "min_value"   : -1.0,
        "max_value"   : 1.0,
        "min_value_sqrt_q": -0.01,    // m/s²/sqrt(s)
        "max_value_sqrt_q": 0.01,     // m/s²/sqrt(s)
        "max_digits"  : 7,
        "process_type": "RW",        // can only be "RC" (random constant), "CONSIDER" (consider state), or "RW" (random walk). Only when "RW", the GUI component must add a NumberField for the "random walk system noise" in [unit]/sqrt(s) to be placed in "data":"processes":"sqrt_q"
        "name"        : "ACC_BIAS",
        "label"       : "Accelerometer Bias",
        "description" : "Biases for the three accelerometers (X/Y/Z axes as indicated on the IMU or in the IMU specifications!).",
        "tooltip"     : "Leave the initial values zero, unless you have external knowledge (as from calibrations). The initial unceratinty and the characteristics of the time-variability (random walk) can be found in the IMU specifications."
      }
    },
    {
      "name"            : "IMU_GYR_BIAS",
      "data"            : {
        "name"     : "GYR_BIAS",
        "init"     : [-0.002091,0.000617,-0.002600],
        // "init"     : [0.0,0.0,0.0],
        "std0"     : [1e-5],                // GUI-Komponente erfragt (erstmal) nur *einen* Weert fuer die "initial uncertainty" - der soll fuer alle Achsen gelten. Wichtig: dennoch den Wert als json-Array der Länge 1 darstellen.
        "estax"    : [0,1,2],                 // axes to be esitmated as defined by some CheckBox-Group. 0 = first axis in "axes", etc. Example: [0,1] for the lever arm means: only estimate Front and Right axes.
        // "processes": {"type":"RW","sqrt_q":1e-7}    // for random walk: "processes"  :{"type":"RW", "sqrt_q":1.234}; for consider state: {"type":"CONSIDER"}
        "processes": {"type":"RW","sqrt_q":3e-7}    // for random walk: "processes"  :{"type":"RW", "sqrt_q":1.234}; for consider state: {"type":"CONSIDER"}
      },
      "gui"             : {
        "component_id": "DynamicState",
        "group"       : "IMU",
        "order"       : 205,
        "axes"        : ["IMU-X","IMU-Y","IMU-Z"],
        "unit"        : "rad/s",
        "min_value"   : -0.1,
        "max_value"   : 0.1,
        "min_value_sqrt_q": -1e-3,   // rad/s/sqrt(s)
        "max_value_sqrt_q": 1e-3,    // rad/s/sqrt(s)
        "max_digits"  : 7,
        "process_type": "RW",        // can only be "RC" (random constant), "CONSIDER" (consider state), or "RW" (random walk). Only when "RW", the GUI component must add a NumberField for the "random walk system noise" in [unit]/sqrt(s) to be placed in "data":"processes":"sqrt_q"
        "name"        : "GYR_BIAS",
        "label"       : "Gyroscope Bias",
        "description" : "Biases for the three gyroscopes (X/Y/Z axes as indicated on the IMU or in the IMU specifications!).",
        "tooltip"     : "Leave the initial values zero, unless you have external knowledge (as from calibrations). The initial unceratinty and the characteristics of the time-variability (random walk) can be found in the IMU specifications."
      }
    },    

    


    // ---------------------------------------------------------------------------------------------------
    // MOTION CONSTRAINT
    // ---------------------------------------------------------------------------------------------------

    {
      "name"            : "CONS_ACTIVE_AXES",
      "data"            : [1,2],  
      "gui"             : {
        "component_id" : "CheckboxGroup",
        "group"        : "Motion Constraint",
        "order"        : 295,
        "label"        : "Select active motion constraints:",
        "tooltip"      : "---",
        "min_checked"  : 0,
        "items"        : [
          {"label": "Zero lateral motion of rear axle"        , "value": 1},
          {"label": "Zero vertical motion at vehicle center"  , "value": 2}
        ]
      }
    },
    
    {
      "name"            : "CONS_STD_RELATIVE_ABSV",
      "data"            : [0.0, 0.0005, 0.01],       // so the (visible) default is 0.05% (for lateral motion) and 0.2% (for vertical motion). Ex: 0.05% * 10 m = 5 mm (@1sec: 10 m/s)
      "gui"             : {
        "component_id": "MultiNumberField",
        "group"        :"Motion Constraint",
        "order"       : 298,
        "unit"        : "%",
        "align"       : "right",
        "integer"     : false,
        "label"       : "Uncertainty of zero-observation, given as percentage of |v|",
        "visible_axes": [1,2],
        "axes"        : ["DUMMY","Lateral Zero-Motion","Vertical Zero-Motion"],
        "tooltip"     : "Define the uncertainty of the zero-motion constraints here as a percentage of the current absolute velocity, |v|",
        "min_value"   : 0.0,
        "max_value"   : 100.0,
        "max_digits"  : 4,
        "multiply"    : 100.0   // core requires unitless fraction, 100.0 translates it into %
      }
    },  
    {
      "name"            : "CONS_RPY",
      "data"            : {
        "name"     : "RPY",
        "init"     : [0.00000,-0.02125,-0.01113],
        "std0"     : [2.0e-4,2.0e-4,2.0e-4],               
        // "estax"    : [2],                   // axes to be esitmated as defined by some CheckBox-Group. 0 = first axis in "axes", etc.
        "estax"    : [],                   // axes to be esitmated as defined by some CheckBox-Group. 0 = first axis in "axes", etc.
        "processes": {"type":"RC", "sqrt_q": 1e-7}    // for random walk: "processes"  :{"type":"RW", "sqrt_q":1.234}; for consider state: {"type":"CONSIDER"}
      },
      "gui"             : {
        "component_id": "DynamicState",
        "group"       : "Motion Constraint",
        "order"       : 300,
        "axes"        : ["DUMMY","Vehicle-Right","IMU-Down"],
        "visible_axes": [1,2],        // die Achsen 0 und 1 sind erforderlich, um auch mal echte Odometer zu verwenden. Hier sollen diese Achsen aber in der GUI nicht erscheinen, dennoch in der Logik enthalten bleiben (also insb. bei den Achsen-Indices usw)
        "unit"        : "deg",
        "min_value"   : -180.0,      // deg
        "max_value"   : 180.0,       // deg
        "min_value_sqrt_q": -1.0,   // deg/sqrt(s)
        "max_value_sqrt_q": 1.0,    // deg/sqrt(s)
        "max_digits"  : 5,
        "multiply"    : 57.295779513082323,     // umrechnung Radiant->Grad (Kern will Radiant, User siehr Grad(=größerer Wert))
        "process_type": "RW",        // can only be "RC" (random constant), "CONSIDER" (consider state), or "RW" (random walk). Only when "RW", the GUI component must add a NumberField for the "random walk system noise" in [unit]/sqrt(s) to be placed in "data":"processes":"sqrt_q"
        "name"        : "RPY",
        "label"       : "Orientation of the vehicle's undercarriage",
        "description" : "The v-frame is defined as follows: The v-Right axis is defined by the orienation of the rear axle (pointing from the real axle center towards the right wheel); the v-Front axis direction is defined by the direction vector, starting at the rear axle center and pointing towards the front-axle center. The v-Down axis completes the right-handed coordinate frame. The IMU-Front/Right/Down-frame (body-Frame; <b>b</b>-Frame) is aligned with the (potentially permuted) IMU axes. The orientation of the v-Frame is given as a two-step YZ-Euler-angle rotation: (1) Rotation around the v-Right axis, (2) Rotation around the IMU-Down axis.",
        "tooltip"     : "In many cases, this orientation is initially unknown (or only with large uncertainty). It is suggested to let the software estimate the two angles. Since the b- and v-frames usually have a very similar orientation, the angles are expected to be small (few degrees)."
      }
    },
    {
      "name"            : "CONS_LEVERARM_6D",
      "data"            : {
        "name"     : "LEV",
        "init"     : [0.0000,0.0000,0.2970,0.2334,1.4489,0.1419],
        "std0"     : [0.01],                // GUI-Komponente erfragt (erstmal) nur *einen* Weert fuer die "initial 1-sigma uncertainty" - der soll fuer alle Achsen gelten. Wichtig: dennoch den Wert als json-Array der Länge 1 darstellen.
        "estax"    : [],                   // axes to be esitmated as defined by some CheckBox-Group. 0 = first axis in "axes", etc.
        "processes": {"type":"RC"}          // for random walk: "processes":{"type":"RW", "sqrt_q":1.234}; for consider state: "processes":{"type":"CONSIDER"}; for random constant: "processes":{"type":"RC"}
      },
      "gui"             : {
        "component_id": "DynamicState",
        "group"       : "Motion Constraint",
        "order"       : 301,
        "axes"        : ["DUMMY","DUMMY","IMU->rear axle: Front","IMU->rear axle: Down", "IMU->vehicle center: Front", "IMU->vehicle center: Right"],
        "visible_axes": [2,3,4,5],        // die Achsen 0 und 1 sind erforderlich, um auch mal echte Odometer zu verwenden. Hier sollen diese Achsen aber in der GUI nicht erscheinen, dennoch in der Logik enthalten bleiben (also insb. bei den Achsen-Indices usw)
        "unit"        : "m",
        "min_value"   : -20.0,
        "max_value"   : 20.0,
        "max_digits"  : 3,
        "process_type": ["CONST","CONSIDER","RC"],        // can only be "RC" (random constant), "CONSIDER" (consider state), or "RW" (random walk). Only when "RW", the GUI component must add a NumberField for the "random walk system noise" in [unit]/sqrt(s) to be placed in "data":"processes":"sqrt_q"
        "name"        : "LEV",
        "label"       : "IMU to Motion Constraint lever arms",
        "description" : "Lever arm along the vehicle frame axes (vehicle-Front, vehicle-Right, vehicle-Down) from the IMU's center of observations (1) to the rear axle (Front and Down components) and (2) to the vehicle center (turn point; Front and Right components)",
        "tooltip"     : "In most cases, it is suggested to let the software determine these values. It can be helpful to perform a dedicated calibration test drive to support this estimation process. In many cases, approximate initial values can be provided (as determined using a tape measure or similar). Please provide a realisitc initial uncertainty."
      }
    },





    // ---------------------------------------------------------------------------------------------------
    // OUTPUTS
    // ---------------------------------------------------------------------------------------------------
    {
      "name"            : "ENABLE_KML_OUTPUT",
      "data"            : true,  
      "gui"             : {
        "component_id"   : "Checkbox",
        "group"          : "Outputs",
        "order"          : 500,
        "label"          : "Create KML output files",
        "tooltip"        : "KML-files allow a quick look at the resulting trajectories using GoogleEarth."
      }
    },
    {
      "name"            : "KML_NAME",
      "links"           : [],
      "data"            : "AlgoNav's tighly-coupled DGNSS+IMU Solution",
      "gui"             : {
        "component_id" : "TextField",
        "dependson_var": "ENABLE_KML_OUTPUT",        //"dependson_val": [true],
        "group"        : "Outputs",
        "order"        : 501,
        "label"        : "Specify the caption for the KML placemark:",
        "tooltip"      : "You may use the following special variables (encapsulated in $-signs):<br>$DSNAME$: data set name"
      }
    }, 
    {
      "name"            : "KML_DATARATE_DT",
      "data"            : 0.1,     // (original) unit is TEC-units per sqrt(s) - default 0.01 TECU/sqrt(s) equals 0.6 TECU/sqrt(hour)
      "gui"             : {
        "component_id" : "DiscreteSlider",
        "dependson_var": "ENABLE_KML_OUTPUT",        //"dependson_val": [true],
        "group"        : "Outputs",
        "order"        : 502,
        "label"        : "Data rate for the KML outputs:",
        "items"        : [
          {"label": "10 sec"        , "value":  10.0 },
          {"label": "5 sec"         , "value":  5.0  },
          {"label": "2 sec"         , "value":  2.0  },
          {"label": "1 Hz"          , "value":  1.0  },
          {"label": "2 Hz"          , "value":  0.5  },
          {"label": "5 Hz"          , "value":  0.2  },
          {"label": "10 Hz"         , "value":  0.1  },
          {"label": "20 Hz"         , "value":  0.05 },
          {"label": "50 Hz"         , "value":  0.02 },
          {"label": "100 Hz"        , "value":  0.01 }
        ]
      }
    },




    {
      "name"            : "ENABLE_CSV_OUTPUT",
      "data"            : true,  
      "gui"             : {
        "component_id"    : "Checkbox",
        "group"           : "Outputs",
        "order"           : 510,
        "label"           : "Enable CSV output files",
        "tooltip"         : "CSV stands for 'comma-separated values'. The files will come with a self-explanatory header, and all data in ASCII format. You can specify the delimiter, and a prefix for the header/comment lines."
      }
    },
    {
      "name"            : "CSV_DATARATE_DT",
      "data"            : 0.1,     // (original) unit is TEC-units per sqrt(s) - default 0.01 TECU/sqrt(s) equals 0.6 TECU/sqrt(hour)
      "gui"             : {
        "component_id" : "DiscreteSlider",
        "dependson_var": "ENABLE_CSV_OUTPUT",
        "group"        : "Outputs",
        "order"        : 511,
        "label"        : "Data rate for the CSV outputs (one row in the output file per epoch):",
        "items"        : [
          {"label": "10 sec"        , "value":  10.0 },
          {"label": "5 sec"         , "value":  5.0  },
          {"label": "2 sec"         , "value":  2.0  },
          {"label": "1 Hz"          , "value":  1.0  },
          {"label": "2 Hz"          , "value":  0.5  },
          {"label": "5 Hz"          , "value":  0.2  },
          {"label": "10 Hz"         , "value":  0.1  },
          {"label": "20 Hz"         , "value":  0.05 },
          {"label": "50 Hz"         , "value":  0.02 },
          {"label": "100 Hz"        , "value":  0.01 }
        ]
      }
    },
    {
      "name"            : "CSV_COLUMNS",
      "data"            : ["WEEK","GPSSOW","LLH","SIGMA_NED","VELN","SIGMA_VNED","RPY","SIGMA_RPY","NUMSAT","NUMFXSAT"],  
      "gui"             : {
        "component_id" : "CheckboxGroup",
        "dependson_var": "ENABLE_CSV_OUTPUT",
        "group"        : "Outputs",
        "order"        : 512,
        "label"        : "Select Columns to be written into CSV-files:",
        "tooltip"      : "---",
        "msg_below_min": "Please select at least one output column!",
        "min_checked"  : 1,
        "items"        : [
          {"label": "GPS week number"                                     , "value": "WEEK"      },
          {"label": "GPS seconds of the week"                             , "value": "GPSSOW"    },
          {"label": "Latitude[°]/Longitude[°]/GRS80 ellipsoidal Height[m]", "value": "LLH"       },
          {"label": "Position uncertainty in North/East/Vertical [m]"     , "value": "SIGMA_NED" },
          {"label": "Velocity in North/East/Down [m/s]"                   , "value": "VELN"      },
          {"label": "Velocity uncertainty in North/East/Vertical [m/s]"   , "value": "SIGMA_VNED"},
          {"label": "Roll/Pitch/Yaw [°]"                                  , "value": "RPY"       },
          {"label": "Roll/Pitch/Yaw uncertainty [°]"                      , "value": "SIGMA_RPY" },
          {"label": "Number of observed satellites"                       , "value": "NUMSAT"    },
          {"label": "Number of fixed satellites"                          , "value": "NUMFXSAT"  }
        ]
      }
    }

  ],
  

  







  
  "process": [
    
    // ==============================================================================================================================
    // ==============================================================================================================================
    // ============================================                                   ===============================================
    // ===========================================           1. READ GNSS FILES        ==============================================
    // ===========================================           AND PREPARE GNSS          ==============================================
    // ============================================                                   ===============================================
    // ==============================================================================================================================
    // ==============================================================================================================================

    {
      "name"    : "Read_Rover_Observations",
      "app"     : "MOD_GNSS_OBS_READER",
      "args"    : {
        "rnxfile" : "$inputfile_rinex_rover$",
        // "max_numep": 600,
        "output" : "~>GNSSRAW_ROVER"
      }
    },
    {
      "name"    : "Read_Base_Observations",
      "app"     : "MOD_GNSS_OBS_READER",
      "args"    : {
        "rnxfile"  : "$inputfileA_rinex_base$",
        "dt"       : "$BASESTATION_DT$",
        "output"   : "~>GNSSRAW_BASE"
      }
    },    
    {
      "name"    : "Read_Precise_Files",
      "app"     : "MOD_GNSS_PREC_READER",
      "args"    : {
        "clkfiles"   : "$inputfileA_clk$",
        "sp3files"   : "$inputfileA_sp3$",
        "type"       : "$PRECISE_FILES_TYPE$",
        "out_orbits" : "~>GNSS_ORBITS",
        "out_clocks" : "~>GNSS_CLOCKS"
      }
    },    
    {
      "name"    : "Read_ANTEX",
      "app"     : "MOD_GNSS_ANTEX_READER",
      "args"    : {
        "sat_antex_file" : "$inputfile_atx_satellites$",
        "in_rawobs"      : "<~GNSSRAW_ROVER",
        "output"         : "~>ANTEX"
      }
    },

    {
      "name"    : "DGNSS_ESC_Selection",
      "app"     : "MOD_DGNSS_ESC_SELECT",
      "args"    : {
        "in_rawobs_rover" : "<~GNSSRAW_ROVER",
        "in_rawobs_base"  : "<~~GNSSRAW_BASE@base",
        "in_orbits" : "<~GNSS_ORBITS",
        "in_clocks" : "<~GNSS_CLOCKS",
        "out_esc_rover": "~>ESC_ROVER",        
        "out_esc_base" : "~~>ESC_BASE@base",
        // "disable_gprns": [1017],
        // "disable_gprns": ["$$",["disable_R17"],[[],[1017]]],
        "disable_gprns": [],
        "freqs_priority_lists":
        {
          "G": [[1], [2, 5   ]],
          "R": [[1], [2      ]],
          "E": [[1], [5, 7, 8]],
          "C": [[2], [7, 6   ]]
        },        
        "enable_gnss":  "$GNSSES$"         //["G","E","R"]
      }
    },

    {
      "name"    : "Prepare-Rover",
      "app"     : "MOD_GNSS_PREPARE",
      "args"    : {
        "in_rawobs" : "<~GNSSRAW_ROVER",
        "in_orbits" : "<~GNSS_ORBITS",
        "in_clocks" : "<~GNSS_CLOCKS",
        "in_antex"  : "<~ANTEX",
        "in_esc"    : "<~ESC_ROVER",

        "out_pp" : "~>PP_ROVER",
        "out_spp": "~>SPP_ROVER",

        "base_station_mode": false,

        "settings"                    : {
          "pivot_min_elevation_deg"     : 10.0,
          // "pivot_elev_bonus_deg"        : 9999.0,   // "elevation bonus" for the current pivot - avoids a too jumpy pivot selection
          "pivot_elev_bonus_deg"        : 1.0,      // "elevation bonus" for the current pivot - avoids a too jumpy pivot selection
          
          "pivot_ignore_av_obs"         : true,     // true: only consider elevation, regardless of availability of observations to the pivot
          // "pivot_ignore_av_obs"         : false, // true: only consider elevation, regardless of availability of observations to the pivot
          
          "show_charts"                 : true,
          "set_outliers_to_nan"         : true,
          "correct_codephase_with_rcb"  : 1.0,
          "correct_codephase_with_scb"  : 1.0,
          "spp_ot_thresh_m"             : 50.0,
          "spp_ot_thresh_m_apriori_perep": 1000.0,
          "spp_min_num_sats"            : 5,
          "elev_mask_deg"               : 5.0,
          "csd_thresh_meters"           : 0.05,
          "use_if_for_spp"              : false,
          "disable_phase_during_eclipse": false,
          "min_sun_sat_angle_deg"       : 0.0,
          "rco_factor"                  : 0.0,
          "tof_iterations"              : 2,
          "switch_rcc"                  : 1.0,
          "switch_pcosat"               : 0.0,
          "switch_pwusat"               : 0.0,
          "switch_ztddry"               : 1.0
        }     
      }
    },

    {
      "name"    : "Prepare-Base",
      "app"     : "MOD_GNSS_PREPARE",
      "args"    : {
        "in_rawobs" : "<~GNSSRAW_BASE",
        "in_orbits" : "<~GNSS_ORBITS",
        "in_clocks" : "<~GNSS_CLOCKS",
        "in_antex"  : "<~ANTEX",
        "in_esc"    : "<~ESC_BASE",

        "out_pp" : "~>PP_BASE",
        "out_spp": "~>SPP_BASE",

        "base_station_mode": true,

        "settings"                    : {
          "dXYZ"                        : [-0.5295,    0.6149,    0.3656],   // at SAPOS Darmstadt station, shift to transform ETRF2014 into (roughly) WGS84 [for better viewing with GoogleEarth!]
          "base_station_arp_xyz"        : "$ARP_XYZ$",
          "dNED"                        : [0.0, 0.0, 0.0],
          "show_charts"                 : true,
          "set_outliers_to_nan"         : true,
          "correct_codephase_with_rcb"  : 1.0,
          "correct_codephase_with_scb"  : 1.0,
          "spp_ot_thresh_m"             : 50.0,
          "spp_ot_thresh_m_apriori_perep": 100.0,
          "spp_min_num_sats"            : 5,
          "elev_mask_deg"               : 5.0,
          "csd_thresh_meters"           : 0.05,
          "use_if_for_spp"              : false,
          "disable_phase_during_eclipse": false,
          "min_sun_sat_angle_deg"       : 0.0,
          "rco_factor"                  : 0.0,
          "tof_iterations"              : 2,
          "switch_rcc"                  : 1.0,
          "switch_pcosat"               : 0.0,
          "switch_pwusat"               : 0.0,
          "switch_ztddry"               : 1.0
        }     
      }
    },







    // ==============================================================================================================================
    // ==============================================================================================================================
    // ============================================                                   ===============================================
    // ===========================================       PROCESS TIGHTLY COUPLED       ==============================================
    // ============================================                                   ===============================================
    // ==============================================================================================================================
    // ==============================================================================================================================
    {
      "name"    : "Synth-Odo-Generator",
      "app"     : "MOD_SDCSYN_EXT_TT",
      "args"    : {
        "sdf_tt"        : "<~SPP_ROVER.T",
        "dt_seconds"    : 1.0,
        // "dt_seconds"    : 0.2,
        // "dt_seconds"    : ["$$",[["consdt",[]]],[0.1,0.2,0.3,0.5,0.75,1.0,1.2,1.5,2.0,3.0]], 
        "field_out_tt"  : "T",
        "field_out_data": "ticks",        
        "const_data"    : [0.0, 0.0, 0.0],
        "output"        : "~>ODODATA"
      }
    },

    {
      "name"    : "Read-IMU-from-TIX",
      "app"     : "MOD_SDC_FROM_TIX",
      "args"    : {
        "input_file": "$inputfile_tix_imu$",
        "output"    : "~>IMUDATA"
      }
    },

    {
      "name": "Starling_IMU_Tightly",
      "app": "MOD_STARLING",
      "args": {
        "sensors" : [
          {
            "name":"dgnssX",
            "type":"DGNSSX",

            "in_pp_rover"    :"<~PP_ROVER",
            "in_esc_rover"   :"<~ESC_ROVER",
            "in_spp"         :"<~SPP_ROVER",

            "mvrs"           : {},            // mvrs settings (as sub-struct!)

            "colin_pp_base"  :"<~~PP_BASE@base",
            "colin_esc_base" :"<~~ESC_BASE@base",

            "states": [ "$GNSS_LEVERARM_FRD$" ],
            
            "settings": {

              "fixing_slc" : {
                "name": "FX_MANUALNAME",
                "sinks": ["~>DGNSS_FX"]
              },
              "ambrts_slc" : { "sinks": ["~>DGNSS_AMBRTS"] },
              
              "disable_phase"           : "$DISABLE_PHASE$",

              "enable_ionosphere_estimation": "$IONOSPHERE_ESTIMATION$",              
              "tec_sqrtq"      : "$IONOSPHERE_SYSTEMNOISE$",  //"tec_sqrtq"      : ["$$",[["tecrw"   ,[3]]],[0.001, 0.002, 0.005, 0.01, 0.02, 0.05, 0.1]],  // 0.01 TECU / sqrt(s) = 0.6 TECU / sqrt(h);         0.001 TECU / sqrt(s) = 0.06 TECU / sqrt(h)

              "tro_estax"         : "$TROPOSPHERE_ESTAX$",
              "tro_std0"          : 0.003, //[0.03, 0.003, 0.003],       // [0.01, 0.005, 0.005],
              "tro_sqrtq"         : "$TROPOSPHERE_SYSTEMNOISE$",          // 3e-6,

              "ion_estax"   : 0,
              "ion_std0"    : [0.5, 0.5, 0.5],  // between-stations-differential TECU
              "ion_sqrtq"   : 0.001,


              "lockindetector_enable"       : true,
              "lockindetector_max_spp_stdev": 2.0  ,
              "lockindetector_min_distance" : 10.0 ,
              "lockindetector_thresh"       : 20.0  ,
              "lockindetector_fact_dist"    : 1.0  ,


              "lockindetectorAB_max_rms"    : 4.0,
              "lockindetectorAB_min_num_sat": 5,

              "iono_mm_per_km" : ["$$",[["ionostd"  ,[9]]],[0.0, 0.25, 0.5, 0.75, 1.0, 1.5, 2.0, 3.0, 5.0,10.0,100.0]],   // example: 1.5 mm/km will model an ionospheric error of 26.4 cm (zenith!) for a 176 km baseline.
              
              "immediate_fixing_always" : true,
              "immediate_fixing_passes" : [],
              "rts_fixing_always"       : false,
              "rts_fixing_passes"       : [],
              
              // "only_wl_fixing"          : false,
              // "enable_wl_fixing"        : false,
              "only_wl_fixing"          : false,
              "enable_wl_fixing"        : ["$$",[["wlfx"  ,[0]]],[false,true]],
              
              "store_accumulated_fix_knowledge"   : true,
              // "store_ambiguity_upon_outlier_reset": false,
              "store_ambiguity_upon_outlier_reset": true,
              "prioritize_rts_fk"                 : true,
              "phase_outlier_immediate_reinit"    : true,
              "amb_reinit_on_fix_outlier"         : 2,          // 2 means: reset all ambiguities of a sat, if some ambiguity-fix-attempt of it was found to be a fix-outlier
              
              "amb_sigma_after_downgrade_cyc": 0.01,
              "phase_ot_for_reinit"          : 0.0,                            

              // "ambiguity_stuckness_threshold": 15.0,
              // "ambiguity_stuckness_threshold": 0.0,
              "ambiguity_stuckness_threshold": ["$$",[["stucky"  ,[6]]],[0.0, 3.0, 4.0, 5.0, 7.5, 10, 15]],
              "disable_gnsses": [],  // ["G"]
              
              "phase_usage" : {
                "G" : "$FIXING_STRATEGY$",
                "E" : "$FIXING_STRATEGY$",
                "C" : "$FIXING_STRATEGY$", 
                "R" : "FLOAT2"  // for GLONASS: usually use float2
              },

              "verbose"           : [
                20,               // Infos zu Pivot changes
                // 21             // Infos zu nicht beobachtetem Pivot
                50,               // Infos zu Ausreissern
                // 51,
                // 51,52,               // erw. Infos zu Ausreissern
                // 53,              // Infos zur ambiguity stuckness detection
                54,              // Info zu lock-in-detector
                // 60, 
                // 61,             // Infos zur Fixierung
                //61,62,63,        // erw. Infos zur Fixierung

                // 10,                 // Fixierung: Info, falls FINAL APPROVAL nicht gewaehrt wurde.
                
                // 40,41,                // Infos zum AmbRTS
                //42, //43,              // erw. Infos zum AmbRTS
                30                       // (30,31,32) adaptivity-information alle 1000, 100, 10 epochen
              ],
              //"reset_all_ambiguities_on_skip": true,
              
              "inflation_for_few_sats": [],    // to be "managed" implicitly via MDB!!              // "inflation_for_few_sats": [100.0,100.0,100.0,  30.0,  10.0,  3.0],              
              "fix_shrink_hightolerance": false,              
              "fix_use_par"             : false,
              "fix_par_P0"              : 0.995,
              "fix_ncands"              : 2,
              "fix_min_num_sats"        : "$AMBFIX_MIN_NUMSAT$",              
              "fix_max_pdop_sq"         : 12.0,  //["$$",[["fxpdopsq"   ,[5]]],[5.0, 5.5, 6.0, 7.0, 8.0, 10.0]],
              // "fix_mu_max"              : 0.65,              
              "fix_mu_max"              : ["$$",[["mumax"   ,[5]]],[0.5,0.6,0.7,0.8,0.9,1.0]],

              "validation_required_wif" : ["$$",[["usewif",[0]]],[false, true ]],
              "validation_required_ftp" : ["$$",[["usewif",[]]],[true , false]],
              // "validation_required_wif" : true,
              // "validation_required_ftp" : true,
              "allowed_fixcand_removals": 1,
              "validation_include_float_obs": false,  // only for WIF-test -- to be consistent with FTP test: false
              "validation_include_wl_obs"   : false,  // only for WIF-test -- to be consistent with FTP test: false

              "validation_required_wl"  : ["$$",[["usewifwl",[1]]],[false , true]],
              "fix_validation_params_wl"   : [1.3,1e10,  2.3,1e10,       1e10,1e10,  1e10,1e10,      2.0, 0.08,   1e10,0.2],           // for new/old/all:   rms[-],rms[m],max[-],max[m]
              // "fix_validation_params_wl"   : [1.0,1e10,  2.0,1e10,       1e10,1e10,  1e10,1e10,      1.5, 0.05 ,   1e10,1e10],           // for new/old/all:   rms[-],rms[m],max[-],max[m]

              "fix_validation_params"   : ["$AMBIGUITY_FIXING_STRICTNESS_LEVEL$->",       // 4-packes for new/old/all;   each: rms[-],rms[m],max[-],max[m]
                // ORIGINAL:
                [2.0,1e10,  3.0,1e10,       1e10,1e10,  1e10,1e10,      3.0, 0.05 ,   1e10,1e10],   // "very_relaxed"     
                [1.6,1e10,  2.6,1e10,       1e10,1e10,  1e10,1e10,      2.5, 0.04 ,   1e10,1e10],   // "more_relaxed"     
                [1.3,1e10,  2.3,1e10,       1e10,1e10,  1e10,1e10,      2.0, 0.035,   1e10,1e10],   // "somewhat_relaxed" 
                [1.0,1e10,  2.0,1e10,       1e10,1e10,  1e10,1e10,      1.5, 0.03 ,   1e10,1e10],   // "normal"           
                [0.8,1e10,  1.8,1e10,       1e10,1e10,  1e10,1e10,      1.3, 0.025,   1e10,1e10],   // "somewhat_strict"  
                [0.7,1e10,  1.5,1e10,       1e10,1e10,  1e10,1e10,      1.1, 0.02 ,   1e10,1e10],   // "more_strict"      
                [0.6,1e10,  1.2,1e10,       1e10,1e10,  1e10,1e10,      1.0, 0.016,   1e10,1e10]    // "very_srict"       
              ],
              
              "fix_pot_threshold"       : 10.0, //["$$",[["pot"   ,[]]],[3.0, 10.0, 50.0, 9999.0]], //10.0, //10.0,  //9999, //15.0,    // NOTE: a low number is *more* strict - if position jumps by more than n sigma, fix will be refused. 10 is relaxed (almost disabled), 9999 is disabled.
              "fix_pseudo_variance"     : 1e-4,  // 1e-6 is consistent with 0.001 cycles of stdev -> approx < 0.2 mm -- small enough it can be regarded as "zero", but not too small considering potential numerical instabilities
              "std0_code"           :  "$CODE_OBSERVATION_NOISE$",
              "std0_phase"          : "$PHASE_OBSERVATION_NOISE$",
              "noise_mapping_exponent": 1.0,
              "elev_mask_deg"       : ["$$",[["elm"   ,[0]]],[10, 12.5, 15, 17.5, 20.0]],
              "elev_mask_fixing_deg": ["$$",[["elmfx" ,[0]]],[10, 12.5, 15, 17.5, 20.0]],
              "amb_std0"          : 1e4, // 10.000 cycles of std
              "ddbias_window_size": ["$$",[["ddbws" ,[4]]],[0, 10, 100, 300, 1000, 3000]],   // 0 to disable
              "ddbias_max_residual_code" : 0.005,
              "ddbias_max_residual_phase": 0.0, //0.0005,

              // "otar_position_inflation": 1000.0, 
              // "otar_position_inflation": ["$$",[["opi" ,[0]]],[2.0,3.0,5.0,10.0,15.0,20.0,30.0,50.0,100.0,1000.0]],
              // "otar_position_inflation": [100.0, 2.0],  // !!!
              "otar_position_inflation": [100.0, 2.0],
              // "otar_position_inflation": 10.0, // inflate position uncertainty by this factor for outlier detection and adaptivity // > 20!?

              "outliertesting_min_num_sats": 0,
              "adaptivity_min_num_sats"    : 8,
              "Huber_thresh"               : 0.1,   // reiterate, if some weight changes by more than 10%              
              "Huber_maxiter"              : 4,

              // "min_num_sat_noskip"   : 5,
              "min_num_sat_noskip"   : ["$$",[["nnoskip",[0]]],[1,2,3,4,5,6]],

              "ot_code" : {
                "max_num_obs_to_remove"    : 9999,
                "min_num_obs_to_keep"      : 1,
                "variance_inflation_at_min": 10000.0,
                "MDB_inflation_factor"       : ["$$",[["mdbc" ,[3]]],[0.0, 0.1, 0.15, 0.2, 0.3, 0.5, 0.7, 1.0]],   // 0 to disable,   // every observation is downweighted to a minimum uncertainty of this factor times the minimum detectable bias (MDB) -- for safety [to avoid over-optimistic estimates in situations, where constellation is bad and outliers cannot be detected]!
                "MDB_inflation_max"          : 1000.0, // every observation is downweighted to a minimum uncertainty of this factor times the minimum detectable bias (MDB) -- for safety [to avoid over-optimistic estimates in situations, where constellation is bad and outliers cannot be detected]!
                "Huber_k"                  : ["$$",[["huberc" ,[3]]],[0.0, 0.7, 1.0, 1.2, 1.5, 2.0, 2.5, 3.0, 10.0]],
                // "Huber_k"                  : 1.5,
                // "Huber_k"                  : 0.0,
                "ot_threshold"             : ["$CODE_OT_STRICTNESS_LEVEL$->" ,   5.0, 4.0, 3.0, 2.5, 2.2, 1.9, 1.7]
              },
              
              "ot_phase" : {
                "max_num_obs_to_remove"    : 9999,
                "min_num_obs_to_keep"      : 0,
                "variance_inflation_at_min": 1.0,
                "MDB_inflation_factor"       : ["$$",[["mdbp" ,[3]]],[0.0, 0.1, 0.15, 0.2, 0.3, 0.5, 0.65, 0.8, 0.9, 1.0]],   // 0 to disable,   // every observation is downweighted to a minimum uncertainty of this factor times the minimum detectable bias (MDB) -- for safety [to avoid over-optimistic estimates in situations, where constellation is bad and outliers cannot be detected]!
                "MDB_inflation_max"          : 1000.0, // every observation is downweighted to a minimum uncertainty of this factor times the minimum detectable bias (MDB) -- for safety [to avoid over-optimistic estimates in situations, where constellation is bad and outliers cannot be detected]!
                "Huber_k"                  : ["$$",[["huberp" ,[4]]],[0.0, 0.7, 1.0, 1.2, 1.5, 2.0, 2.5, 3.0, 10.0]],
                // "Huber_k"                  : 0.0,
                // "Huber_k"                  : 1.5,
                "ot_threshold"             : ["$PHASE_OT_STRICTNESS_LEVEL$->",   5.0, 4.0, 3.0, 2.5, 2.2, 1.9, 1.7]
              },
              "ot_fixing" : {
                "max_num_obs_to_remove"    : 9999,
                "min_num_obs_to_keep"      : 0,
                // "all_or_nothing"           : true,
                "all_or_nothing"           : false,
                "ot_threshold"             : 3.0 //2.5
              },


              "adaptivity_code" : { // <---------------------------------------------------------------------------------------------------------- attenuation irgendwie pro Zeiteinheit definieren, nicht pro update!
                "enabled"    :true,
                // "enabled"    :false,
                "min_factor" :0.2,
                "max_factor" :5.0,
                "min_residual":0.1,
                "max_residual":10.0,
                "attenuation":0.001,
                // "attenuation":0.0,
                "convergence":0.65
                // "attenuation":0.00
              }
              ,
              "adaptivity_phase" : {
                // "enabled"    :true,
                "enabled"    :false,   // TODO!! use a second "enabled"-variable, like "fully_disabled" or so
                "min_factor" :0.33,
                "max_factor" :3.0,
                "attenuation":0.0005,
                // "attenuation":0.0,
                "convergence":0.65
              }
            }
          }

          // {
          //   "name":"cupt",
          //   "type":"POSLEV",
          //   "input":"<~DGNSS_CUPT",            
          //   "field_t"    : "T",
          //   "field_llh"  : "LLH", "latlon_in_degrees": false,
          //   "field_R"    : "POSN:P",            // "field_sdned": "POSN:STDFULL",
          //   "std_fact"    : 1.0, //["$$",["cuptstdfact"], [1.0,2.0,3.0,5.0,10.0]],
          //   // "ot_threshold": 30.0,
          //   "MDB_inflation_factor": 0.0,
          //   "Huber_k"             : 1.5,
          //   "states": [ "$GNSS_LEVERARM_FRD$" ],
          //   "settings" : { }
          // }          
          // ,

          // {
          //   "name"       :"cons",
          //   "type"       :"ODON",            // "type"       :"ODOB",
          //   "input"      :"<~ODODATA",
          //   "field_t"    : "T",
          //   "field_ticks": "ticks",
          //   "settings": {"clandestine": false},  // "settings": {"clandestine": true},
          //   // "ot_threshold": 30.0,
          //   // "MDB_inflation_factor": 0.0,   // default: 0.0 = off
          //   // "Huber_k"             : 1.5,   // default: 0.0 = off
          //   "HEC_mode": 2,   // 0: disable, 1: enable - using odometry measurement, 2: forcing a heading flip right away!
          //   "HEC_max_reverse_speed": 10.0,
          //   "disable_clone_states_in_rts": ["$$",["csrts"], [false,true]],
          //   // "disable_clone_states_in_rts": true, 
          //   "active_axes": "$CONS_ACTIVE_AXES$",
          //   "stdev_ticks"   : [0.0, 0.001, 0.001],   // scale factor is 1.0, so ticks equal meters
          //   "stdev_meters"  : [0.0, 0.0, 0.0],
          //   "stdev_perc"    : [0.0, 0.0, 0.0],   // percentage from delta-pos in individual directions
          //   "stdev_perc_abs": "$CONS_STD_RELATIVE_ABSV$",
          //   // "stdev_per_f"   : [0.0,0.0,0.0,   0.0,0.05,0.002,   0.0,0.0,0.0],    // 1st block (=1st column in 3x3 matrix): Effect of f_Front for the three Odometry observations in F/R/D. Same for 2nd column and 3rd column (attention: will be multiplied with 9.8!)
          //   "stdev_per_f"   : [0.0,0.0,0.0,   0.0,0.0,0.0,   0.0,0.0,0.0],    // 1st block (=1st column in 3x3 matrix): Effect of f_Front for the three Odometry observations in F/R/D. Same for 2nd column and 3rd column (attention: will be multiplied with 9.8!)
          //   "rpy_min_v_abs": 1.0,       // minimum absolute velocity for RPY estimation.
          //   "rpy": "$CONS_RPY$",
          //   "states": [
          //     "$CONS_LEVERARM_6D$",
          //     {"name":"SF","init": [1.0, 1.0, 1.0],"estax": 0, "std0": [0.1], "processes": {"type": "RC"}},
          //     // {"name":"OMEGA","init": [0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0],"estax": ["$$",["estomega"], [[],[2,5]]], "std0": [0.0002], "processes": {"type": "RC"}}       // 0.0002 rad/(m/s²) approx.= 0.01 deg/(m/s²); axis 2: f_Front propagating into "Y", axis 5: f_Right propagating into "Y"
          //     {"name":"OMEGA","init": [0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0],"estax": [], "std0": [0.0002], "processes": {"type": "RC"}}       // 0.0002 rad/(m/s²) approx.= 0.01 deg/(m/s²)
          //   ]
          // }        // end of cons

          ,
          {
            "name"       :"cons",
            "type"       :"V",            // "type"       :"ODOB",
            "input"      :"<~ODODATA",
            "field_t"    : "T",
            "field_ticks": "ticks",
            "settings": {"clandestine": false},  // "settings": {"clandestine": true},
            // "ot_threshold": 30.0,
            // "MDB_inflation_factor": 0.0,   // default: 0.0 = off
            // "Huber_k"             : 1.5,   // default: 0.0 = off
            "HEC_mode": 2,   // 0: disable, 1: enable - using odometry measurement, 2: forcing a heading flip right away!
            "HEC_max_reverse_speed": 10.0,
            "active_axes": "$CONS_ACTIVE_AXES$",
            "stdev_ticks"   : [0.0, 0.0001, 0.0001],   // scale factor is 1.0, so ticks equal meters
            "stdev_meters"  : [0.0, 0.0, 0.0],
            "stdev_perc"    : [0.0, 0.0, 0.0],   // percentage from delta-pos in individual directions
            "stdev_perc_abs": "$CONS_STD_RELATIVE_ABSV$",
            "stdev_per_f"   : [0.0,0.0,0.0,   0.0,0.0,0.0,   0.0,0.0,0.0],    // 1st block (=1st column in 3x3 matrix): Effect of f_Front for the three Odometry observations in F/R/D. Same for 2nd column and 3rd column (attention: will be multiplied with 9.8!)
            "rpy_min_v_abs": 1.0,       // minimum absolute velocity for RPY estimation.

            
            "zupt_intervals": "$ZUPT_INTERVALS$",
            "zupt_stdev_FRD": [0.0001,0.0001,0.0001],
            "enable_zupt"   : ["$$",[["zupt",[1]]], [false,true]],
            
            "rpy": "$CONS_RPY$",
            "states": [
              "$CONS_LEVERARM_6D$",
              {"name":"SF","init": [1.0, 1.0, 1.0],"estax": 0, "std0": [0.1], "processes": {"type": "RC"}},
              {"name":"OMEGA","init": [0.0,0.0,0.0,0.0,0.0,-0.5e-3,0.0,0.0,0.0],"estax": ["$$",[["estomega",[0]]], [[],[5]]], "std0": [0.001], "processes": {"type": "CONSIDER"}}       // 0.0002 rad/(m/s²) approx.= 0.01 deg/(m/s²); axis 2: f_Front propagating into "Y", axis 5: f_Right propagating into "Y"
              // {"name":"OMEGA","init": [0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0,0.0],"estax": [], "std0": [0.0002], "processes": {"type": "RC"}}       // 0.0002 rad/(m/s²) approx.= 0.01 deg/(m/s²)
            ]
          }        // end of cons

        ],        
        
        "predictor": {
          "type"        : "IMUN",
          "imudata"     : "<~IMUDATA",
          "field_tt"    : "T",
          "field_acc"   : "acc",
          "field_gyr"   : "gyr",
          "vel0_std"    : 0.01,
          "imu_timeshift_ep": 1,           
          "orientation" : "$IMU_AXIS_ORIENTATION$",             //"FLU",
          "check_timestamps": true,
          "large_heading_mode"     : false,  //         "large_heading_threshold": 0.0003,   // 0.0: always use large heading mode
          "disable_bias_estimation_1st_pass": ["$$",[["disablebias"   ,[0]]], [false,true]],
          "snvel"       : "$IMU_VELOCITY_RANDOMWALK$",       //1e-3,
          "snatt"       : "$IMU_ANGULAR_RANDOMWALK$", //["$$",[["snattimu"   ,[3]]], [1e-6, 3e-6, 1e-5, 3e-5, 1e-4, 3e-4, 5e-3]],
          "states": [ "$IMU_ACC_BIAS$", "$IMU_GYR_BIAS$" ]
        },   

        "outputs" : [
          {
            "name": "EKFLog",
            "trigger" : "EKF_AFTER_CLEANUP",  "sensor"  : "dgnssX",
            // "trigger" : "EKF_AFTER_CLEANUP",  "sensor"  : "cons",
            // "trigger" : "EKF_TIME",  "dt"  : 0.2,
            "fields": [
              "i64|T",                 "u16|WEEK",             "f64|GPSSOW",              
              "d|LLH",
              "d|V_NED",
              "d|V_FRD",
              "d|RPY",
              "d|COG",
              "f32|IMUN:ACC_BIAS",
              "f32|IMUN:ACC_BIAS:STDFULL",
              "f32|IMUN:GYR_BIAS",
              "f32|IMUN:GYR_BIAS:STDFULL",
              "i16|dgnssX:FX",
              "i16|dgnssX:FXWL",
              "f32|dgnssX:AMB:NAN","f32|dgnssX:AMB:STDFULL",              
              "f32|dgnssX:TRO:NAN","f32|dgnssX:TRO:STDFULL",              
              "f32|dgnssX:ION:NAN","f32|dgnssX:ION:STDFULL",
              "f32|cons:RPY",
              "f32|cons:RPY:STDFULL",
              "f32|cons:LEV",
              "f32|cons:LEV:STDFULL",
              "f32|cons:OMEGA",
              "f32|cons:OMEGA:STDFULL",
              "f32|POSN:STDFULL",
              "f32|VELN:STDFULL",
              "f32|ATTN:STDFULL"
            ],
            "sinks": ["~>TC_EKF"]
          },
          {
            "name": "RTSLog_for_CSV",
            // "trigger" : "RTS_TIME",    "dt"      : 1.0,            
            "trigger" : "RTS_TIME",    "dt"      : 0.2,            
            // "trigger" : "RTS_MEAS",    "sensor"      : "dgnssX",            
            // "trigger" : "RTS_MEAS",    "sensor"      : "cons",
            "fields": [
              "i64|T",                 "u16|WEEK",             "f64|GPSSOW",              
              "d|LLH",
              "d|V_NED",
              "d|V_FRD",
              "d|RPY",
              "d|COG",
              "f32|IMUN:ACC_BIAS",
              "f32|IMUN:GYR_BIAS",
              "f32|dgnssX:AMB:NAN","f32|dgnssX:AMB:STDFULL",              
              "f32|dgnssX:TRO:NAN","f32|dgnssX:TRO:STDFULL",              
              "f32|dgnssX:ION:NAN","f32|dgnssX:ION:STDFULL",
              "f32|cons:RPY",
              "f32|cons:RPY:STDFULL",
              "f32|cons:LEV",
              "f32|cons:LEV:STDFULL",
              "f32|cons:OMEGA",
              "f32|cons:OMEGA:STDFULL",
              "f32|POSN:STDFULL",
              "f32|VELN:STDFULL",
              "f32|ATTN:STDFULL"
            ],
            "sinks": ["~>TC_RTS_FULL"]
          },
          {
            "name": "RTSLog_for_KML",
            // "trigger" : "RTS_TIME",    "dt"      : 1.0,            
            "trigger" : "RTS_TIME",    "dt"      : 0.2,
            // "trigger" : "RTS_MEAS",    "sensor"      : "dgnssX",            
            // "trigger" : "RTS_MEAS",    "sensor"      : "cons",
            "fields": [              
              "d|LLH"
            ],
            "sinks": ["~>TC_RTS4KML"]
          },
          {
            "name": "GNSS-Info-and-Stats",
            "trigger" : "EKF_AFTER_PASS", "sensor"  : "dgnssX",
            "fields": [
              "i32|dgnssX:GPRNS",              
              "i32|dgnssX:NUMFREQS",              
              "i32|dgnssX:GNSSES",              
              "i32|dgnssX:CHANNELS",              
              "i32|dgnssX:FREQS",              
              "f64|dgnssX:FREQSHZ",              
              "f64|dgnssX:LAMBDA",
              "u32|dgnssX:NUM_OBS_ACCUMULATED",
              "u32|dgnssX:NUM_IN_ACCUMULATED",
              "u32|dgnssX:NUM_OUT_ACCUMULATED",
              "u32|dgnssX:NUM_STUCK_ACCUMULATED"
            ],
            "sinks": ["~>DGNSS_STATS"]
          }             
        ],

        "settings" : {
          "t_first": "$PROCESSING_FROM$" ,
          "t_final": "$PROCESSING_UNTIL$",    //          "t_duration"  : [1000.0],
          "forward": "$FORWARD_TC$",          
          "dt_predict": 0.1,
          "show_all_logs": false,
          "rts_solver_method": 7,      //["$$",[["rtssolver",[7]]],[0,1,2,3,4,5,6,7]],     // 7 is ldlt   !!! (zumindest!) 1 und 3 erzeugen falschen GYR_BIAS !!!
          "ekf_solver_method": 7,      //["$$",[["ekfsolver",[7]]],[0,1,2,3,4,5,6,7]],     // 7 is ldlt
          "passes" : "$PASSES_TC$",
          "init_rpy_degrees"    : [0.0, 0.0, ["$FORWARD_TC$?","$INIT_HEADING_DEGRESS_FORWARD$","$INIT_HEADING_DEGRESS_REVERSE$"]],          "init_rpy_std_degrees": [1.0,1.0,1.0]
          // "init_rpy_degrees"    : [0.0, 0.0, 0.0],          "init_rpy_std_degrees": [1.0,1.0,60.0]
        }
      }
    },   // end of tightly-coupled Starling Module



    
    // ==============================================================================================================================
    // ==============================================================================================================================
    // =========================================                                         ============================================
    // ========================================     OUTPUT TIGHTLY RESULTS INTO FILES     ===========================================
    // =========================================                                         ============================================
    // ==============================================================================================================================
    // ==============================================================================================================================
    {
      "name"    : "EKF_into_MTIX",
      "app"     : "MOD_SDC2MTIX",
      "args"    : {
        "enable"  : "$DEV_OUTPUTS$",
        // "enable"  : false,
        "in"      : "<~~TC_EKF@__ALL",
        "output_files" : [
          {
            "path"    : "$DEVOUTPUTFOLDER$",
            "filename": "tightly_EKF.MTIX",
            "type"    : "mtix",
            "visible" : false,
            "required": false
          }
        ]   
      }
    },    
    {
      "name"    : "DGNSSInfo_into_MTIX",
      "app"     : "MOD_SDC2MTIX",
      "args"    : {
        "enable"  : "$DEV_OUTPUTS$",
        // "enable"  : false,
        "in"      : "<~~DGNSS_STATS@__ALL",
        "output_files" : [
          {
            "path"    : "$DEVOUTPUTFOLDER$",
            "filename": "dgnss_info.MTIX",
            "type"    : "mtix",
            "visible" : false,
            "required": false
          }
        ]   
      }
    },    
    {
      "name"    : "RTS4KML_into_TIX",
      "app"     : "MOD_SDC2TIX",
      "args"    : {
        "enable"  : "$DEMO_OUTPUTS$",
        "in"      : "<~TC_RTS4KML",
        "output_files" : [
          {
            "path"    : "$OUTPUTFOLDER$",
            "filename": "tc_$DSNAME$_4kml.TIX",
            "type"    : "tix4kml",
            "visible" : false,
            "required": true
          }
        ]          
      }
    },    
    {
      "name"    : "RTS_into_MTIX",
      "app"     : "MOD_SDC2MTIX",
      "args"    : {
        "enable"  : "$DEV_OUTPUTS$",
        "in"    : "<~~TC_RTS_FULL@__ALL",
        "output_files" : [
          {
            "path"    : "$DEVOUTPUTFOLDER$",
            "filename": "tightly_RTS.MTIX",
            "type"    : "mtix",
            "visible" : false,
            "required": false
          }
        ]          
      }
    },    
    {
      "name"    : "RTS_into_MultiKML",
      "app"     : "MOD_SDC2KML",
      "args"    : {
        "enable"  : "$DEV_OUTPUTS$",
        "colin"        : "<~~TC_RTS4KML@__ALL",
        "output_files" : [
          {
            "path"    : "$DEVOUTPUTFOLDER$",
            "filename": "tightly.kml",
            "type"    : "kml",
            "visible" : false,
            "required": false
          }
        ],          
        "kml_name"                          : "$KML_NAME$",  //"tightly-coupled DGNSS+IMU Solution",
        "document_include"                  : "<open>1</open><gx:balloonVisibility>0</gx:balloonVisibility>",
        "placemark_include"                 : "",
        "placemark_name"                    : "Tightly result for ",
        // "placemark_names"                   : "$[]DSNAME@F$",
        "multicolors"                       : ["FF0062FC", "FFFF0000", "FF00FF00", "FF0000FF", "FFFFFF00", "FFFF00FF", "FF00FFFF", "FFFFFFFF"],
        "placemark_description"             : "",
        "static"                            : false,
        "add_links_to_placemark_name"       : true,
        "add_links_to_placemark_description": false,
        "linestyle_width"         : 1,
        "folders"                 : ["fxs","usewif","fxoutreset","wlfx","zupt","stucky","mumax","fxpdopsq","fw"],        // "polystyle_color"         : "7f00ff00",        // "linestyle_color"         : "ffffffff",
        "linestyle_color"         : "FF00FF00",
        "color_mode"              : "normal",   // "random",   // "random" or "normal"(default)
        "linestring_extrude"      : 0,
        "linestring_tessellate"   : 0,
        "linestring_altitudeMode" : "clampToGround"   //"absolute"
      }
    },

    {
      "name"    : "RTS_into_SingleKML",
      "app"     : "MOD_SDC2KML",
      "args"    : {
        "enable"      : "$DEMO_OUTPUTS$",
        "in"          : "<~TC_RTS4KML",
        "output_files": [
          {
            "path"    : "$OUTPUTFOLDER$",
            "filename": "tc_$DSNAME$.kml",
            "type"    : "kml",
            "visible" : true,
            "required": true
          }
        ],          
        "kml_name"                          : "$KML_NAME$",  //"tightly-coupled DGNSS+IMU Solution",
        "document_include"                  : "<open>1</open><gx:balloonVisibility>0</gx:balloonVisibility>",
        "placemark_include"                 : "",
        "placemark_name"                    : "$DSNAME$",
        "placemark_description"             : "",
        "static"                            : false,
        "add_links_to_placemark_name"       : true,
        "add_links_to_placemark_description": false,
        "linestyle_width"         : 3,
        // "polystyle_color"         : "7f00ff00",
        // "linestyle_color"         : "ffffffff",
        "linestyle_color"         : "FF00FF00",
        "color_mode"              : "normal",   // "random",   // "random" or "normal"(default)
        "linestring_extrude"      : 0,
        "linestring_tessellate"   : 0,
        "linestring_altitudeMode" : "clampToGround"   //"absolute"
      }
    },

    {
      "name"    : "LC_into_CSV",
      "app"     : "MOD_SDC2CSV",//      "use_vars": ["$DSNAME$"],
      "args"    : {
        "enable"  : "$DEMO_OUTPUTS$",
        "in"      : "<~TC_RTS_FULL",
        "output_files" : [
          {
            "path"    : "$OUTPUTFOLDER$",   // NOTIZ: im Kern: leerer Path -> KEIN '/' einfügen.
            "filename": "tc_$DSNAME$.csv",
            "type"    : "csv_pva",
            "visible" : true,
            "required": true
          }
        ],         
        "welcome_message"         : "===================================================\nAlgoNav DGNSS/IMU for data set $DSNAME$\n===================================================",
        "delim"                   : ", ",
        "eol"                     : "\n",
        "delimiter_after_last_col": false,
        "output_full_header"      : true,
        "output_header_row"       : true,
        "comment_prefix"          : "# ",
        "columns"                 : [
          {"src":"WEEK"        , "ind": 0, "name": "GPSWeek"      , "fmt": "%4u"   ,  "unit": ""   ,                    "descr": "GPS week number (counting since Jan 6th, 1980, 00:00:00)"          },
          {"src":"GPSSOW"      , "ind": 0, "name": "GPSSecOfWeek" , "fmt": "%14.6f",  "unit": "s"  ,                    "descr": "GPS seconds since beginning of current GPS week (Sunday 00:00:00)" },
          {"src":"LLH"         , "ind": 0, "name": "Latitude"     , "fmt": "%14.9f",  "unit": "deg", "to_degrees":true, "descr": "ITRF2020 Latitude"                                                 },
          {"src":"LLH"         , "ind": 1, "name": "Longitude"    , "fmt": "%14.9f",  "unit": "deg", "to_degrees":true, "descr": "ITRF2020 Longitude"                                                },
          {"src":"LLH"         , "ind": 2, "name": "h"            , "fmt": "%10.4f",  "unit": "m"  ,                    "descr": "GRS80 ellipsoidal height"                                          },
          {"src":"POSN:STDFULL", "ind": 0, "name": "SD_North"     , "fmt": "%8.3f" ,  "unit": "m"  ,                    "descr": "1-sigma North/South uncertainty"                    , "factor": 3.0},
          {"src":"POSN:STDFULL", "ind": 1, "name": "SD_East"      , "fmt": "%8.3f" ,  "unit": "m"  ,                    "descr": "1-sigma West/East   uncertainty"                    , "factor": 3.0},
          {"src":"POSN:STDFULL", "ind": 2, "name": "SD_h"         , "fmt": "%8.3f" ,  "unit": "m"  ,                    "descr": "1-sigma vertical uncertainty"                       , "factor": 3.0},
          {"src":"V_NED"       , "ind": 0, "name": "v_North"      , "fmt": "%9.4f" ,  "unit": "m/s",                    "descr": "North Velocity"                                     , "factor": 1.0},
          {"src":"V_NED"       , "ind": 1, "name": "v_East"       , "fmt": "%9.4f" ,  "unit": "m/s",                    "descr": "East  Velocity"                                     , "factor": 1.0},
          {"src":"V_NED"       , "ind": 2, "name": "v_Up"         , "fmt": "%9.4f" ,  "unit": "m/s",                    "descr": "Up    Velocity"                                     , "factor":-1.0},
          {"src":"VELN:STDFULL", "ind": 0, "name": "SD_vNorth"    , "fmt": "%6.4f" ,  "unit": "m/s",                    "descr": "1-sigma North Velocity uncertainty"                 , "factor": 3.0},
          {"src":"VELN:STDFULL", "ind": 1, "name": "SD_vEast"     , "fmt": "%6.4f" ,  "unit": "m/s",                    "descr": "1-sigma East  Velocity uncertainty"                 , "factor": 3.0},
          {"src":"VELN:STDFULL", "ind": 2, "name": "SD_vUp"       , "fmt": "%6.4f" ,  "unit": "m/s",                    "descr": "1-sigma Up    Velocity uncertainty"                 , "factor": 3.0},
          {"src":"RPY"         , "ind": 1, "name": "Pitch"        , "fmt": "%9.4f" ,  "unit": "deg", "to_degrees":true, "descr": "Roll  angle (positive=right side down)"             , "factor": 1.0},
          {"src":"RPY"         , "ind": 0, "name": "Roll"         , "fmt": "%9.4f" ,  "unit": "deg", "to_degrees":true, "descr": "Pitch angle (positive=front up)"                    , "factor": 1.0},
          {"src":"RPY"         , "ind": 2, "name": "Heading"      , "fmt": "%9.4f" ,  "unit": "deg", "to_degrees":true, "descr": "Heading angle (0=North, 90=East)"                   , "factor": 1.0},
          {"src":"ATTN:STDFULL", "ind": 0, "name": "SD_AttN"      , "fmt": "%7.4f" ,  "unit": "deg", "to_degrees":true, "descr": "1-sigma attitude uncertainty about North axis"      , "factor": 3.0},
          {"src":"ATTN:STDFULL", "ind": 1, "name": "SD_AttE"      , "fmt": "%7.4f" ,  "unit": "deg", "to_degrees":true, "descr": "1-sigma attitude uncertainty about East  axis"      , "factor": 3.0},
          {"src":"ATTN:STDFULL", "ind": 2, "name": "SD_AttH"      , "fmt": "%7.4f" ,  "unit": "deg", "to_degrees":true, "descr": "1-sigma heading uncertainty"                        , "factor": 3.0}          
        ]
      }
    }  


  ],

  


  // main DAC-Process settings (if any)
  "settings": {  }
  
}
