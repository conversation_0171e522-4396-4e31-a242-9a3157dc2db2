FROM ubuntu:24.04

# Arbeitsverzeichnis
WORKDIR /app

# System- und Python-Abhängigkeiten installieren (Beispiel)
COPY requirements.txt .
# System-Pakete, Python & pip
RUN apt-get update && \
    apt-get install -y --no-install-recommends \
        python3 python3-pip python3-venv \
        build-essential ca-certificates python-is-python3 && \
    rm -rf /var/lib/apt/lists/*


COPY . /app
RUN python3 -m pip install --break-system-packages --no-cache-dir -r requirements.txt



ENV PYTHONUNBUFFERED=1

# EXPOSE 8000

CMD ["python", "main.py"]
