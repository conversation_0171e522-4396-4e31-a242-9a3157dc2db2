FROM --platform=linux/amd64 ubuntu:latest
# Avoid timezone prompt during installation
ENV DEBIAN_FRONTEND=noninteractive

# Install Python and system dependencies
RUN apt-get update && apt-get install -y \
    python3 \
    python3-pip \
    python3-venv \
    python3-dev \
    gcc-13 \
    g++-13 \
    build-essential \
    libstdc++6 \
    libstdc++-13-dev \
    libc6 \
    libc6-dev \
    && rm -rf /var/lib/apt/lists/*

# Set gcc-13 as default to match GitHub Actions environment
RUN update-alternatives --install /usr/bin/gcc gcc /usr/bin/gcc-13 100 \
    && update-alternatives --install /usr/bin/g++ g++ /usr/bin/g++-13 100

# Set C++ standard and optimization flags to match the build environment
ENV CXXFLAGS="-std=c++20 -O3"
ENV LD_LIBRARY_PATH="/usr/local/lib:/usr/lib/x86_64-linux-gnu:${LD_LIBRARY_PATH}"

WORKDIR /app

# Create and activate virtual environment
RUN python3 -m venv /opt/venv
ENV PATH="/opt/venv/bin:$PATH"

# Copy and install requirements in virtual environment
COPY requirements.txt .
RUN . /opt/venv/bin/activate && pip install --no-cache-dir -r requirements.txt

ENV PYTHONUNBUFFERED=1

# The actual source code will be mounted at runtime
CMD ["/opt/venv/bin/python", "main.py"]
