import os
from dotenv import load_dotenv

load_dotenv()
url: str = os.environ.get("SUPABASE_URL")
key: str = os.environ.get("SUPABASE_ANON_KEY")
service_key: str = os.environ.get("SUPABASE_SERVICE_KEY")
rt_url: str = os.environ.get("SUPABASE_RT_URL")
bucket_name: str = os.environ.get("BUCKET_NAME")
# Renamed from job_folder to reflect that it holds data for individual tasks (old jobs)
task_folder: str = os.environ.get("TASK_FOLDER", "/app/tasks").rstrip('/') # Added default value
precision_folder: str = os.environ.get("PRECISION_FOLDER").rstrip('/')
is_dev: bool = os.environ.get("ISDEV", "false").lower() == "true"
schema: str = os.environ.get("SUPABASE_SCHEMA", "public")
output_bucket_name: str = os.environ.get("OUTPUT_BUCKET_NAME", "results")
dev_output_bucket_name: str = os.environ.get("DEVOUTPUT_BUCKET_NAME", "results")
max_processes: int = int(os.environ.get("MAX_WORKER_CORES", 30))
concurrent_threads_per_worker_core: int = int(os.environ.get("CONCURRENT_THREADS_PER_WORKER_CORE", 4))

# Race condition prevention settings
task_fetch_limit_multiplier: int = int(os.environ.get("TASK_FETCH_LIMIT_MULTIPLIER", "2"))  # Fetch 2x available workers
worker_sleep_time: float = float(os.environ.get("WORKER_SLEEP_TIME", "0.1"))  # Sleep time between task checks
use_skip_locked: bool = os.environ.get("USE_SKIP_LOCKED", "true").lower() == "true"  # Use SKIP LOCKED approach

