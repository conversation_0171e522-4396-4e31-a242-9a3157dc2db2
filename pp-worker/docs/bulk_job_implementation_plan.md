# Plan to Implement Bulk/Bridge Task Processing in `pp-worker`

This document outlines the plan for adding support for processing bulk/bridge jobs (e.g., multi-KML generation) within the `pp-worker` application.

## 1. Modify `pp-worker/job_processing.py` (`JobProcessor` class)

*   **Update `_handle_job_based_on_type(self)`:**
    *   Add logic to detect bulk jobs via `self.job.get('bulk_job_type')`.
    *   Route detected bulk jobs to a new method `self._process_bulk_job()`.
    *   Explicitly handle 'standard' jobs (e.g., check if `bulk_job_type` is null *and* `job_json['type']` is not 'dummy').
    ```python
    # Example modification in _handle_job_based_on_type
    def _handle_job_based_on_type(self):
        bulk_type = self.job.get('bulk_job_type')
        job_type = self.job.get('job_json', {}).get('type')

        if bulk_type:
            logging.info(f"Processing Bulk Job {self.job['id']} of type: {bulk_type}")
            self._process_bulk_job()
        elif job_type == 'dummy':
            self._process_dummy_job()
        else: # Assuming other types are standard processing jobs
            logging.info(f"Processing Standard Job {self.job['id']}")
            self._process_standard_job()
    ```

*   **Implement `_process_bulk_job(self)`:**
    *   Orchestrate the steps for bulk jobs:
        1.  Call `self._setup_storage_for_bulk()` (New).
        2.  Call `self._setup_output_files()` (Existing - defines the *output* of the bulk job).
        3.  Call `self._process_bulk_input_files()` (New - fetches *inputs* which are results of previous tasks).
        4.  Call `self._run_core()` (Existing - needs `vars` populated correctly).
        5.  Call `self._store_output_files()` (Existing - stores the final output).
        6.  Call `self._validate_output_files()` (Existing - validates the final output).
    *   Include error handling.

*   **Implement `_setup_storage_for_bulk(self)`:**
    *   Instantiate `storage.SuperbaseStorage` for reading inputs from `output_bucket_name`. Store as `self.result_input_store`.
    *   Instantiate `storage.SuperbaseStorage` for writing outputs to `output_bucket_name`. Store as `self.outputstore`.
    *   Instantiate `storage.SuperbaseStorage` for writing dev outputs to `dev_output_bucket_name` (if applicable). Store as `self.devoutputstore`.

*   **Implement `_process_bulk_input_files(self)`:**
    1.  **Get Job Info & Setup:** Get `bulk_job_id`, define `job_path`, create directory.
    2.  **Access & Validate Workervars:** Get `workervars_data = self.job.get('workervars', {}).get('data', [])`. Validate it's a non-empty list.
    3.  **Extract Result IDs and Input Structure:**
        *   Iterate through `workervars_data`.
        *   Extract `job_results` IDs from `output_files_by_type` for each task entry.
        *   Collect unique IDs in `result_ids_to_fetch`.
        *   Build `structured_input_data_for_vars` list, temporarily storing `result_id` for each file type.
        *   Keep track of context in `result_id_to_file_info`.
    4.  **Query Database for Paths:** Call `database.get_result_file_paths(list(result_ids_to_fetch))` (new function) to get `result_paths_map` (`result_id` -> `file_path` from `job_results` table). Validate all IDs were found.
    5.  **Prepare for Download:** Create `result_id_to_local_path` map using `os.path.join(job_path, os.path.basename(remote_path))`.
    6.  **Download Files:** Use `self.result_input_store.get_files(list(result_paths_map.values()), local_folder=job_path)`. Verify downloads.
    7.  **Decompression (If Necessary):** Call `self.decompress_files` and update `result_id_to_local_path` map if files were decompressed.
    8.  **Populate `vars` for Core:**
        *   Iterate through `structured_input_data_for_vars`.
        *   Replace `result_id` placeholders with the final `local_path` from `result_id_to_local_path`.
        *   Use `self.set_var(self.job['vars'], 'TASK_RESULT_LOCAL', structured_input_data_for_vars)` to inject the structure.
        *   Set other necessary variables (e.g., `OUTPUTFILENAME`) using `self.set_var`.

## 2. Modify `pp-worker/database.py`

*   Add new function `get_result_file_paths(result_ids: list[int]) -> dict[int, str]`:
    *   Takes a list of `job_results` IDs.
    *   Queries the `job_results` table: `SELECT id, file_path FROM job_results WHERE id = ANY(%s)`.
    *   Returns a dictionary mapping each `id` to its `file_path`.
    *   Include error handling.

## 3. Verify `pp-worker/storage.py`

*   Ensure `SuperbaseStorage.get_files` correctly downloads files using the full paths stored in `job_results.file_path` from the `output_bucket_name`.

## 4. Verify `pp-worker/config.py`

*   Confirm `output_bucket_name` and potentially `dev_output_bucket_name` are correctly defined.

## 5. Error Handling & Status Updates

*   Implement robust error handling in all new methods.
*   Fail the job using `_job_failed()` if required inputs are missing or errors occur.
*   Update job status appropriately throughout the process.

## Conceptual Flow Diagram

```mermaid
graph TD
    subgraph Worker Main Thread
        A[main.py: Fetch Job] --> B{Job Type?};
        B -- Standard --> C[Submit _process_standard_job];
        B -- Bulk --> D[Submit _process_bulk_job];
        B -- Dummy --> E[Submit _process_dummy_job];
    end

    subgraph Job Processor Process
        direction LR
        D1[Start _process_bulk_job] --> D2[_setup_storage_for_bulk];
        D2 --> D3[_setup_output_files];
        D3 --> D4[_process_bulk_input_files];
        subgraph _process_bulk_input_files
            D4a[Parse workervars] --> D4b[Extract Result IDs];
            D4b --> D4c[DB: Get Result Paths];
            D4c --> D4d[Storage: Download Result Files];
            D4d --> D4e[Populate Job Vars with Local Paths];
        end
        D4 --> D5[_run_core];
        D5 --> D6[_store_output_files];
        D6 --> D7[_validate_output_files];
        D7 --> D8{Success?};
        D8 -- Yes --> D9[_finalize_job];
        D8 -- No --> D10[_job_failed];
    end

    style Worker Main Thread fill:#f9f,stroke:#333,stroke-width:2px
    style Job Processor Process fill:#ccf,stroke:#333,stroke-width:2px