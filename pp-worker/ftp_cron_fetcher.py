import os
import time
from ftplib import FTP
from datetime import datetime, timedelta

# FTP-Server Details
FTP_SERVER = "ftp.aiub.unibe.ch"
FTP_DIRECTORY = "/CODE"

# Lokales Verzeichnis zum Speichern von Dateien
LOCAL_DIRECTORY = "./CODE"


max_file_age = datetime.now() - timedelta(days=7)

# Liste der Verzeichnisse, die durchsucht werden sollen
directories = [FTP_DIRECTORY, f"{FTP_DIRECTORY}/{datetime.now().year}"]

while True:
    try:
        # Verbindung zum FTP-Server herstellen
        
        ftp = FTP(FTP_SERVER, timeout=120)
        #ftp.set_pasv(False)
        ftp.login()

        for directory in directories:
            try:
                print(f"Processing directory {directory}")
                ftp.cwd(directory)
                for filename, meta in ftp.mlsd():
                    try:
                        if meta['type'] == 'file':
                            modified_time = datetime.strptime(meta['modify'], "%Y%m%d%H%M%S")

                            # Überprüfen, ob die Datei neuer als einen Monat ist
                            if modified_time > max_file_age:
                                local_subdirectory = directory.replace(FTP_DIRECTORY, '').lstrip('/')
                                local_directory = os.path.join(LOCAL_DIRECTORY, local_subdirectory)
                                os.makedirs(local_directory, exist_ok=True)
                                local_file = os.path.join(local_directory, filename)

                                # Überprüfen, ob die Datei bereits heruntergeladen wurde
                                if not(os.path.exists(local_file)) or filename.split('.')[0] == 'COD0OPSULT':
                                    # Datei herunterladen
                                    with open(local_file, 'wb') as f:
                                        ftp.retrbinary('RETR ' + filename, f.write)
                                    print(f"Downloaded {filename}")
                                else:
                                    print(f"File {filename} already exists")
                    except Exception as e:
                        print(f"Error processing file {filename}: {e}")
            except Exception as e:
                print(f"Error processing directory {directory}: {e}")
                raise e
        break
    except Exception as e:
        print(f"Error connecting to FTP server: {e}")
        print("Retrying in 5 minutes...")
        time.sleep(300)

# Verbindung zum FTP-Server schließen
ftp.quit()
