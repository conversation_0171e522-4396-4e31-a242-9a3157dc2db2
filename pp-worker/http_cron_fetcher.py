import os
import time
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin
from datetime import datetime, timedelta

# URL des Verzeichnisses, das Sie herunterladen möchten
url = "http://ftp.aiub.unibe.ch/CODE/"
urlmgex = "http://ftp.aiub.unibe.ch/CODE_MGEX/CODE/"

# Lokales Verzeichnis zum Speichern von Dateien
local_directory = "/data/CODE"

# Liste der Verzeichnisse, die durchsucht werden sollen
directories = [f"{urlmgex}{year}/" for year in range(2014, datetime.now().year+1)]

directories.append(url)
current_time = time.time()
try:
    for file in os.listdir(local_directory):
        file_path = os.path.join(local_directory, file)
        if os.path.isfile(file_path):
            file_age = current_time - os.path.getmtime(file_path)
            if file_age > 14 * 24 * 60 * 60:  # 14 days in seconds
                os.remove(file_path)
                print(f"Deleted old file: {file_path}")
except Exception as e:
    print(f"Error deleting old files: {e}")

while True:
    try:
        for directory in directories:
            try:
                print(f"Processing directory {directory}")

                # HTTP-Anfrage senden
                response = requests.get(directory)
                response.raise_for_status()  # Stellt sicher, dass die Anfrage erfolgreich war

                # HTML parsen
                soup = BeautifulSoup(response.text, 'html.parser')
                local_subdirectory = directory.replace(urlmgex, '').lstrip('/')
                local_subdirectory = local_subdirectory.replace(url, '').lstrip('/')
                local_directory_path = os.path.join(local_directory, local_subdirectory)
                os.makedirs(local_directory_path, exist_ok=True)
                # Alle Links im HTML finden
                for link in soup.find_all('a'):
                    try:
                        href = link.get('href')

                        # Überprüfen, ob der Link auf eine Datei verweist
                        if not href.endswith('/'):
                            # Vollständige URL der Datei erstellen
                            file_url = urljoin(directory, href)

                            # Lokale Datei-Pfad erstellen
                            
                            local_file = os.path.join(local_directory_path, href)

                            # Überprüfen, ob die Datei bereits heruntergeladen wurde, wenn Dateiname (vor der Extension) gleich COD0OPSULT ist, lade die Datei trotzdem herunter
                            if not os.path.exists(local_file) or href.split('.')[0] == 'COD0OPSULT':
                                #only download files that end with 01D_05M_ORB.SP3 or 01D_30S_CLK.CLK 
                                if href.endswith('.SP3.gz') or href.endswith('.EPH.Z') or href.endswith('.CLK.gz') or href.endswith('.CLK.Z') or href.endswith('.CLK') or href.endswith('.SP3'): #TODO: für alte Rinex Namenskonventionen anpassen
                                    # Datei herunterladen
                                    response = requests.get(file_url)
                                    response.raise_for_status()  # Stellt sicher, dass die Anfrage erfolgreich war

                                # Datei speichern
                                    with open(local_file, 'wb') as f:
                                        f.write(response.content)

                                    print(f"Downloaded {href}")
                            else:
                                print(f"File {href} already exists")
                    except Exception as e:
                        print(f"Error processing file {href}: {e}")
            except Exception as e:
                print(f"Error processing directory {directory}: {e}")
                raise e
        break
        # Delete files older than 14 days
    

    except Exception as e:
        print(f"Error connecting to HTTP server: {e}")
        print("Retrying in 5 minutes...")
        time.sleep(300)