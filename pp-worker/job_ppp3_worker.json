{"vars": [{"data": [true, false], "name": "FORWARD", "links": [["dir", [0]]]}, {"data": "", "name": "CLOCKFILES", "links": []}, {"data": "", "name": "SP3FILES", "links": []}, {"data": "", "name": "RNXFILE", "links": []}, {"data": "", "name": "OUTPUTFOLDER", "links": []}, {"data": "", "name": "DEVOUTPUTFOLDER", "links": []}, {"data": "./ATX/igs20.atx", "name": "ATXFILE", "links": []}, {"data": ["G", "E", "R", "C"], "name": "ENABLE_GNSS", "links": []}, {"data": false, "name": "STATIC", "links": []}], "process": [{"app": "MOD_GNSS_OBS_READER", "args": {"output": "~>GNSSOBS_ROVER", "rnxfile": "$RNXFILE$"}, "name": "Read_Rover_Observations", "enable": true}, {"app": "MOD_GNSS_PREC_READER", "args": {"clkfiles": "$CLOCKFILES$", "sp3files": "$SP3FILES$", "out_clocks": "~>GNSS_CLOCKS", "out_orbits": "~>GNSS_ORBITS", "polydegree": 9}, "name": "Read_Precise_Files", "enable": true}, {"app": "MOD_GNSS_ANTEX_READER", "args": {"output": "~>ANTEX", "in_rawobs": "<~GNSSOBS_ROVER", "sat_antex_file": "$ATXFILE$"}, "name": "Read_ANTEX", "enable": true}, {"app": "MOD_GNSS_PPP_ESC_SELECT", "args": {"out_esc": "~>ESC", "in_clocks": "<~GNSS_CLOCKS", "in_orbits": "<~GNSS_ORBITS", "in_rawobs": "<~GNSSOBS_ROVER", "disable_gprns": [], "enable_gnss": "$ENABLE_GNSS$", "freqs_priority_lists": {"C": [[2], [6, 7]], "E": [[1], [5, 7, 8]], "G": [[1], [2, 5]], "R": [[1], [2]]}}, "name": "ESC_Selection", "enable": true}, {"app": "MOD_GNSS_PREPARE", "args": {"in_esc": "<~ESC", "out_pp": "~>ROVER_PREPROCESSED", "out_spp": "~>ROVER_SPP", "in_antex": "<~ANTEX", "settings": {"rco_factor": 0, "switch_rcc": 1, "show_charts": false, "elev_mask_deg": 10, "switch_pcosat": 1, "switch_pwusat": 1, "switch_ztddry": 1, "tof_iterations": 1, "spp_ot_thresh_m": 100, "csd_thresh_meters": 0.05, "min_sun_sat_angle_deg": 0, "correct_codephase_with_rcb": 1, "correct_codephase_with_scb": 1, "disable_phase_during_eclipse": true}, "in_clocks": "<~GNSS_CLOCKS", "in_orbits": "<~GNSS_ORBITS", "in_rawobs": "<~GNSSOBS_ROVER"}, "name": "GNSS-Preprocessor", "enable": true}, {"app": "MOD_STARLING", "args": {"outputs": [{"name": "PPP_CUPT_RTSLog", "sinks": ["~>PPP_CUPT"], "fields": ["i64|T", "d|LLH", "f32|V_NED", "f32|POSN:STDFULL", "u8|ppp:AMB:NDEST", "f32|ppp:AMB:NAN", "f32|ppp:AMB:STDFULL", "f32|ppp:TRO", "f32|ppp:TRO:STDFULL", "f32|ppp:RCB", "f32|ppp:RCB:STDFULL", "f32|ppp:ISB", "f32|ppp:ISB:STDFULL"], "sensor": "ppp", "trigger": "RTS_MEAS"}, {"name": "PPP_CUPT_EKFLog", "sinks": ["~>PPP_CUPT_EKF"], "fields": ["i64|T", "d|LLH", "f32|V_NED", "f32|POSN:STDFULL", "u8|ppp:AMB:NDEST", "f32|ppp:AMB:NAN", "f32|ppp:AMB:STDFULL", "f32|ppp:TRO", "f32|ppp:TRO:STDFULL", "f32|ppp:RCB", "f32|ppp:RCB:STDFULL", "f32|ppp:ISB", "f32|ppp:ISB:STDFULL", "f32|ppp:RESIDUALS"], "sensor": "ppp", "trigger": "EKF_AFTER_CLEANUP"}], "sensors": [{"in": "<~ROVER_PREPROCESSED", "name": "ppp", "type": "PPP", "in_esc": "<~ESC", "in_spp": "<~ROVER_SPP", "states": [{"init": [0, 0, 0], "name": "LEV", "std0": [], "estax": 0}], "settings": {"amb_std0": 1000000, "isb_std0": 10, "rcb_std0": 100000, "tro_init": 0.1, "tro_std0": 0.03, "isb_sqrtq": 0, "ot_thresh": ["$$", [["ot", [1]]], [2, 2.25, 2.5, 3, 5, 10]], "rcb_sqrtq": 100000, "std0_code": ["$$", [["std0co", [0]]], [0.3, 0.5, 0.75, 1, 1.5, 2, 2.5, 3, 5]], "tro_sqrtq": ["$$", [["trorw", [1]]], [0, 0.0001666]], "std0_phase": ["$$", [["std0ph", [0]]], [0.005, 0.0075, 0.01, 0.015, 0.02]], "disable_phase": false, "elev_mask_deg": ["$$", [["elm", [4]]], [0, 5, 7.5, 10, 12.5, 15, 17.5]], "ot_sigma_posrcb": 100, "verbose_outliers": false, "internal_adaption": false, "min_num_sat_noskip": 5, "variance_inflation": 2500, "min_num_sat_adaptive": 8, "min_num_sat_noinflate": 6, "adaptivity_attenuation_code": 0, "adaptivity_attenuation_phase": 0, "reset_all_ambiguities_on_skip": true}}], "settings": {"passes": 2, "forward": "$FORWARD$", "dt_predict": 2, "show_all_logs": false}, "predictor": {"llh0": [0, 0, 0], "std0": 100000, "type": ["$STATIC$?", "CONSTPOS", "POS"], "sqrt_q": 1000}}, "name": "Starling_PPP", "enable": true}, {"app": "MOD_SDC2KML", "args": {"in": "<~~PPP_CUPT@__ALL", "out": "$OUTPUTFOLDER$/ppp.kml", "linestyle_width": 4, "polystyle_color": "7f00ff00", "linestring_extrude": 0, "linestring_tessellate": 0, "linestring_altitudeMode": "clampToGround", "static": "$STATIC$"}, "name": "Collect_RTS_into_KML", "enable": true}], "settings": {"VERBOSE": 1, "dac_param2": ["hello", "DAC", "world"]}}