# Job Processing Refactoring Plan

## Current Architecture Overview
```mermaid
graph TD
    A[JobProcessor] --> B[process_job]
    B --> C{Job Type}
    C -->|Standard| D[_process_standard_job]
    C -->|Bulk| E[_process_bulk_job]
    C -->|Dummy| F[_process_dummy_job]
    
    D --> G[_setup_storage]
    D --> H[_process_files]
    D --> I[_run_core]
    D --> J[_store_output_files]
    
    E --> K[_setup_storage_for_bulk]
    E --> L[_process_bulk_input_files]
    E --> I
    E --> J
```

## Proposed Refactoring Steps

### 1. Code Organization Improvements
- **Break down long methods**:
  - Split `_process_bulk_input_files()` into:
    - `_collect_required_file_ids()`
    - `_download_bulk_files()`
    - `_update_job_vars_with_paths()`

- **Consolidate duplicate code**:
  - Create shared methods for:
    - File downloading (`_download_files()`)
    - Variable updating (`_update_job_vars()`)
    - Storage setup (`_setup_storage_common()`)

### 2. Type Hinting Implementation
```python
from typing import Dict, List, Optional, Union

class JobProcessor:
    def __init__(self) -> None:
        self.job: Dict = None
        self.output_manager: OutputFileManager = OutputFileManager()
        
    def process_job(self, job: Dict) -> None:
        ...
```

### 3. Error Handling Enhancements
```python
class JobProcessingError(Exception):
    """Base exception for job processing errors"""
    pass

class FileDownloadError(JobProcessingError):
    """Raised when file downloads fail"""
    pass

class CoreExecutionError(JobProcessingError):
    """Raised when core processing fails"""
    pass
```

### 4. Configuration Management
```python
class JobConfig:
    def __init__(self):
        self.job_folder = config.job_folder
        self.bucket_name = config.bucket_name
        self.output_bucket_name = config.output_bucket_name
        self.is_dev = config.is_dev
        
    def get_storage_path(self, job_id: str) -> str:
        return f"{self.job_folder}/{job_id}"
```

### 5. Performance Optimizations
- **Parallel file downloads**:
```python
from concurrent.futures import ThreadPoolExecutor

def _download_files_parallel(self, file_map: Dict[str, str]) -> List[str]:
    with ThreadPoolExecutor() as executor:
        futures = {
            executor.submit(self.storage.get_file, remote, local)
            for remote, local in file_map.items()
        }
        return [f.result() for f in futures]
```

## Implementation Roadmap

1. **Phase 1 (Structural)**:
   - Break down long methods
   - Add type hints
   - Implement custom exceptions

2. **Phase 2 (Functional)**:
   - Consolidate duplicate code
   - Implement configuration class
   - Add parallel processing

3. **Phase 3 (Testing)**:
   - Add unit tests
   - Create integration test scenarios
   - Benchmark performance improvements

## Expected Benefits
- 30-40% reduction in code duplication
- Improved maintainability
- Better error tracking
- Potential 2-3x speedup for bulk operations
- Enhanced developer experience with type hints