import logging
from concurrent.futures import Process<PERSON>oolExecutor, as_completed
import signal
import sys
import time
import threading
import concurrent.futures
import os
from multiprocessing import Event, current_process, Manager
from logging.handlers import Queue<PERSON>and<PERSON>, QueueListener
import config
import database
from job_processing import TaskProcessor # Renamed JobProcessor -> TaskProcessor
from job_processing import flush_all_logs
# Initialize centralized, process-safe logging with dual output
log_queue = Manager().Queue(-1)  # Unbounded queue for log records
root_logger = logging.getLogger()
root_logger.setLevel(logging.INFO)

# Console handler in main process - the only place that writes to stdout
console_handler = logging.StreamHandler(sys.stdout)
console_handler.setFormatter(logging.Formatter('[%(asctime)s] %(levelname)s [%(name)s]: %(message)s'))

# Start the queue listener in main process
queue_listener = QueueListener(log_queue, console_handler)
queue_listener.start()

# Replace all handlers with QueueHandler
root_logger.handlers = [QueueHandler(log_queue)]

# Reduce noise from external libraries
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("supabase").setLevel(logging.WARNING)

class DualOutputHandler(logging.Handler):
    """
    Custom logging handler that sends logs to both:
    1. Central queue for console output (process-safe)
    2. Individual task log file (when task context is available)
    """
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue
        self.queue_handler = QueueHandler(log_queue)
        self.current_task_id = None
        self.current_file_handler = None

    def set_task_context(self, task_id):
        """Set the current task context for file logging"""
        if self.current_task_id == task_id:
            return  # Already set to this task

        # Close previous file handler if exists
        if self.current_file_handler:
            self.current_file_handler.close()
            self.current_file_handler = None

        self.current_task_id = task_id

        if task_id:
            # Create file handler for this task
            try:
                log_dir = f"{config.task_folder}/{task_id}/devoutput"
                os.makedirs(log_dir, exist_ok=True)
                log_file_path = f"{log_dir}/worker.log"

                self.current_file_handler = logging.FileHandler(log_file_path, mode='a')
                self.current_file_handler.setFormatter(
                    logging.Formatter('[%(asctime)s] %(levelname)s [%(name)s]: %(message)s')
                )
            except Exception as e:
                # If file handler creation fails, log to console only
                print(f"Warning: Could not create log file for task {task_id}: {e}", file=sys.stderr)
                self.current_file_handler = None

    def emit(self, record):
        """Emit log record to both console queue and task file"""
        # Always send to central queue for console output
        self.queue_handler.emit(record)

        # Also write to task file if available
        if self.current_file_handler:
            try:
                self.current_file_handler.emit(record)
            except Exception:
                # If file writing fails, continue with console logging only
                pass

    def close(self):
        """Clean up handlers"""
        if self.current_file_handler:
            self.current_file_handler.close()
            self.current_file_handler = None
        super().close()

# Initialize counters and lock
active_workers = 0
active_workers_lock = threading.Lock()
total_workers = config.max_processes

# Initialize shutdown event
shutdown_event = Event()

def signal_handler(sig, frame):
    logging.info("Shutdown signal received. Initiating graceful shutdown...")
    shutdown_event.set()

def run_task(task_data): # Renamed run_job -> run_task, job -> task_data
    """Function to run a task using TaskProcessor.""" # Updated docstring
    task_id = task_data.get('id', 'UNKNOWN')

    if shutdown_event.is_set():
        logging.info(f"Shutdown signal set, skipping task {task_id}")
        return

    # Set task context for dual logging (console + task-specific file)
    try:
        global DUAL_HANDLER
        if 'DUAL_HANDLER' in globals() and DUAL_HANDLER:
            DUAL_HANDLER.set_task_context(task_id)
    except Exception as e:
        # If setting task context fails, continue with console logging only
        logging.warning(f"Could not set task context for logging: {e}")

    # Log task processing start in worker process
    logging.info(f"Starting task processing for task {task_id}")

    processor = None
    try:
        processor = TaskProcessor() # Renamed JobProcessor -> TaskProcessor
        processor.process_task(task_data) # Renamed process_job -> process_task
        logging.info(f"Task {task_id} processing completed successfully")
    except Exception as e:
        logging.error(f"Task {task_id} processing failed with exception: {e}", exc_info=True)
        # Force flush logs immediately when exception occurs
        try:
            flush_all_logs()
        except ImportError:
            # Fallback if import fails
            pass
        raise  # Re-raise to ensure the callback handles it properly
    finally:
        # Force flush logs before cleanup
        try:
            flush_all_logs()
        except ImportError:
            # Fallback if import fails
            pass

        # Clear task context when task is done (optional, for cleaner separation)
        try:
            if 'DUAL_HANDLER' in globals() and DUAL_HANDLER:
                DUAL_HANDLER.set_task_context(None)
        except Exception:
            pass  # Ignore cleanup errors

        # Final flush after cleanup
        try:
            flush_all_logs()
        except ImportError:
            # Fallback if import fails
            pass

def task_done_callback(future, task_id): # Renamed job_done_callback -> task_done_callback, job_id -> task_id
    """Callback function to handle task completion.""" # Updated docstring
    global active_workers
    try:
        # Retrieve the result to catch any exceptions raised during task execution
        future.result()
        # Only log completion for tasks that were actually processed
        # (Tasks that couldn't be locked will not generate detailed logs)
        logging.debug(f"Task {task_id} worker completed.") # Renamed Job -> Task
    except Exception as exc:
        logging.error(f"Task {task_id} generated an exception: {exc}") # Renamed Job -> Task
        # Force flush logs immediately when exception occurs
        try:
            from job_processing import flush_all_logs
            flush_all_logs()
        except ImportError:
            # Fallback if import fails
            pass
    finally:
        with active_workers_lock:
            active_workers -= 1
            logging.info(f"Task {task_id} finished. Active workers: {active_workers}/{total_workers}") # Renamed Job -> Task
        # Final flush after task completion
        try:
            from job_processing import flush_all_logs
            flush_all_logs()
        except ImportError:
            # Fallback if import fails
            pass

def _worker_init(shutdown_event_flag, log_q):
    """Initialize worker process to ignore SIGINT, set global shutdown flag, and configure dual logging."""
    signal.signal(signal.SIGINT, signal.SIG_IGN)
    # Make the shutdown event accessible within the worker process's scope
    # This is used by JobProcessor/TaskProcessor to check for shutdown signals
    global SHUTDOWN_EVENT, DUAL_HANDLER
    SHUTDOWN_EVENT = shutdown_event_flag

    # Configure dual logging for this worker process
    # Create dual handler that writes to both console queue and task files
    DUAL_HANDLER = DualOutputHandler(log_q)
    worker_root = logging.getLogger()
    worker_root.handlers = [DUAL_HANDLER]
    worker_root.setLevel(logging.INFO)

    # Make DUAL_HANDLER available to job_processing module
    import job_processing
    job_processing.DUAL_HANDLER = DUAL_HANDLER
    job_processing.SHUTDOWN_EVENT = SHUTDOWN_EVENT

    # Reduce noise from external libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("supabase").setLevel(logging.WARNING)

    # Log worker process initialization
    logging.info(f"Worker process {current_process().pid} initialized")

def main():
    global active_workers

    # Setup signal handlers for graceful shutdown
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)
    logging.info("Worker starting up...") # Updated log

    with ProcessPoolExecutor(
        max_workers=total_workers,
        initializer=_worker_init,
        initargs=(shutdown_event, log_queue)
    ) as executor:
        futures = {}  # Track submitted futures {task_id: future}
        try:
            while not shutdown_event.is_set():
                try:
                    
                    flush_all_logs()
                    # Clean up completed futures first
                    completed_task_ids = {task_id for task_id, fut in futures.items() if fut.done()}
                    for task_id in completed_task_ids:
                        del futures[task_id]

                    # Only fetch new tasks if we have available workers
                    with active_workers_lock:
                        available_workers = total_workers - active_workers

                    # Choose task retrieval method based on configuration
                    if config.use_skip_locked:
                        # Try to get tasks one by one using SKIP LOCKED to prevent race conditions
                        while available_workers > 0:
                            with active_workers_lock:
                                if active_workers >= total_workers:
                                    break

                            # Get next available task atomically (already locked)
                            task = database.get_next_available_task()
                            if not task:
                                # No more tasks available
                                logging.debug("No more tasks available from database")
                                break

                            # Ensure task has all required fields for backward compatibility
                            if 'user_id' not in task:
                                task['user_id'] = None
                            if 'bulk_job_type' not in task:
                                task['bulk_job_type'] = None

                            task_id = task['id']
                            logging.debug(f"Processing task {task_id}")
                            # Check if task is already running (shouldn't happen with SKIP LOCKED, but safety check)
                            if task_id in futures:
                                logging.warning(f"Task {task_id} already in futures despite SKIP LOCKED - this shouldn't happen")
                                continue

                            with active_workers_lock:
                                if active_workers < total_workers:
                                    active_workers += 1
                                    available_workers -= 1
                                    logging.info(f"Task {task_id} started. Active workers: {active_workers}/{total_workers}") # Renamed Job -> Task
                                    # Submit the task to the executor (task is already locked and status set to 'In Progress')
                                    future = executor.submit(run_task, task) # Renamed run_job -> run_task
                                    futures[task_id] = future  # Track the future by task_id

                                    # Add callback for when the task is done
                                    future.add_done_callback(
                                        lambda fut, tid=task_id: task_done_callback(fut, tid) # Renamed job_id -> tid, call task_done_callback
                                    )
                                else:
                                    logging.info(f"SKIP LOCKED: All workers busy ({active_workers}/{total_workers}), stopping task fetch")
                                    # Max workers reached, break out of the loop
                                    break
                    else:
                        # Traditional method - fetch multiple tasks and try to lock them
                        if available_workers > 0:
                            # Limit task fetching to available workers to reduce race conditions
                            unprocessed_tasks = database.get_unprocessed_tasks(limit=available_workers * config.task_fetch_limit_multiplier)

                            for task in unprocessed_tasks:
                                task_id = task['id']
                                # Check if task is already running or recently completed
                                if task_id in futures:
                                    continue

                                if task['status'] == 'queued':
                                    with active_workers_lock:
                                        if active_workers < total_workers:
                                            active_workers += 1
                                            logging.info(f"Task {task_id} started. Active workers: {active_workers}/{total_workers}")

                                            # Submit the task to the executor
                                            future = executor.submit(run_task, task)
                                            futures[task_id] = future

                                            # Add callback for when the task is done
                                            future.add_done_callback(
                                                lambda fut, tid=task_id: task_done_callback(fut, tid)
                                            )
                                        else:
                                            # Max workers reached, break out of the loop
                                            break

                    # Adjust sleep time based on whether workers are busy
                    with active_workers_lock:
                        sleep_time = config.worker_sleep_time if active_workers == total_workers else config.worker_sleep_time
                    time.sleep(sleep_time)
                    flush_all_logs()
                except Exception as e:
                    logging.error(f"Error in main loop: {e}", exc_info=True) # Added exc_info for better debugging
                    time.sleep(5) # Wait longer after an error

        except KeyboardInterrupt:
            logging.info("KeyboardInterrupt received. Initiating shutdown...")
            shutdown_event.set()
        
        finally:
             logging.info("Main loop exited. Waiting for active tasks to complete...")
             # Wait for all submitted tasks to complete
             # Note: executor shutdown happens automatically when exiting the 'with' block
             # concurrent.futures.wait(futures.values()) # Wait for all futures if needed, but shutdown handles this
             logging.info("All tasks completed or cancelled. Worker shutting down.")
             flush_all_logs()

    # Clean shutdown of logging system
    try:
        queue_listener.stop()
        logging.info("Logging system shutdown complete.")
    except Exception as e:
        print(f"Error during logging shutdown: {e}", file=sys.stderr)


if __name__ == "__main__":
    main()
