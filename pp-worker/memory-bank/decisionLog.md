# Decision Log

## 12.2.2025 - Output Files Management System

**Context:** Need to implement a flexible output files management system that supports both development and demo outputs, with frontend integration capabilities.

**Decision:** Implement output files handling through a structured JSON schema in module definitions with support for variable substitution.

**Rationale:** 
- Enables declarative output file definitions per module
- Provides flexibility through variable substitution ($DSNAME$, $OUTPUTFOLDER$, etc.)
- Supports different output types with frontend integration (KML, CSV, MTIX)
- Allows for development vs. demo output differentiation

**Implementation:**
1. Schema Definition
```json
{
  "output_files": [
    {
      "path": "string",      // Target directory with variable support
      "filename": "string",  // Output filename with variable support
      "type": "string",     // File type for frontend handling
      "visible": "boolean", // Controls frontend visibility
      "required": "boolean" // Affects task status
    }
  ]
}
```

2. Environmental Controls
- `$DEV_OUTPUTS$` - Enable development outputs
- `$DEMO_OUTPUTS$` - Enable demo outputs
- `$OUTPUTFOLDER$` - Main output directory
- `$DEVOUTPUTFOLDER$` - Development output directory
- `$DSNAME$` - Dataset name substitution

3. Integration Points
- Storage System: Leverage existing StorageInterface for file operations
- Status Tracking: Task status affected by required files
- Frontend: File type-based icon display
- Variable Substitution: Runtime path/filename generation

## Previous Decisions...
[Previous content preserved]