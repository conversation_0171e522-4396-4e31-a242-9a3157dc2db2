# Post-Processing Cloud Worker Service

## Project Overview
This is a worker service component of a post-processing cloud system, specifically designed for GNSS (Global Navigation Satellite System) data processing. It handles the execution of queued processing jobs in parallel using a process pool architecture, with integration to a native C/C++ processing core.

## Core Objectives
- Process queued GNSS processing jobs efficiently and reliably
- Manage parallel job execution with resource constraints
- Provide graceful shutdown capabilities
- Handle errors and job status updates
- Monitor and log processing activities
- Fetch and manage precision files (orbit and clock files) automatically

## Technical Context
- Language: Python with C/C++ core integration
- Architecture: Process Pool based parallel processing
- Database: Supabase
- Storage: Supabase Storage
- Configuration: Environment variables via config.py
- Max Parallel Jobs: Configurable via MAX_WORKER_CORES

## System Components

1. Job Processing System
   - JobProcessor class for handling individual jobs
   - Process pool for parallel execution
   - Job status management and locking
   - C/C++ core integration for processing
   - Result management and storage

2. Storage System
   - Abstract StorageInterface with multiple implementations
   - SupabaseStorage for cloud storage
   - LocalMountedStorage for mounted filesystems
   - CopyLocalStorage for local file operations
   - File compression/decompression handling

3. Data Acquisition System
   - FTP-based precision file fetcher
   - HTTP-based precision file fetcher
   - Automatic file cleanup for old data
   - Specific file pattern matching (SP3, CLK files)

4. Database Integration
   - Job queue management
   - Status updates with locking
   - Batch processing support
   - Result tracking
   - File path management

5. Configuration Management
   - Environment-based configuration
   - Development/Production environment handling
   - Storage bucket configuration
   - Worker thread configuration

## Database Structure

### Core Tables

1. **jobs**
   - Primary key: id (serial)
   - Foreign keys: 
     - user_id -> auth.users(id)
     - global_job_template_id -> global_job_templates(id)
     - dataset_id -> datasets(id)
     - batch_id -> batches(id)
   - Key fields: name, description, status, job_json, workervars, vars, result
   - Timestamps: created_at, updated_at

2. **job_results**
   - Primary key: id (serial)
   - Foreign key: job_id -> jobs(id)
   - Fields: file_name, file_path, file_size, content_type
   - Timestamp: created_at

3. **datasets**
   - Primary key: id (serial)
   - Foreign keys:
     - user_id -> auth.users(id)
     - category_id -> categories(id)
   - Fields: name, description, variable_overrides

4. **categories**
   - Primary key: id (serial)
   - Foreign keys:
     - user_id -> auth.users(id)
     - parent_category_id -> categories(id)
   - Fields: name, description, variable_overrides

5. **files**
   - Primary key: id (serial)
   - Foreign keys:
     - user_id -> auth.users(id)
     - dataset_id -> datasets(id)
   - Fields: bucket_name, file_path, file_name, file_size, content_type
   - Timestamp: created_at

6. **global_job_templates**
   - Primary key: id (serial)
   - Foreign key: user_id -> auth.users(id)
   - Fields: name, description, template_data, vars, commented_json, commented_vars

7. **gui_components**
   - Primary key: id (text)
   - Fields: description, parameters, component_name
   - Timestamps: created_at, updated_at

8. **batches**
   - Primary key: id (serial)
   - Foreign key: user_id -> auth.users(id)
   - Fields: name, description, status
   - Timestamps: created_at, updated_at

### Junction Tables

1. **dataset_files**
   - Composite key: (dataset_id, file_id)
   - Foreign keys to datasets and files
   - Field: file_type

2. **category_files**
   - Composite key: (category_id, file_id)
   - Foreign keys to categories and files
   - Field: file_type

3. **category_job_templates**
   - Composite key: (category_id, global_job_template_id)
   - Foreign keys to categories and global_job_templates

4. **job_template_files**
   - Composite key: (job_template_id, file_id)
   - Foreign keys to global_job_templates and files
   - Field: file_type

### Storage Buckets

1. **cloud**: Private bucket for user files
   - Each user has access to their own folder
   - Automatic file tracking through triggers

2. **results**: Private bucket for job results
   - User-specific folders
   - Access controlled through RLS policies

### Key Functions

1. **File Management**
   - `handle_storage_change()`: Tracks file changes in storage buckets
   - `verify_file_ownership()`: Ensures file ownership matches object ownership

2. **Dataset Operations**
   - `create_dataset_with_files()`: Creates datasets with file associations
   - `update_dataset_with_files()`: Updates dataset information and file associations
   - `sync_dataset_files()`: Synchronizes dataset file associations

3. **Batch Processing**
   - `update_batch_status_with_lock()`: Updates batch status with concurrency control
   - `set_job_status_with_check_and_lock()`: Updates job status with locking

4. **Template Management**
   - `sync_job_template_files()`: Manages file associations for job templates
   - `update_template_data()`: Handles commented JSON processing
   - `remove_json_comments()`: Processes commented JSON configurations

### Security

1. **Row Level Security (RLS)**
   - Enabled on all tables
   - User-specific access policies
   - Service role exceptions for administrative tasks

2. **Storage Security**
   - Bucket-specific access policies
   - User folder isolation
   - Trigger-based file tracking

3. **Function Security**
   - SECURITY DEFINER functions for protected operations
   - Permission grants to authenticated users
   - Service role privileges for system operations

## Key Integration Points
1. Supabase
   - Storage buckets for input/output files
   - Database for job management
   - Real-time updates (capability available)

2. Core Processing
   - C/C++ library integration
   - Thread-level parallelism configuration
   - Error handling and logging

3. External Data Sources
   - AIUB FTP server (ftp.aiub.unibe.ch)
   - CODE MGEX HTTP server
   - Precision file management

## Memory Bank Structure
### Core Files
- **productContext.md** (this file): Project overview, goals, and technical context
- **activeContext.md**: Current session state and immediate goals
- **progress.md**: Work completed and upcoming tasks
- **decisionLog.md**: Key architectural and technical decisions
- **systemPatterns.md**: Identified patterns and best practices

Additional files may be created as needed to document specific aspects of the system.

## Environmental Requirements
- Python 3.x
- Supabase account and configuration
- Access to AIUB FTP/HTTP servers
- C/C++ compilation environment
- Sufficient storage for GNSS data files