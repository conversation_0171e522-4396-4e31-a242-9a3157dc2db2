# System Patterns

## Database Patterns

### 1. User-Centric Security Pattern
**Implementation:** Row Level Security (RLS)
**Key Characteristics:**
- Policy-based access control
- Automatic user context filtering
- Consistent security across tables
- Service role exceptions

### 2. File Association Pattern
**Implementation:** Junction tables with types
**Key Characteristics:**
- Centralized file metadata
- Type-specific associations
- Cascading deletions
- Ownership verification

### 3. Commented Configuration Pattern
**Implementation:** JSON processing functions
**Key Characteristics:**
- Documentation preservation
- Clean data storage
- Comment stripping
- Whitespace preservation

### 4. Status Tracking Pattern
**Implementation:** Atomic updates with locking
**Key Characteristics:**
- Optimistic locking
- Progress counters
- Status transitions
- Concurrent access handling

### 5. Storage Event Pattern
**Implementation:** Bucket triggers
**Key Characteristics:**
- Automatic file tracking
- User folder isolation
- Metadata extraction
- Reference maintenance

## Architectural Patterns

### 1. Worker Pool Pattern
**Implementation:** ProcessPoolExecutor
**Key Characteristics:**
- Fixed-size worker pool
- Parallel job processing
- Resource-aware job distribution
- Worker lifecycle management

### 2. Job Queue Pattern
**Implementation:** Database-backed queue
**Key Characteristics:**
- Persistent job storage
- Status tracking
- Job metadata management
- Queue state consistency

### 3. Producer-Consumer Pattern
**Implementation:** Main loop and worker processes
**Key Characteristics:**
- Asynchronous job processing
- Decoupled job submission and execution
- Buffer through database queue
- Coordinated shutdown

### 4. Strategy Pattern
**Implementation:** Storage interfaces
**Key Characteristics:**
- Abstract StorageInterface base class
- Concrete implementations (Supabase, Local, Mounted)
- Runtime storage backend selection
- Consistent interface across implementations

### 5. Data Acquisition Pattern
**Implementation:** FTP and HTTP fetchers
**Key Characteristics:**
- Multiple source support (FTP/HTTP)
- Automatic retry mechanism
- File age management
- Incremental updates

## Design Patterns

### 1. Resource Management
```python
with ProcessPoolExecutor(max_workers=total_workers) as executor:
    # Resource automatically managed
```
- Deterministic resource cleanup
- Automatic process management
- Error-resistant resource handling

### 2. Observer Pattern
```python
future.add_done_callback(
    lambda fut, job_id=job['id']: job_done_callback(fut, job_id)
)
```
- Asynchronous completion notification
- Decoupled status updates
- Event-driven architecture

### 3. Thread-Safe Counter
```python
with active_workers_lock:
    active_workers += 1
```
- Mutex-based synchronization
- Race condition prevention
- Thread-safe state management

### 4. Factory Pattern
```python
def get_storage_class(storage_type):
    if storage_type == 'supabase_file':
        return storage.SuperbaseStorage
    elif storage_type == 'local_storage':
        return storage.LocalStorage
```
- Dynamic object creation
- Implementation abstraction
- Configuration-driven instantiation

### 5. Template Method Pattern
```python
class StorageInterface(ABC):
    @abstractmethod
    def get_files(self):
        pass
```
- Abstract base class definition
- Common interface specification
- Specialized implementations

## Data Management Patterns

### 1. Output Files Pattern
**Implementation:** Module-level output file declarations
**Key Characteristics:**
- Declarative output configuration
- Variable substitution system
- Environment-based output control
- Frontend integration hooks
- Task status integration

**Usage Example:**
```json
{
  "output_files": [
    {
      "path": "$OUTPUTFOLDER$",
      "filename": "result_$DSNAME$.kml",
      "type": "kml",
      "visible": true,
      "required": true
    }
  ]
}
```

### 2. File Processing Pipeline
- Download management
- Compression handling
- Format validation
- Result storage

### 3. Batch Processing
- Grouped job execution
- Status propagation
- Atomic operations
- Result aggregation

### 4. Storage Abstraction
- Multiple backend support
- Transparent file operations
- Location independence
- Consistent interface

## Error Handling Patterns

### 1. Graceful Degradation
- Exception catching in main loop
- Retry delay on errors
- Continued operation despite failures

### 2. Job Isolation
- Individual process per job
- Error containment
- Process boundary protection

### 3. Structured Logging
- Consistent log format
- Level-based filtering
- Operation traceability

## Integration Patterns

### 1. Database Integration
- Connection pooling
- Transaction management
- Status synchronization
- Result tracking

### 2. Storage Integration
- Multi-backend support
- Transparent file operations
- Atomic file transfers
- Result persistence

### 3. External Service Integration
- Retry mechanisms
- Timeout handling
- Error recovery
- Resource cleanup

## Best Practices

### 1. Configuration Management
- Environment variables
- Development/Production modes
- Configurable constraints
- Dynamic settings

### 2. Process Isolation
- Separate processes for jobs
- Clean process boundaries
- Independent error handling
- Resource isolation

### 3. Resource Monitoring
- Active worker tracking
- Resource limit enforcement
- Operational visibility
- Performance metrics

## Anti-Patterns to Avoid

### 1. Resource Leakage
- Always use context managers
- Implement proper cleanup
- Monitor resource usage
- Handle edge cases

### 2. Global State
- Minimize global variables
- Use proper synchronization
- Maintain thread safety
- Isolate state changes

### 3. Blocking Operations
- Use appropriate timeouts
- Implement cancellation
- Consider async operations
- Handle long-running tasks