SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

CREATE EXTENSION IF NOT EXISTS "pg_net" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgsodium" WITH SCHEMA "pgsodium";



CREATE EXTENSION IF NOT EXISTS "pg_graphql" WITH SCHEMA "graphql";

CREATE EXTENSION IF NOT EXISTS "pg_stat_statements" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgcrypto" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "pgjwt" WITH SCHEMA "extensions";

CREATE EXTENSION IF NOT EXISTS "supabase_vault" WITH SCHEMA "vault";

CREATE EXTENSION IF NOT EXISTS "uuid-ossp" WITH SCHEMA "extensions";




CREATE OR REPLACE FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_dataset_id integer;
    v_file_path text;
    v_file_id integer;
    v_files_count integer := 0;
    v_associated_files_count integer := 0;
BEGIN
    -- Insert the dataset
    INSERT INTO public.datasets (user_id, name, description, category_id, variable_overrides)
    VALUES (p_user_id, p_name, p_description, p_category_id, p_variable_overrides)
    RETURNING id INTO v_dataset_id;

    -- Check if the dataset was actually inserted
    IF v_dataset_id IS NULL THEN
        RAISE EXCEPTION 'Failed to insert dataset';
    END IF;

    RAISE NOTICE 'Dataset created successfully with ID: %', v_dataset_id;

    -- Count the number of files provided
    v_files_count := array_length(p_file_paths, 1);

    -- Associate files with the dataset
    FOREACH v_file_path IN ARRAY p_file_paths LOOP
        -- Get the file ID from the file table, ensuring it belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id)
            VALUES (v_dataset_id, v_file_id);
            v_associated_files_count := v_associated_files_count + 1;
            RAISE NOTICE 'File associated with dataset. Dataset ID: %, File ID: %', v_dataset_id, v_file_id;
        ELSE
            RAISE WARNING 'File not found or user does not have permission: %', v_file_path;
        END IF;
    END LOOP;

    RAISE NOTICE 'Associated % out of % files with the dataset', v_associated_files_count, v_files_count;

    RETURN json_build_object(
        'dataset_id', v_dataset_id,
        'associated_files_count', v_associated_files_count,
        'total_files_count', v_files_count
    );
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise
        RAISE NOTICE 'Error in create_dataset_with_files: %', SQLERRM;
        RAISE;
END;
$$;


CREATE OR REPLACE FUNCTION "public"."create_dataset_with_files"("p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_dataset_id integer;
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_files_count integer := 0;
    v_associated_files_count integer := 0;
BEGIN
    -- Insert the dataset
    INSERT INTO public.datasets (user_id, name, description, category_id, variable_overrides)
    VALUES (p_user_id, p_name, p_description, p_category_id, p_variable_overrides)
    RETURNING id INTO v_dataset_id;

    -- Count the number of files provided
    v_files_count := array_length(p_file_paths, 1);

    -- Associate files with the dataset
    FOR i IN 1..v_files_count LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Get the file ID from the file table, ensuring it belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id, file_type)
            VALUES (v_dataset_id, v_file_id, v_file_type);
            v_associated_files_count := v_associated_files_count + 1;
        END IF;
    END LOOP;

    RETURN json_build_object(
        'dataset_id', v_dataset_id,
        'associated_files_count', v_associated_files_count,
        'total_files_count', v_files_count
    );
END;
$$;


CREATE OR REPLACE FUNCTION "public"."handle_storage_change"() RETURNS "trigger"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    file_owner_id uuid;
    file_name text;
    folder_name text;
BEGIN
    -- Extract the first folder name from the file path
    folder_name := split_part(NEW.name, '/', 1);
    
    -- Check if the folder name is a valid UUID and exists in the auth.users table
    IF EXISTS (
        SELECT 1 FROM auth.users WHERE id::text = folder_name
    ) THEN
        file_owner_id := folder_name::uuid;
    ELSE
        -- If not a valid user UUID, set to NULL or handle as needed
        file_owner_id := NULL;
    END IF;

    -- Extract the file name from the path
    file_name := split_part(NEW.name, '/', -1);

    IF (TG_OP = 'INSERT') THEN
        INSERT INTO public.FILES (user_id, bucket_name, file_path, file_name, file_size, content_type)
        VALUES (
            file_owner_id,
            TG_ARGV[0]::text,
            NEW.name,
            file_name,
            COALESCE((NEW.metadata->>'size')::bigint, 0),
            COALESCE(NEW.metadata->>'mimetype', 'application/octet-stream')
        );
    ELSIF (TG_OP = 'DELETE') THEN
        DELETE FROM public.FILES WHERE bucket_name = TG_ARGV[0]::text AND file_path = OLD.name;
    END IF;

    RETURN NEW;
END;
$$;





CREATE OR REPLACE FUNCTION "public"."set_job_status_with_check_and_lock"("jobid" integer, "oldstatus" "text", "newstatus" "text", "table_name" "text" DEFAULT 'jobs'::"text") RETURNS "text"
    LANGUAGE "plpgsql"
    AS $_$
DECLARE
    affected_rows TEXT;
    query TEXT;
BEGIN
    -- Dynamically construct the SELECT query
    query := format('SELECT status FROM public.%I WHERE id = $1 FOR NO KEY UPDATE NOWAIT', table_name);

    -- Execute the dynamic query
    EXECUTE query INTO affected_rows USING jobid;

    IF affected_rows != oldstatus THEN
        RETURN 'Status mismatch';
    END IF;

    -- Dynamically construct the UPDATE query
    query := format('UPDATE public.%I SET status = $1 WHERE id = $2', table_name);

    -- Execute the dynamic UPDATE query
    EXECUTE query USING newstatus, jobid;

    RETURN 'Success';

EXCEPTION
    WHEN OTHERS THEN
        -- Return the exception message
        RETURN SQLERRM;
END;
$_$;


CREATE OR REPLACE FUNCTION "public"."sync_category_files"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    file_id_value INTEGER;
BEGIN
    SET search_path TO public;
    -- Verify ownership for each file_id in the variable_overrides
    FOR file_id_value IN 
        SELECT (file_id::text)::integer
        FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
             jsonb_array_elements_text(elem->'file_ids') AS file_id
    LOOP
        PERFORM verify_file_ownership(file_id_value, 'category', NEW.id);
    END LOOP;

    DELETE FROM public.category_files WHERE category_id = NEW.id;
  
    INSERT INTO public.category_files (category_id, file_id, file_type)
    SELECT 
        NEW.id,
        (file_id::text)::integer,
        jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
    FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
         jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
    RETURN NEW;
END;
$$;


CREATE OR REPLACE FUNCTION "public"."sync_dataset_files"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    file_id_value INTEGER;
BEGIN
  SET search_path TO public;
    -- Verify ownership for each file_id in the variable_overrides
    FOR file_id_value IN 
        SELECT (file_id::text)::integer
        FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
             jsonb_array_elements_text(elem->'file_ids') AS file_id
    LOOP
        PERFORM verify_file_ownership(file_id_value, 'dataset', NEW.id);
    END LOOP;

    -- Delete old entries
    DELETE FROM dataset_files WHERE dataset_id = NEW.id;
  
    -- Insert new entries
    INSERT INTO dataset_files (dataset_id, file_id, file_type)
    SELECT 
        NEW.id,
        (file_id::text)::integer,
        jsonb_array_elements(NEW.variable_overrides->'workervars')->>'name'
    FROM jsonb_array_elements(NEW.variable_overrides->'workervars') AS elem,
         jsonb_array_elements_text(elem->'file_ids') AS file_id;
  
    RETURN NEW;
END;
$$;


CREATE OR REPLACE FUNCTION "public"."sync_job_template_files"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    file_id INTEGER;
BEGIN
    SET search_path TO public;

    IF (TG_OP = 'INSERT' OR TG_OP = 'UPDATE') THEN
        -- Verify ownership for each file_id in the template_data
        FOR file_id IN 
            SELECT (jsonb_array_elements_text(elem->'file_ids'))::integer
            FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem
        LOOP
            PERFORM verify_file_ownership(file_id, 'job_template', NEW.id);
        END LOOP;

        -- Delete existing entries and insert new ones
        DELETE FROM public.job_template_files WHERE job_template_id = NEW.id;
        
        INSERT INTO public.job_template_files (job_template_id, file_id, file_type)
        SELECT
            NEW.id,
            (jsonb_array_elements_text(elem->'file_ids'))::integer,
            elem->>'name'
        FROM jsonb_array_elements(NEW.template_data->'workervars') AS elem;

        RETURN NEW;
    END IF;
    RETURN NULL;
END;
$$;


CREATE OR REPLACE FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_ids" integer[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
  v_file_id integer;
BEGIN
  -- Update the dataset
  UPDATE public.datasets
  SET name = p_name,
      description = p_description,
      category_id = p_category_id,
      variable_overrides = p_variable_overrides
  WHERE id = p_dataset_id AND user_id = p_user_id;

  -- Remove existing file associations
  DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;

  -- Add new file associations
  FOREACH v_file_id IN ARRAY p_file_ids LOOP
    INSERT INTO public.dataset_files (dataset_id, file_id)
    VALUES (p_dataset_id, v_file_id);
  END LOOP;

  RETURN json_build_object('dataset_id', p_dataset_id);
END;
$$;


CREATE OR REPLACE FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_rows_affected integer;
BEGIN
    -- Update the dataset
    UPDATE public.datasets
    SET name = p_name,
        description = p_description,
        category_id = p_category_id,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Check if the dataset was actually updated
    IF v_rows_affected = 0 THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    RAISE NOTICE 'Dataset updated successfully. Rows affected: %', v_rows_affected;

    -- Remove existing file associations
    DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;
    
    RAISE NOTICE 'Existing file associations removed for dataset ID: %', p_dataset_id;

    -- Add new file associations
    FOREACH v_file_path IN ARRAY p_file_paths LOOP
        -- Get the file ID from the file table
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id)
            VALUES (p_dataset_id, v_file_id);
            RAISE NOTICE 'File associated with dataset. Dataset ID: %, File ID: %', p_dataset_id, v_file_id;
        ELSE
            RAISE WARNING 'File not found or user does not have permission: %', v_file_path;
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
EXCEPTION
    WHEN OTHERS THEN
        -- Log the error and re-raise
        RAISE NOTICE 'Error in update_dataset_with_files: %', SQLERRM;
        RAISE;
END;
$$;


CREATE OR REPLACE FUNCTION "public"."update_dataset_with_files"("p_dataset_id" integer, "p_user_id" "uuid", "p_name" "text", "p_description" "text", "p_category_id" integer, "p_variable_overrides" "jsonb", "p_file_paths" "text"[], "p_file_types" "text"[]) RETURNS "json"
    LANGUAGE "plpgsql" SECURITY DEFINER
    AS $$
DECLARE
    v_file_path text;
    v_file_id integer;
    v_file_type text;
    v_rows_affected integer;
    v_dataset_exists boolean;
BEGIN
    -- Check if the dataset exists and belongs to the user
    SELECT EXISTS (
        SELECT 1 
        FROM public.datasets 
        WHERE id = p_dataset_id AND user_id = p_user_id
    ) INTO v_dataset_exists;

    IF NOT v_dataset_exists THEN
        RAISE EXCEPTION 'Dataset not found or user does not have permission to update it';
    END IF;

    -- Update the dataset
    UPDATE public.datasets
    SET name = p_name,
        description = p_description,
        category_id = p_category_id,
        variable_overrides = p_variable_overrides
    WHERE id = p_dataset_id AND user_id = p_user_id;

    GET DIAGNOSTICS v_rows_affected = ROW_COUNT;

    -- Remove existing file associations
    DELETE FROM public.dataset_files WHERE dataset_id = p_dataset_id;

    -- Add new file associations
    FOR i IN 1..array_length(p_file_paths, 1) LOOP
        v_file_path := p_file_paths[i];
        v_file_type := p_file_types[i];

        -- Check if the file exists and belongs to the user
        SELECT id INTO v_file_id
        FROM public.files
        WHERE file_path = v_file_path AND user_id = p_user_id;

        IF v_file_id IS NOT NULL THEN
            INSERT INTO public.dataset_files (dataset_id, file_id, file_type)
            VALUES (p_dataset_id, v_file_id, v_file_type);
        END IF;
    END LOOP;

    RETURN json_build_object('dataset_id', p_dataset_id);
END;
$$;


CREATE OR REPLACE FUNCTION "public"."update_template_data"() RETURNS "trigger"
    LANGUAGE "plpgsql"
    AS $$
BEGIN
    IF NEW.commented_json IS NOT NULL THEN
        NEW.template_data := public.remove_json_comments(NEW.commented_json);
    END IF;
    RETURN NEW;
END;
$$;


CREATE OR REPLACE FUNCTION "public"."verify_file_ownership"("p_file_id" integer, "p_object_type" "text", "p_object_id" integer) RETURNS "void"
    LANGUAGE "plpgsql"
    AS $$
DECLARE
    v_file_owner uuid;
    v_object_owner uuid;
BEGIN
    SET search_path TO public;
    -- Get the owner of the file
    SELECT user_id INTO v_file_owner
    FROM files
    WHERE id = p_file_id;

    -- Get the owner of the object (dataset, category, or job_template)
    CASE p_object_type
        WHEN 'dataset' THEN
            SELECT user_id INTO v_object_owner
            FROM datasets
            WHERE id = p_object_id;
        WHEN 'category' THEN
            SELECT user_id INTO v_object_owner
            FROM categories
            WHERE id = p_object_id;
        WHEN 'job_template' THEN
            SELECT user_id INTO v_object_owner
            FROM global_job_templates
            WHERE id = p_object_id;
    END CASE;
    -- Compare the owners
    
    IF v_file_owner != v_object_owner THEN
        RAISE EXCEPTION 'File (ID: %) does not belong to the owner of the % (ID: %)', 
                        p_file_id, p_object_type, p_object_id;
    END IF;
    
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = "heap";


CREATE TABLE IF NOT EXISTS "public"."batches" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "status" character varying(50) NOT NULL,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


CREATE SEQUENCE IF NOT EXISTS "public"."batches_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."batches_id_seq" OWNED BY "public"."batches"."id";

CREATE TABLE IF NOT EXISTS "public"."categories" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "parent_category_id" integer,
    "variable_overrides" "jsonb"
);


CREATE SEQUENCE IF NOT EXISTS "public"."categories_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."categories_id_seq" OWNED BY "public"."categories"."id";

CREATE TABLE IF NOT EXISTS "public"."category_files" (
    "category_id" integer NOT NULL,
    "file_id" integer NOT NULL,
    "file_type" "text"
);


CREATE TABLE IF NOT EXISTS "public"."category_job_templates" (
    "category_id" integer NOT NULL,
    "global_job_template_id" integer NOT NULL
);


CREATE TABLE IF NOT EXISTS "public"."dataset_files" (
    "dataset_id" integer NOT NULL,
    "file_id" integer NOT NULL,
    "file_type" "text"
);


CREATE TABLE IF NOT EXISTS "public"."datasets" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "category_id" integer,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "variable_overrides" "jsonb"
);


CREATE SEQUENCE IF NOT EXISTS "public"."datasets_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."datasets_id_seq" OWNED BY "public"."datasets"."id";

CREATE TABLE IF NOT EXISTS "public"."files" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "dataset_id" integer,
    "bucket_name" character varying(255) NOT NULL,
    "file_path" character varying(255) NOT NULL,
    "file_name" character varying(255) NOT NULL,
    "file_size" integer,
    "content_type" character varying(255),
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL
);


CREATE SEQUENCE IF NOT EXISTS "public"."files_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."files_id_seq" OWNED BY "public"."files"."id";

CREATE TABLE IF NOT EXISTS "public"."global_job_templates" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "template_data" "jsonb",
    "vars" "jsonb",
    "commented_json" "text"
);


CREATE SEQUENCE IF NOT EXISTS "public"."global_job_templates_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."global_job_templates_id_seq" OWNED BY "public"."global_job_templates"."id";

CREATE TABLE IF NOT EXISTS "public"."job_template_files" (
    "job_template_id" integer NOT NULL,
    "file_id" integer NOT NULL,
    "file_type" "text"
);


CREATE TABLE IF NOT EXISTS "public"."jobs" (
    "id" integer NOT NULL,
    "user_id" "uuid" NOT NULL,
    "name" character varying(255) NOT NULL,
    "description" "text",
    "global_job_template_id" integer NOT NULL,
    "dataset_id" integer NOT NULL,
    "job_json" "jsonb",
    "status" character varying(50) NOT NULL,
    "created_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "updated_at" timestamp without time zone DEFAULT CURRENT_TIMESTAMP NOT NULL,
    "batch_id" integer,
    "workervars" "jsonb",
    "vars" "jsonb",
    "result" "jsonb"
);


CREATE SEQUENCE IF NOT EXISTS "public"."jobs_id_seq"
    AS integer
    START WITH 1
    INCREMENT BY 1
    NO MINVALUE
    NO MAXVALUE
    CACHE 1;


ALTER SEQUENCE "public"."jobs_id_seq" OWNED BY "public"."jobs"."id";


ALTER TABLE ONLY "public"."batches" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."batches_id_seq"'::"regclass");

ALTER TABLE ONLY "public"."categories" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."categories_id_seq"'::"regclass");

ALTER TABLE ONLY "public"."datasets" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."datasets_id_seq"'::"regclass");

ALTER TABLE ONLY "public"."files" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."files_id_seq"'::"regclass");

ALTER TABLE ONLY "public"."global_job_templates" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."global_job_templates_id_seq"'::"regclass");

ALTER TABLE ONLY "public"."jobs" ALTER COLUMN "id" SET DEFAULT "nextval"('"public"."jobs_id_seq"'::"regclass");



ALTER TABLE ONLY "public"."batches"
    ADD CONSTRAINT "batches_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."category_files"
    ADD CONSTRAINT "category_files_pkey" PRIMARY KEY ("category_id", "file_id");

ALTER TABLE ONLY "public"."category_job_templates"
    ADD CONSTRAINT "category_job_templates_pkey" PRIMARY KEY ("category_id", "global_job_template_id");

ALTER TABLE ONLY "public"."dataset_files"
    ADD CONSTRAINT "dataset_files_pkey" PRIMARY KEY ("dataset_id", "file_id");

ALTER TABLE ONLY "public"."datasets"
    ADD CONSTRAINT "datasets_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."files"
    ADD CONSTRAINT "files_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."global_job_templates"
    ADD CONSTRAINT "global_job_templates_pkey" PRIMARY KEY ("id");

ALTER TABLE ONLY "public"."job_template_files"
    ADD CONSTRAINT "job_template_files_pkey" PRIMARY KEY ("job_template_id", "file_id");

ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_pkey" PRIMARY KEY ("id");

CREATE OR REPLACE TRIGGER "sync_category_files_trigger" AFTER INSERT OR UPDATE ON "public"."categories" FOR EACH ROW EXECUTE FUNCTION "public"."sync_category_files"();

CREATE OR REPLACE TRIGGER "sync_job_template_files_trigger" AFTER INSERT OR UPDATE ON "public"."global_job_templates" FOR EACH ROW EXECUTE FUNCTION "public"."sync_job_template_files"();

CREATE OR REPLACE TRIGGER "update_template_data_trigger" BEFORE INSERT OR UPDATE ON "public"."global_job_templates" FOR EACH ROW EXECUTE FUNCTION "public"."update_template_data"();


ALTER TABLE ONLY "public"."batches"
    ADD CONSTRAINT "batches_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_parent_category_id_fkey" FOREIGN KEY ("parent_category_id") REFERENCES "public"."categories"("id");

ALTER TABLE ONLY "public"."categories"
    ADD CONSTRAINT "categories_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE ONLY "public"."category_files"
    ADD CONSTRAINT "category_files_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."category_files"
    ADD CONSTRAINT "category_files_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id");

ALTER TABLE ONLY "public"."category_job_templates"
    ADD CONSTRAINT "category_job_templates_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id");

ALTER TABLE ONLY "public"."category_job_templates"
    ADD CONSTRAINT "category_job_templates_global_job_template_id_fkey" FOREIGN KEY ("global_job_template_id") REFERENCES "public"."global_job_templates"("id");

ALTER TABLE ONLY "public"."dataset_files"
    ADD CONSTRAINT "dataset_files_dataset_id_fkey" FOREIGN KEY ("dataset_id") REFERENCES "public"."datasets"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."dataset_files"
    ADD CONSTRAINT "dataset_files_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."datasets"
    ADD CONSTRAINT "datasets_category_id_fkey" FOREIGN KEY ("category_id") REFERENCES "public"."categories"("id");

ALTER TABLE ONLY "public"."datasets"
    ADD CONSTRAINT "datasets_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE ONLY "public"."files"
    ADD CONSTRAINT "files_dataset_id_fkey" FOREIGN KEY ("dataset_id") REFERENCES "public"."datasets"("id");

ALTER TABLE ONLY "public"."files"
    ADD CONSTRAINT "files_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE ONLY "public"."global_job_templates"
    ADD CONSTRAINT "global_job_templates_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");

ALTER TABLE ONLY "public"."job_template_files"
    ADD CONSTRAINT "job_template_files_file_id_fkey" FOREIGN KEY ("file_id") REFERENCES "public"."files"("id");

ALTER TABLE ONLY "public"."job_template_files"
    ADD CONSTRAINT "job_template_files_job_template_id_fkey" FOREIGN KEY ("job_template_id") REFERENCES "public"."global_job_templates"("id") ON DELETE CASCADE;

ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_batch_id_fkey" FOREIGN KEY ("batch_id") REFERENCES "public"."batches"("id");

ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_dataset_id_fkey" FOREIGN KEY ("dataset_id") REFERENCES "public"."datasets"("id");

ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_global_job_template_id_fkey" FOREIGN KEY ("global_job_template_id") REFERENCES "public"."global_job_templates"("id");

ALTER TABLE ONLY "public"."jobs"
    ADD CONSTRAINT "jobs_user_id_fkey" FOREIGN KEY ("user_id") REFERENCES "auth"."users"("id");



ALTER TABLE "public"."batches" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "batches_policy" ON "public"."batches" USING (("auth"."uid"() = "user_id"));

ALTER TABLE "public"."categories" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "categories_policy" ON "public"."categories" USING (("auth"."uid"() = "user_id"));

ALTER TABLE "public"."category_files" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "category_files_access_policy" ON "public"."category_files" USING ((EXISTS ( SELECT 1
   FROM "public"."categories"
  WHERE (("categories"."id" = "category_files"."category_id") AND ("categories"."user_id" = "auth"."uid"())))));

ALTER TABLE "public"."category_job_templates" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "category_job_templates_policy" ON "public"."category_job_templates" USING ((EXISTS ( SELECT 1
   FROM "public"."categories"
  WHERE (("categories"."id" = "category_job_templates"."category_id") AND ("categories"."user_id" = "auth"."uid"())))));

ALTER TABLE "public"."dataset_files" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "dataset_files_access_policy" ON "public"."dataset_files" USING ((EXISTS ( SELECT 1
   FROM ("public"."datasets" "d"
     JOIN "public"."files" "f" ON (("f"."id" = "dataset_files"."file_id")))
  WHERE (("d"."id" = "dataset_files"."dataset_id") AND ("d"."user_id" = "auth"."uid"()) AND ("f"."user_id" = "auth"."uid"())))));

ALTER TABLE "public"."datasets" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "datasets" ON "public"."datasets" TO "authenticated" USING (("auth"."uid"() = "user_id"));

ALTER TABLE "public"."files" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "files_policy2" ON "public"."files" USING (("auth"."uid"() = "user_id"));

ALTER TABLE "public"."global_job_templates" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "global_job_templates_policy" ON "public"."global_job_templates" USING (("auth"."uid"() = "user_id"));

ALTER TABLE "public"."job_template_files" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "job_template_files_access_policy" ON "public"."job_template_files" USING ((EXISTS ( SELECT 1
   FROM "public"."global_job_templates"
  WHERE (("global_job_templates"."id" = "job_template_files"."job_template_id") AND ("global_job_templates"."user_id" = "auth"."uid"())))));

ALTER TABLE "public"."jobs" ENABLE ROW LEVEL SECURITY;

CREATE POLICY "jobs_policy" ON "public"."jobs" USING (("auth"."uid"() = "user_id"));


GRANT USAGE ON SCHEMA "public" TO "authenticated";
GRANT USAGE ON SCHEMA "public" TO "service_role";
GRANT USAGE ON SCHEMA "public" TO "postgres";



GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."batches" TO "authenticated";
GRANT SELECT ON TABLE "public"."batches" TO "postgres";

GRANT SELECT,USAGE ON SEQUENCE "public"."batches_id_seq" TO "authenticated";

GRANT ALL ON TABLE "public"."categories" TO "authenticated";
GRANT ALL ON TABLE "public"."categories" TO "service_role";
GRANT SELECT ON TABLE "public"."categories" TO "postgres";

GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."categories_id_seq" TO "service_role";

GRANT SELECT ON TABLE "public"."category_files" TO "postgres";

GRANT ALL ON TABLE "public"."category_job_templates" TO "authenticated";
GRANT ALL ON TABLE "public"."category_job_templates" TO "service_role";
GRANT SELECT ON TABLE "public"."category_job_templates" TO "postgres";

GRANT SELECT ON TABLE "public"."dataset_files" TO PUBLIC;
GRANT SELECT ON TABLE "public"."dataset_files" TO "postgres";

GRANT ALL ON TABLE "public"."datasets" TO "authenticated";
GRANT ALL ON TABLE "public"."datasets" TO "service_role";
GRANT SELECT ON TABLE "public"."datasets" TO "postgres";

GRANT ALL ON SEQUENCE "public"."datasets_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."datasets_id_seq" TO "service_role";

GRANT ALL ON TABLE "public"."files" TO "authenticated";
GRANT ALL ON TABLE "public"."files" TO "service_role";
GRANT SELECT ON TABLE "public"."files" TO "postgres";

GRANT ALL ON SEQUENCE "public"."files_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."files_id_seq" TO "service_role";

GRANT ALL ON TABLE "public"."global_job_templates" TO "authenticated";
GRANT ALL ON TABLE "public"."global_job_templates" TO "service_role";
GRANT SELECT ON TABLE "public"."global_job_templates" TO "postgres";

GRANT ALL ON SEQUENCE "public"."global_job_templates_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."global_job_templates_id_seq" TO "service_role";

GRANT SELECT,INSERT,DELETE,UPDATE ON TABLE "public"."job_template_files" TO PUBLIC;
GRANT SELECT ON TABLE "public"."job_template_files" TO "postgres";

GRANT ALL ON TABLE "public"."jobs" TO "authenticated";
GRANT ALL ON TABLE "public"."jobs" TO "service_role";
GRANT SELECT ON TABLE "public"."jobs" TO "postgres";

GRANT ALL ON SEQUENCE "public"."jobs_id_seq" TO "authenticated";
GRANT ALL ON SEQUENCE "public"."jobs_id_seq" TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON SEQUENCES  TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON FUNCTIONS  TO "service_role";

ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "authenticated";
ALTER DEFAULT PRIVILEGES FOR ROLE "postgres" IN SCHEMA "public" GRANT ALL ON TABLES  TO "service_role";

RESET ALL;
