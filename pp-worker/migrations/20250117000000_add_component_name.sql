-- Add component_name column to gui_components table
ALTER TABLE gui_components
ADD COLUMN component_name TEXT NOT NULL;

-- Add comment explaining the column
COMMENT ON COLUMN gui_components.component_name IS 'The actual React component name to use for rendering (e.g., TextField, IntegerSlider)';

-- Update existing records to match their IDs initially
UPDATE gui_components SET component_name = id;

-- Add a check constraint to ensure component_name is not empty
ALTER TABLE gui_components
ADD CONSTRAINT gui_components_component_name_not_empty CHECK (component_name != '');
