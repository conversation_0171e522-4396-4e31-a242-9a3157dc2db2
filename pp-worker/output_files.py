import os
import re
from typing import List, Dict, Optional

class OutputFile:
    """Represents an output file definition with its properties"""
    def __init__(self, 
                 path: str, 
                 filename: str, 
                 file_type: str,
                 visible: bool = True,
                 required: bool = True):
        self.path = path
        self.filename = filename 
        self.file_type = file_type
        self.visible = visible
        self.required = required

    def to_dict(self) -> Dict:
        """Convert the output file definition to a dictionary"""
        return {
            "path": self.path,
            "filename": self.filename,
            "type": self.file_type,
            "visible": self.visible,
            "required": self.required
        }

    @staticmethod
    def from_dict(data: Dict) -> 'OutputFile':
        """Create an OutputFile instance from a dictionary"""
        return OutputFile(
            path=data["path"],
            filename=data["filename"],
            file_type=data["type"],
            visible=data.get("visible", True),
            required=data.get("required", True)
        )

class OutputFileManager:
    """Manages output files for a job module"""
    
    def __init__(self):
        self.output_files: List[OutputFile] = []
        self.variables = {}

    def add_output_file(self, output_file: OutputFile):
        """Add an output file definition"""
        self.output_files.append(output_file)

    def set_variable(self, name: str, value: str):
        """Set a variable value for placeholder replacement"""
        self.variables[name] = value

    def resolve_variable(self, value: str) -> str:
        """Resolve a variable value from the variables dictionary"""
        if isinstance(value, str) and value.startswith('$') and value.endswith('$'):
            var_name = value[1:-1]  # Remove $ signs
            return str(self.variables.get(var_name, value))
        return str(value)

    def get_resolved_path(self, output_file: OutputFile) -> str:
        """Get the resolved path with variables replaced"""
        path = output_file.path
        filename = output_file.filename

        # Replace variables in path and filename
        for var_name, var_value in self.variables.items():
            placeholder = f"${var_name}$"
            path = path.replace(placeholder, var_value)
            filename = filename.replace(placeholder, var_value)

        return os.path.join(path, filename)

    def get_missing_required_files(self) -> List[str]:
        """Get list of required files that are missing"""
        missing_files = []
        
        for output_file in self.output_files:
            if output_file.required:
                full_path = self.get_resolved_path(output_file)
                if not os.path.exists(full_path):
                    missing_files.append(full_path)
                    
        return missing_files

    def get_visible_files(self) -> List[Dict]:
        """Get list of visible files and their properties"""
        visible_files = []
        
        for output_file in self.output_files:
            if output_file.visible:
                file_info = output_file.to_dict()
                file_info["resolved_path"] = self.get_resolved_path(output_file)
                visible_files.append(file_info)
                
        return visible_files

    def has_all_required_files(self) -> bool:
        """Check if all required files exist"""
        return len(self.get_missing_required_files()) == 0

def validate_filename(filename: str) -> bool:
    """Validate that filename only contains safe characters"""
    # Allow alphanumeric, dash, underscore, dot
    return bool(re.match(r'^[\w\-_.]+$', filename))