# Contributing

We highly appreciate feedback and contributions from the community! If you'd like to contribute to this project, please make sure to review and follow the guidelines below.

## Code of conduct

In the interest of fostering an open and welcoming environment, please review and follow our [code of conduct](./CODE_OF_CONDUCT.md).

## Code and copy reviews

All submissions, including submissions by project members, require review. We
use GitHub pull requests for this purpose. Consult
[GitHub Help](https://help.github.com/articles/about-pull-requests/) for more
information on using pull requests.

## Report an issue

Report all issues through [GitHub Issues](./issues).

## File a feature request

File your feature request through [GitHub Issues](./issues).

## Create a pull request

When making pull requests to the repository, make sure to follow these guidelines for both bug fixes and new features:

- Before creating a pull request, file a GitHub Issue so that maintainers and the community can discuss the problem and potential solutions before you spend time on an implementation.
- In your PR's description, link to any related issues or pull requests to give reviewers the full context of your change.
- For commit messages, follow the [Conventional Commits](https://www.conventionalcommits.org/en/v1.0.0/) format.
  - For example, if you update documentation for a specific extension, your commit message might be: `docs(extension-name) updated installation documentation`.
diff --git a/CODE_OF_CONDUCT.md b/CODE_OF_CONDUCT.md
