<!doctype html>
<html lang="en">
<head>
<meta charset="utf-8">
<meta name="viewport" content="width=device-width, initial-scale=1, minimum-scale=1" />
<meta name="generator" content="pdoc 0.9.1" />
<title>realtime.channel API documentation</title>
<meta name="description" content="" />
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/sanitize.min.css" integrity="sha256-PK9q560IAAa6WVRRh76LtCaI8pjTJ2z11v0miyNNjrs=" crossorigin>
<link rel="preload stylesheet" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/10up-sanitize.css/11.0.1/typography.min.css" integrity="sha256-7l/o7C8jubJiy74VsKTidCy1yBkRtiUGbVkYBylBqUg=" crossorigin>
<link rel="stylesheet preload" as="style" href="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/styles/github.min.css" crossorigin>
<style>:root{--highlight-color:#fe9}.flex{display:flex !important}body{line-height:1.5em}#content{padding:20px}#sidebar{padding:30px;overflow:hidden}#sidebar > *:last-child{margin-bottom:2cm}.http-server-breadcrumbs{font-size:130%;margin:0 0 15px 0}#footer{font-size:.75em;padding:5px 30px;border-top:1px solid #ddd;text-align:right}#footer p{margin:0 0 0 1em;display:inline-block}#footer p:last-child{margin-right:30px}h1,h2,h3,h4,h5{font-weight:300}h1{font-size:2.5em;line-height:1.1em}h2{font-size:1.75em;margin:1em 0 .50em 0}h3{font-size:1.4em;margin:25px 0 10px 0}h4{margin:0;font-size:105%}h1:target,h2:target,h3:target,h4:target,h5:target,h6:target{background:var(--highlight-color);padding:.2em 0}a{color:#058;text-decoration:none;transition:color .3s ease-in-out}a:hover{color:#e82}.title code{font-weight:bold}h2[id^="header-"]{margin-top:2em}.ident{color:#900}pre code{background:#f8f8f8;font-size:.8em;line-height:1.4em}code{background:#f2f2f1;padding:1px 4px;overflow-wrap:break-word}h1 code{background:transparent}pre{background:#f8f8f8;border:0;border-top:1px solid #ccc;border-bottom:1px solid #ccc;margin:1em 0;padding:1ex}#http-server-module-list{display:flex;flex-flow:column}#http-server-module-list div{display:flex}#http-server-module-list dt{min-width:10%}#http-server-module-list p{margin-top:0}.toc ul,#index{list-style-type:none;margin:0;padding:0}#index code{background:transparent}#index h3{border-bottom:1px solid #ddd}#index ul{padding:0}#index h4{margin-top:.6em;font-weight:bold}@media (min-width:200ex){#index .two-column{column-count:2}}@media (min-width:300ex){#index .two-column{column-count:3}}dl{margin-bottom:2em}dl dl:last-child{margin-bottom:4em}dd{margin:0 0 1em 3em}#header-classes + dl > dd{margin-bottom:3em}dd dd{margin-left:2em}dd p{margin:10px 0}.name{background:#eee;font-weight:bold;font-size:.85em;padding:5px 10px;display:inline-block;min-width:40%}.name:hover{background:#e0e0e0}dt:target .name{background:var(--highlight-color)}.name > span:first-child{white-space:nowrap}.name.class > span:nth-child(2){margin-left:.4em}.inherited{color:#999;border-left:5px solid #eee;padding-left:1em}.inheritance em{font-style:normal;font-weight:bold}.desc h2{font-weight:400;font-size:1.25em}.desc h3{font-size:1em}.desc dt code{background:inherit}.source summary,.git-link-div{color:#666;text-align:right;font-weight:400;font-size:.8em;text-transform:uppercase}.source summary > *{white-space:nowrap;cursor:pointer}.git-link{color:inherit;margin-left:1em}.source pre{max-height:500px;overflow:auto;margin:0}.source pre code{font-size:12px;overflow:visible}.hlist{list-style:none}.hlist li{display:inline}.hlist li:after{content:',\2002'}.hlist li:last-child:after{content:none}.hlist .hlist{display:inline;padding-left:1em}img{max-width:100%}td{padding:0 .5em}.admonition{padding:.1em .5em;margin-bottom:1em}.admonition-title{font-weight:bold}.admonition.note,.admonition.info,.admonition.important{background:#aef}.admonition.todo,.admonition.versionadded,.admonition.tip,.admonition.hint{background:#dfd}.admonition.warning,.admonition.versionchanged,.admonition.deprecated{background:#fd4}.admonition.error,.admonition.danger,.admonition.caution{background:lightpink}</style>
<style media="screen and (min-width: 700px)">@media screen and (min-width:700px){#sidebar{width:30%;height:100vh;overflow:auto;position:sticky;top:0}#content{width:70%;max-width:100ch;padding:3em 4em;border-left:1px solid #ddd}pre code{font-size:1em}.item .name{font-size:1em}main{display:flex;flex-direction:row-reverse;justify-content:flex-end}.toc ul ul,#index ul{padding-left:1.5em}.toc > ul > li{margin-top:.5em}}</style>
<style media="print">@media print{#sidebar h1{page-break-before:always}.source{display:none}}@media print{*{background:transparent !important;color:#000 !important;box-shadow:none !important;text-shadow:none !important}a[href]:after{content:" (" attr(href) ")";font-size:90%}a[href][title]:after{content:none}abbr[title]:after{content:" (" attr(title) ")"}.ir a:after,a[href^="javascript:"]:after,a[href^="#"]:after{content:""}pre,blockquote{border:1px solid #999;page-break-inside:avoid}thead{display:table-header-group}tr,img{page-break-inside:avoid}img{max-width:100% !important}@page{margin:0.5cm}p,h2,h3{orphans:3;widows:3}h1,h2,h3,h4,h5,h6{page-break-after:avoid}}</style>
<script defer src="https://cdnjs.cloudflare.com/ajax/libs/highlight.js/10.1.1/highlight.min.js" integrity="sha256-Uv3H6lx7dJmRfRvH8TH6kJD1TSK1aFcwgx+mdg3epi8=" crossorigin></script>
<script>window.addEventListener('DOMContentLoaded', () => hljs.initHighlighting())</script>
</head>
<body>
<main>
<article id="content">
<header>
<h1 class="title">Module <code>realtime.channel</code></h1>
</header>
<section id="section-intro">
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">import asyncio
import json
from typing import List
from collections import namedtuple

&#34;&#34;&#34;
Callback Listener is a tuple with `event` and `callback` 
&#34;&#34;&#34;
CallbackListener = namedtuple(&#34;CallbackListener&#34;, &#34;event callback&#34;)


class Channel:
    &#34;&#34;&#34;
    `Channel` is an abstraction for a topic listener for an existing socket connection.
    Each Channel has its own topic and a list of event-callbacks that responds to messages.
    Should only be instantiated through `connection.Socket().set_channel(topic)`
    Topic-Channel has a 1-many relationship.
    &#34;&#34;&#34;

    def __init__(self, socket, topic: str, params: dict = {}):
        &#34;&#34;&#34;

        :param socket: Socket object
        :param topic: Topic that it subscribes to on the realtime server
        :param params:
        &#34;&#34;&#34;
        self.socket = socket
        self.topic: str = topic
        self.params: dict = params
        self.listeners: List[CallbackListener] = []
        self.joined: bool = False

    def join(self):
        &#34;&#34;&#34;
        Wrapper for async def _join() to expose a non-async interface
        Essentially gets the only event loop and attempt joining a topic
        :return: None
        &#34;&#34;&#34;
        loop = asyncio.get_event_loop()
        loop.run_until_complete(self._join())
        return self

    async def _join(self):
        &#34;&#34;&#34;
        Coroutine that attempts to join Phoenix Realtime server via a certain topic
        :return: Channel.channel
        &#34;&#34;&#34;
        join_req = dict(topic=self.topic, event=&#34;phx_join&#34;, payload={}, ref=None)

        try:
            await self.socket.ws_connection.send(json.dumps(join_req))

        except Exception as e:
            print(str(e))
            return

    def on(self, event: str, callback):
        &#34;&#34;&#34;

        :param event: A specific event will have a specific callback
        :param callback: Callback that takes msg payload as its first argument
        :return: NOne
        &#34;&#34;&#34;

        # TODO: Should I return self so that I can allow chaining?
        cl = CallbackListener(event=event, callback=callback)
        self.listeners.append(cl)

    def off(self, event: str):
        &#34;&#34;&#34;

        :param event: Stop responding to a certain event
        :return: None
        &#34;&#34;&#34;
        self.listeners = [callback for callback in self.listeners if callback.event != event]</code></pre>
</details>
</section>
<section>
</section>
<section>
</section>
<section>
</section>
<section>
<h2 class="section-title" id="header-classes">Classes</h2>
<dl>
<dt id="realtime.channel.CallbackListener"><code class="flex name class">
<span>class <span class="ident">CallbackListener</span></span>
<span>(</span><span>event, callback)</span>
</code></dt>
<dd>
<div class="desc"><p>CallbackListener(event, callback)</p></div>
<h3>Ancestors</h3>
<ul class="hlist">
<li>builtins.tuple</li>
</ul>
<h3>Instance variables</h3>
<dl>
<dt id="realtime.channel.CallbackListener.callback"><code class="name">var <span class="ident">callback</span></code></dt>
<dd>
<div class="desc"><p>Alias for field number 1</p></div>
</dd>
<dt id="realtime.channel.CallbackListener.event"><code class="name">var <span class="ident">event</span></code></dt>
<dd>
<div class="desc"><p>Alias for field number 0</p></div>
</dd>
</dl>
</dd>
<dt id="realtime.channel.Channel"><code class="flex name class">
<span>class <span class="ident">Channel</span></span>
<span>(</span><span>socket, topic: str, params: dict = {})</span>
</code></dt>
<dd>
<div class="desc"><p><code><a title="realtime.channel.Channel" href="#realtime.channel.Channel">Channel</a></code> is an abstraction for a topic listener for an existing socket connection.
Each Channel has its own topic and a list of event-callbacks that responds to messages.
Should only be instantiated through <code>connection.Socket().set_channel(topic)</code>
Topic-Channel has a 1-many relationship.</p>
<p>:param socket: Socket object
:param topic: Topic that it subscribes to on the realtime server
:param params:</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">class Channel:
    &#34;&#34;&#34;
    `Channel` is an abstraction for a topic listener for an existing socket connection.
    Each Channel has its own topic and a list of event-callbacks that responds to messages.
    Should only be instantiated through `connection.Socket().set_channel(topic)`
    Topic-Channel has a 1-many relationship.
    &#34;&#34;&#34;

    def __init__(self, socket, topic: str, params: dict = {}):
        &#34;&#34;&#34;

        :param socket: Socket object
        :param topic: Topic that it subscribes to on the realtime server
        :param params:
        &#34;&#34;&#34;
        self.socket = socket
        self.topic: str = topic
        self.params: dict = params
        self.listeners: List[CallbackListener] = []
        self.joined: bool = False

    def join(self):
        &#34;&#34;&#34;
        Wrapper for async def _join() to expose a non-async interface
        Essentially gets the only event loop and attempt joining a topic
        :return: None
        &#34;&#34;&#34;
        loop = asyncio.get_event_loop()
        loop.run_until_complete(self._join())
        return self

    async def _join(self):
        &#34;&#34;&#34;
        Coroutine that attempts to join Phoenix Realtime server via a certain topic
        :return: Channel.channel
        &#34;&#34;&#34;
        join_req = dict(topic=self.topic, event=&#34;phx_join&#34;, payload={}, ref=None)

        try:
            await self.socket.ws_connection.send(json.dumps(join_req))

        except Exception as e:
            print(str(e))
            return

    def on(self, event: str, callback):
        &#34;&#34;&#34;

        :param event: A specific event will have a specific callback
        :param callback: Callback that takes msg payload as its first argument
        :return: NOne
        &#34;&#34;&#34;

        # TODO: Should I return self so that I can allow chaining?
        cl = CallbackListener(event=event, callback=callback)
        self.listeners.append(cl)

    def off(self, event: str):
        &#34;&#34;&#34;

        :param event: Stop responding to a certain event
        :return: None
        &#34;&#34;&#34;
        self.listeners = [callback for callback in self.listeners if callback.event != event]</code></pre>
</details>
<h3>Methods</h3>
<dl>
<dt id="realtime.channel.Channel.join"><code class="name flex">
<span>def <span class="ident">join</span></span>(<span>self)</span>
</code></dt>
<dd>
<div class="desc"><p>Wrapper for async def _join() to expose a non-async interface
Essentially gets the only event loop and attempt joining a topic
:return: None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def join(self):
    &#34;&#34;&#34;
    Wrapper for async def _join() to expose a non-async interface
    Essentially gets the only event loop and attempt joining a topic
    :return: None
    &#34;&#34;&#34;
    loop = asyncio.get_event_loop()
    loop.run_until_complete(self._join())
    return self</code></pre>
</details>
</dd>
<dt id="realtime.channel.Channel.off"><code class="name flex">
<span>def <span class="ident">off</span></span>(<span>self, event: str)</span>
</code></dt>
<dd>
<div class="desc"><p>:param event: Stop responding to a certain event
:return: None</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def off(self, event: str):
    &#34;&#34;&#34;

    :param event: Stop responding to a certain event
    :return: None
    &#34;&#34;&#34;
    self.listeners = [callback for callback in self.listeners if callback.event != event]</code></pre>
</details>
</dd>
<dt id="realtime.channel.Channel.on"><code class="name flex">
<span>def <span class="ident">on</span></span>(<span>self, event: str, callback)</span>
</code></dt>
<dd>
<div class="desc"><p>:param event: A specific event will have a specific callback
:param callback: Callback that takes msg payload as its first argument
:return: NOne</p></div>
<details class="source">
<summary>
<span>Expand source code</span>
</summary>
<pre><code class="python">def on(self, event: str, callback):
    &#34;&#34;&#34;

    :param event: A specific event will have a specific callback
    :param callback: Callback that takes msg payload as its first argument
    :return: NOne
    &#34;&#34;&#34;

    # TODO: Should I return self so that I can allow chaining?
    cl = CallbackListener(event=event, callback=callback)
    self.listeners.append(cl)</code></pre>
</details>
</dd>
</dl>
</dd>
</dl>
</section>
</article>
<nav id="sidebar">
<h1>Index</h1>
<div class="toc">
<ul></ul>
</div>
<ul id="index">
<li><h3>Super-module</h3>
<ul>
<li><code><a title="realtime" href="index.html">realtime</a></code></li>
</ul>
</li>
<li><h3><a href="#header-classes">Classes</a></h3>
<ul>
<li>
<h4><code><a title="realtime.channel.CallbackListener" href="#realtime.channel.CallbackListener">CallbackListener</a></code></h4>
<ul class="">
<li><code><a title="realtime.channel.CallbackListener.callback" href="#realtime.channel.CallbackListener.callback">callback</a></code></li>
<li><code><a title="realtime.channel.CallbackListener.event" href="#realtime.channel.CallbackListener.event">event</a></code></li>
</ul>
</li>
<li>
<h4><code><a title="realtime.channel.Channel" href="#realtime.channel.Channel">Channel</a></code></h4>
<ul class="">
<li><code><a title="realtime.channel.Channel.join" href="#realtime.channel.Channel.join">join</a></code></li>
<li><code><a title="realtime.channel.Channel.off" href="#realtime.channel.Channel.off">off</a></code></li>
<li><code><a title="realtime.channel.Channel.on" href="#realtime.channel.Channel.on">on</a></code></li>
</ul>
</li>
</ul>
</li>
</ul>
</nav>
</main>
<footer id="footer">
<p>Generated by <a href="https://pdoc3.github.io/pdoc"><cite>pdoc</cite> 0.9.1</a>.</p>
</footer>
</body>
</html>