CREATE OR REPLACE FUNCTION set_job_status_with_check_and_lock(jobid TEXT, oldstatus TEXT, newstatus TEXT)
RETURNS BOOLEAN AS $$
DECLARE
    affected_rows TEXT;
BEGIN
    SELECT status INTO affected_rows FROM jobs WHERE id = UUID(jobid) FOR NO KEY UPDATE NOWAIT;
    IF affected_rows != oldstatus THEN
        RETURN FALSE;
    END IF;
    UPDATE jobs SET status = newstatus WHERE id = UUID(jobid);
    RETURN TRUE;
END;
$$ LANGUAGE plpgsql;