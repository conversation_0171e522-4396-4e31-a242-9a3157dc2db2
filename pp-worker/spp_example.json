{"workervars": [{"name": "R_CLOCKFILES", "data": ["file1", "file2"], "source": "supabase_file"}, {"name": "R_SP3FILES", "data": ["file1", "file2"], "source": "supabase_file"}, {"name": "OUTPUT_FILES"}], "vars": [{"name": "FORWARD", "links": [["dir", [0]]], "data": [true, false]}, {"name": "CLOCKFILES", "links": [], "data": "to be replaced by worker"}, {"name": "SP3FILES", "links": [], "data": "to be replaced by worker"}], "process": [{"name": "GNSS-Preprocessor", "enable": true, "app": "MOD_GNSSRAW", "args": {"clkfiles": "$CLOCKFILES$", "sp3files": "$SP3FILES$", "rnxfiles": ["20231107.23o"], "elev_mask_deg": 10, "rco_factor": 0.0, "solver": 0, "switch_rcc": 1.0, "freqs": {"G": [1], "R": [1], "E": [1]}, "tof_iterations": 1, "polydegree": 9, "out_spp": "~>SPP", "output_files": [{"path": "$DEVOUTPUTFOLDER$", "filename": "spp_results.csv", "type": "csv", "visible": false, "required": false}]}}, {"name": "SPP_into_TIX", "enable": true, "app": "MOD_SDC2TIX", "args": {"in": "<~SPP", "out": "spp.TIX", "output_files": [{"path": "$OUTPUTFOLDER$", "filename": "spp_$DSNAME$.tix", "type": "tix", "visible": true, "required": true}]}}, {"name": "Starling_Simple_CUPT_Test", "enable": true, "app": "MOD_STARLING", "args": {"sensors": [{"name": "cupt", "type": "POSLEV", "input": "<~SPP", "states": [{"name": "LEV", "init": [1.0, 2.0, 3.0], "std0": [10.0], "estax": -1, "processes": {"type": "RW", "sqrt_q": 1, "inverseofBeta": 100, "sigma0": 0.1}}], "settings": {"sensor_param1": "foo", "sensor_param2": "bar", "sensor_param3": 3.1415}}], "predictor": {"type": "POS", "sqrt_q": 100, "std0": 1000000.0}, "outputs": [{"name": "MyEKFLog", "rts": false, "fields": ["c|TSTRING", "d|LLH", "f32|cupt:LEV"], "sinks": ["~>CUPT_RESULTS_EKF"]}, {"name": "MyRTSLog", "rts": true, "fields": ["c|TSTRING", "i64|T", "d|LLH", "i8|cupt:LEV:ND*", "f32|cupt:LEV"], "sinks": ["~>CUPT_RESULTS_RTS"]}], "settings": {"forward": "$FORWARD$", "passes": 1}}, "settings": {}}, {"name": "RTS_into_TIX", "enable": true, "app": "MOD_SDC2TIX", "args": {"in": "<~CUPT_RESULTS_RTS", "out": "rts.TIX", "output_files": [{"path": "$OUTPUTFOLDER$", "filename": "rts_$DSNAME$.tix", "type": "tix", "visible": true, "required": true}]}}], "settings": {"VERBOSE": 1, "dac_param1": [0, 2, 4], "dac_param2": ["hello", "DAC", "world"]}}