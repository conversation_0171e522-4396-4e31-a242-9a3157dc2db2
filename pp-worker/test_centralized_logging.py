#!/usr/bin/env python3
"""
Test script to verify the enhanced dual logging system.
This script simulates the worker environment to ensure both console and file logging work correctly.
"""

import logging
import sys
import time
import os
import multiprocessing as mp
from logging.handlers import <PERSON><PERSON><PERSON><PERSON><PERSON>, QueueListener
from concurrent.futures import ProcessPoolExecutor

class DualOutputHandler(logging.Handler):
    """
    Custom logging handler that sends logs to both:
    1. Central queue for console output (process-safe)
    2. Individual task log file (when task context is available)
    """
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue
        self.queue_handler = QueueHandler(log_queue)
        self.current_task_id = None
        self.current_file_handler = None

    def set_task_context(self, task_id):
        """Set the current task context for file logging"""
        if self.current_task_id == task_id:
            return  # Already set to this task

        # Close previous file handler if exists
        if self.current_file_handler:
            self.current_file_handler.close()
            self.current_file_handler = None

        self.current_task_id = task_id

        if task_id:
            # Create file handler for this task
            try:
                log_dir = f"./test_tasks/{task_id}/devoutput"
                os.makedirs(log_dir, exist_ok=True)
                log_file_path = f"{log_dir}/worker.log"

                self.current_file_handler = logging.FileHandler(log_file_path, mode='a')
                self.current_file_handler.setFormatter(
                    logging.Formatter('[%(asctime)s] %(levelname)s [%(name)s]: %(message)s')
                )
            except Exception as e:
                # If file handler creation fails, log to console only
                print(f"Warning: Could not create log file for task {task_id}: {e}", file=sys.stderr)
                self.current_file_handler = None

    def emit(self, record):
        """Emit log record to both console queue and task file"""
        # Always send to central queue for console output
        self.queue_handler.emit(record)

        # Also write to task file if available
        if self.current_file_handler:
            try:
                self.current_file_handler.emit(record)
            except Exception:
                # If file writing fails, continue with console logging only
                pass

    def close(self):
        """Clean up handlers"""
        if self.current_file_handler:
            self.current_file_handler.close()
            self.current_file_handler = None
        super().close()

def setup_centralized_logging():
    """Set up the centralized logging system like in main.py"""
    # Initialize centralized, process-safe logging
    log_queue = mp.Manager().Queue(-1)  # Unbounded queue for log records
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    # Console handler in main process - the only place that writes to stdout
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter('[%(asctime)s] %(levelname)s [%(name)s]: %(message)s'))

    # Start the queue listener in main process
    queue_listener = QueueListener(log_queue, console_handler)
    queue_listener.start()

    # Replace all handlers with QueueHandler
    root_logger.handlers = [QueueHandler(log_queue)]

    return log_queue, queue_listener

def worker_init(log_q):
    """Initialize worker process with dual logging"""
    global DUAL_HANDLER
    # Configure dual logging for this worker process
    DUAL_HANDLER = DualOutputHandler(log_q)
    worker_root = logging.getLogger()
    worker_root.handlers = [DUAL_HANDLER]
    worker_root.setLevel(logging.INFO)

def worker_task(task_id, log_q):
    """Simulate a worker task that logs messages with dual output"""
    global DUAL_HANDLER

    # Set task context for file logging
    if 'DUAL_HANDLER' in globals() and DUAL_HANDLER:
        DUAL_HANDLER.set_task_context(task_id)

    logger = logging.getLogger(f"Worker-{mp.current_process().pid}")

    logger.info(f"Worker {mp.current_process().pid} starting task {task_id}")

    # Simulate some work with logging
    for i in range(3):
        logger.info(f"Task {task_id} - Step {i+1}/3")
        time.sleep(0.1)  # Simulate work

    logger.info(f"Worker {mp.current_process().pid} completed task {task_id}")

    # Clear task context when done
    if 'DUAL_HANDLER' in globals() and DUAL_HANDLER:
        DUAL_HANDLER.set_task_context(None)

    return f"Task {task_id} completed"

def test_dual_logging():
    """Test the dual logging system with multiple processes"""
    print("Setting up dual logging system...")

    # Clean up any existing test directories
    import shutil
    if os.path.exists("./test_tasks"):
        shutil.rmtree("./test_tasks")

    log_queue, queue_listener = setup_centralized_logging()

    # Test main process logging
    main_logger = logging.getLogger("Main")
    main_logger.info("Dual logging system initialized")
    main_logger.info("Starting multi-process dual logging test")

    try:
        # Test with multiple worker processes
        with ProcessPoolExecutor(
            max_workers=3,
            initializer=worker_init,
            initargs=(log_queue,)
        ) as executor:

            main_logger.info("Submitting tasks to worker processes...")

            # Submit multiple tasks
            futures = []
            for task_id in range(1, 6):
                future = executor.submit(worker_task, task_id, log_queue)
                futures.append(future)

            # Wait for all tasks to complete
            for future in futures:
                result = future.result()
                main_logger.info(f"Received result: {result}")

        main_logger.info("All tasks completed successfully")
        main_logger.info("Verifying log files were created...")

        # Verify that log files were created for each task
        for task_id in range(1, 6):
            log_file_path = f"./test_tasks/{task_id}/devoutput/worker.log"
            if os.path.exists(log_file_path):
                with open(log_file_path, 'r') as f:
                    content = f.read()
                    line_count = len(content.strip().split('\n'))
                    main_logger.info(f"✅ Task {task_id} log file created with {line_count} lines")
            else:
                main_logger.error(f"❌ Task {task_id} log file not found at {log_file_path}")

        main_logger.info("Dual logging test completed")

    finally:
        # Clean shutdown of logging system
        try:
            queue_listener.stop()
            print("Logging system shutdown complete.", file=sys.stderr)
        except Exception as e:
            print(f"Error during logging shutdown: {e}", file=sys.stderr)

if __name__ == "__main__":
    test_dual_logging()
