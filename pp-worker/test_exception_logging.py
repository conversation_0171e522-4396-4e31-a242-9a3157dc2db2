#!/usr/bin/env python3
"""
Test script to verify that the enhanced dual logging system correctly handles
exceptions and flushes logs immediately when exceptions occur.
"""

import logging
import sys
import time
import os
import shutil
import multiprocessing as mp
from logging.handlers import Que<PERSON><PERSON><PERSON><PERSON>, QueueListener
from concurrent.futures import ProcessPoolExecutor

class DualOutputHandler(logging.Handler):
    """
    Custom logging handler that sends logs to both:
    1. Central queue for console output (process-safe)
    2. Individual task log file (when task context is available)
    """
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue
        self.queue_handler = QueueHandler(log_queue)
        self.current_task_id = None
        self.current_file_handler = None

    def set_task_context(self, task_id):
        """Set the current task context for file logging"""
        if self.current_task_id == task_id:
            return  # Already set to this task

        # Close previous file handler if exists
        if self.current_file_handler:
            self.current_file_handler.close()
            self.current_file_handler = None

        self.current_task_id = task_id

        if task_id:
            # Create file handler for this task
            log_dir = f"test_tasks/{task_id}/devoutput"
            os.makedirs(log_dir, exist_ok=True)
            log_file_path = f"{log_dir}/worker.log"

            self.current_file_handler = logging.FileHandler(log_file_path, mode='a')
            self.current_file_handler.setFormatter(
                logging.Formatter('[%(asctime)s] %(levelname)s [%(name)s]: %(message)s')
            )
    
    def emit(self, record):
        """Emit log record to both console queue and task file"""
        # Always send to central queue for console output
        self.queue_handler.emit(record)
        
        # Also write to task file if available
        if self.current_file_handler:
            try:
                self.current_file_handler.emit(record)
            except Exception:
                # If file writing fails, continue with console logging only
                pass
    
    def close(self):
        """Clean up handlers"""
        if self.current_file_handler:
            self.current_file_handler.close()
            self.current_file_handler = None
        super().close()

def flush_all_logs():
    """
    Force flush all logging handlers to ensure logs are written immediately.
    This is critical when exceptions occur to prevent log buffer loss.
    """
    try:
        # Flush the dual handler if available
        if DUAL_HANDLER:
            # Flush the queue handler (console output)
            if hasattr(DUAL_HANDLER, 'queue_handler') and hasattr(DUAL_HANDLER.queue_handler, 'flush'):
                DUAL_HANDLER.queue_handler.flush()
            
            # Flush the current file handler if available
            if DUAL_HANDLER.current_file_handler and hasattr(DUAL_HANDLER.current_file_handler, 'flush'):
                DUAL_HANDLER.current_file_handler.flush()
        
        # Flush all handlers on the root logger as fallback
        root_logger = logging.getLogger()
        for handler in root_logger.handlers:
            if hasattr(handler, 'flush'):
                handler.flush()
                
        # Force system stdout/stderr flush as additional safety
        import sys
        if hasattr(sys.stdout, 'flush'):
            sys.stdout.flush()
        if hasattr(sys.stderr, 'flush'):
            sys.stderr.flush()
            
    except Exception:
        # If flushing fails, don't raise another exception
        # Just continue - this is a safety mechanism
        pass

def setup_logging():
    """Set up the centralized logging system"""
    log_queue = mp.Manager().Queue(-1)
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter('[%(asctime)s] %(levelname)s [%(name)s]: %(message)s'))

    queue_listener = QueueListener(log_queue, console_handler)
    queue_listener.start()

    root_logger.handlers = [QueueHandler(log_queue)]

    return log_queue, queue_listener

def worker_init(log_q):
    """Initialize worker process with dual logging"""
    global DUAL_HANDLER
    DUAL_HANDLER = DualOutputHandler(log_q)
    worker_root = logging.getLogger()
    worker_root.handlers = [DUAL_HANDLER]
    worker_root.setLevel(logging.INFO)

def simulate_task_with_exception(task_id, exception_type="normal"):
    """Simulate a task that logs messages and then raises an exception"""
    global DUAL_HANDLER
    
    # Set task context for file logging
    if 'DUAL_HANDLER' in globals() and DUAL_HANDLER:
        DUAL_HANDLER.set_task_context(task_id)

    logger = logging.getLogger(f"Worker-{mp.current_process().pid}")
    
    try:
        logger.info(f"Worker {mp.current_process().pid} starting task {task_id}")
        
        # Simulate some work with logging
        for i in range(3):
            logger.info(f"Task {task_id} - Step {i+1}/3")
            time.sleep(0.1)  # Simulate work
        
        logger.info(f"Task {task_id} - About to raise {exception_type} exception")
        
        # Raise different types of exceptions to test
        if exception_type == "normal":
            raise ValueError(f"Test exception in task {task_id}")
        elif exception_type == "critical":
            raise RuntimeError(f"Critical error in task {task_id}")
        else:
            raise Exception(f"Generic exception in task {task_id}")
            
    except Exception as e:
        logger.error(f"Task {task_id} failed with exception: {e}", exc_info=True)
        # Force flush logs immediately when exception occurs
        flush_all_logs()
        raise  # Re-raise the exception
    finally:
        # Force flush logs before cleanup
        flush_all_logs()
        
        # Clear task context when done
        if 'DUAL_HANDLER' in globals() and DUAL_HANDLER:
            DUAL_HANDLER.set_task_context(None)
            
        # Final flush after cleanup
        flush_all_logs()

def main():
    """Test the exception logging functionality"""
    print("Testing exception logging with dual output...")
    
    # Clean up any previous test files
    if os.path.exists("test_tasks"):
        shutil.rmtree("test_tasks")
    
    # Set up logging
    log_queue, queue_listener = setup_logging()
    
    try:
        with ProcessPoolExecutor(max_workers=2, initializer=worker_init, initargs=(log_queue,)) as executor:
            # Test different types of exceptions
            test_cases = [
                ("task_001", "normal"),
                ("task_002", "critical"),
                ("task_003", "generic")
            ]
            
            futures = []
            for task_id, exception_type in test_cases:
                print(f"Submitting {task_id} with {exception_type} exception...")
                future = executor.submit(simulate_task_with_exception, task_id, exception_type)
                futures.append((task_id, future))
            
            # Wait for all tasks and handle exceptions
            for task_id, future in futures:
                try:
                    future.result()  # This will raise the exception from the worker
                except Exception as e:
                    print(f"Caught exception from {task_id}: {e}")
                    
        print("\nAll tasks completed. Checking log files...")
        
        # Check if log files were created and contain the expected content
        for task_id, _ in test_cases:
            log_file = f"test_tasks/{task_id}/devoutput/worker.log"
            if os.path.exists(log_file):
                print(f"\n✓ Log file exists for {task_id}: {log_file}")
                with open(log_file, 'r') as f:
                    content = f.read()
                    if "About to raise" in content and "failed with exception" in content:
                        print(f"✓ {task_id}: Log file contains expected exception logs")
                    else:
                        print(f"✗ {task_id}: Log file missing expected content")
                        print(f"Content: {content}")
            else:
                print(f"✗ Log file missing for {task_id}: {log_file}")
                
    finally:
        # Clean up logging
        queue_listener.stop()
        print("\nTest completed.")

if __name__ == "__main__":
    # Global variable for dual handler
    DUAL_HANDLER = None
    main()
