#!/usr/bin/env python3
"""
Test script to verify that the logging fix resolves the visibility issue.
This script creates dummy tasks and runs a limited worker to test logging visibility.
"""

import logging
import time
import threading
from concurrent.futures import ProcessPoolExecutor
from multiprocessing import Event, current_process
import signal
import sys
import os

# Add the current directory to the path so we can import our modules
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

import config
import database

# Test configuration
TEST_MAX_WORKERS = 5
TEST_TASK_COUNT = 15
TEST_DURATION = 30  # seconds

# Initialize logging for main test process
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s [Test-Main]: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    force=True
)

# Ensure no buffering
for handler in logging.getLogger().handlers:
    handler.setFormatter(logging.Formatter('[%(asctime)s] %(levelname)s [Test-Main]: %(message)s'))
    if hasattr(handler, 'flush'):
        handler.flush()

# Reduce noise from external libraries
logging.getLogger("httpx").setLevel(logging.WARNING)
logging.getLogger("supabase").setLevel(logging.WARNING)

# Global shutdown event
shutdown_event = Event()

def test_worker_init(shutdown_event_flag):
    """Initialize test worker process with proper logging configuration."""
    signal.signal(signal.SIGINT, signal.SIG_IGN)
    
    global SHUTDOWN_EVENT
    SHUTDOWN_EVENT = shutdown_event_flag
    
    # Configure logging for this worker process
    logging.basicConfig(
        level=logging.INFO,
        format='[%(asctime)s] %(levelname)s [Test-Worker-%(process)d]: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        force=True
    )
    
    # Ensure no buffering for immediate log visibility
    for handler in logging.getLogger().handlers:
        handler.setFormatter(logging.Formatter('[%(asctime)s] %(levelname)s [Test-Worker-%(process)d]: %(message)s'))
        if hasattr(handler, 'flush'):
            handler.flush()
    
    # Reduce noise from external libraries
    logging.getLogger("httpx").setLevel(logging.WARNING)
    logging.getLogger("supabase").setLevel(logging.WARNING)
    
    # Log worker process initialization
    logging.info(f"Test worker process {current_process().pid} initialized")

def simulate_task_processing(task_id):
    """Simulate task processing with logging."""
    logging.info(f"Starting processing for test task {task_id}")
    
    # Simulate some work with periodic logging
    for i in range(3):
        if SHUTDOWN_EVENT and SHUTDOWN_EVENT.is_set():
            logging.info(f"Shutdown signal received, stopping test task {task_id}")
            return
        
        logging.info(f"Test task {task_id} - processing step {i+1}/3")
        time.sleep(1)  # Simulate work
    
    logging.info(f"Test task {task_id} completed successfully")
    
    # Ensure logs are flushed immediately
    for handler in logging.getLogger().handlers:
        if hasattr(handler, 'flush'):
            handler.flush()

def create_dummy_tasks():
    """Create dummy tasks in the database for testing."""
    logging.info(f"Creating {TEST_TASK_COUNT} dummy test tasks...")
    
    created_tasks = []
    for i in range(TEST_TASK_COUNT):
        try:
            # Create a simple dummy task
            task_data = {
                'status': 'queued',
                'job_json': {'type': 'dummy'},
                'vars': {'vars': []},
                'workervars': []
            }
            
            # Insert task into database
            response = database.supabaseclient.table('tasks').insert(task_data).execute()
            if response.data:
                task_id = response.data[0]['id']
                created_tasks.append(task_id)
                logging.info(f"Created dummy test task {task_id}")
            else:
                logging.error(f"Failed to create dummy task {i+1}")
        except Exception as e:
            logging.error(f"Error creating dummy task {i+1}: {e}")
    
    logging.info(f"Successfully created {len(created_tasks)} dummy test tasks: {created_tasks}")
    return created_tasks

def cleanup_test_tasks(task_ids):
    """Clean up test tasks from the database."""
    logging.info(f"Cleaning up {len(task_ids)} test tasks...")
    
    for task_id in task_ids:
        try:
            database.supabaseclient.table('tasks').delete().eq('id', task_id).execute()
            logging.info(f"Deleted test task {task_id}")
        except Exception as e:
            logging.error(f"Error deleting test task {task_id}: {e}")

def run_logging_test():
    """Run the main logging visibility test."""
    logging.info("=" * 60)
    logging.info("STARTING LOGGING VISIBILITY TEST")
    logging.info("=" * 60)
    
    # Create test tasks
    test_task_ids = create_dummy_tasks()
    if not test_task_ids:
        logging.error("No test tasks created, aborting test")
        return False
    
    active_workers = 0
    active_workers_lock = threading.Lock()
    
    def task_done_callback(future, task_id):
        nonlocal active_workers
        try:
            future.result()
            logging.info(f"Test task {task_id} worker completed")
        except Exception as exc:
            logging.error(f"Test task {task_id} generated an exception: {exc}")
        finally:
            with active_workers_lock:
                active_workers -= 1
                logging.info(f"Test task {task_id} finished. Active workers: {active_workers}/{TEST_MAX_WORKERS}")
    
    logging.info(f"Starting test with {TEST_MAX_WORKERS} workers processing {len(test_task_ids)} tasks")
    
    try:
        with ProcessPoolExecutor(
            max_workers=TEST_MAX_WORKERS,
            initializer=test_worker_init,
            initargs=(shutdown_event,)
        ) as executor:
            
            futures = {}
            task_index = 0
            start_time = time.time()
            
            # Submit tasks gradually to simulate real workload
            while task_index < len(test_task_ids) and time.time() - start_time < TEST_DURATION:
                with active_workers_lock:
                    if active_workers < TEST_MAX_WORKERS and task_index < len(test_task_ids):
                        task_id = test_task_ids[task_index]
                        task_index += 1
                        active_workers += 1
                        
                        logging.info(f"Submitting test task {task_id}. Active workers: {active_workers}/{TEST_MAX_WORKERS}")
                        
                        future = executor.submit(simulate_task_processing, task_id)
                        futures[task_id] = future
                        
                        future.add_done_callback(
                            lambda fut, tid=task_id: task_done_callback(fut, tid)
                        )
                
                time.sleep(0.5)  # Brief pause between submissions
            
            # Wait for all tasks to complete
            logging.info("Waiting for all test tasks to complete...")
            for future in futures.values():
                future.result(timeout=30)
            
            logging.info("All test tasks completed")
            
    except Exception as e:
        logging.error(f"Error during test execution: {e}", exc_info=True)
        return False
    finally:
        # Clean up test tasks
        cleanup_test_tasks(test_task_ids)
    
    logging.info("=" * 60)
    logging.info("LOGGING VISIBILITY TEST COMPLETED")
    logging.info("=" * 60)
    
    return True

if __name__ == "__main__":
    try:
        success = run_logging_test()
        if success:
            logging.info("✅ Logging test completed successfully!")
            print("\n" + "="*60)
            print("TEST RESULTS:")
            print("✅ All worker processes should have shown logs")
            print("✅ Each task should show start, progress, and completion logs")
            print("✅ Worker process IDs should be visible in log format")
            print("="*60)
        else:
            logging.error("❌ Logging test failed!")
            sys.exit(1)
    except KeyboardInterrupt:
        logging.info("Test interrupted by user")
        shutdown_event.set()
    except Exception as e:
        logging.error(f"Test failed with exception: {e}", exc_info=True)
        sys.exit(1)
