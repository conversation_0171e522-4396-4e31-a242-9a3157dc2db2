#!/usr/bin/env python3
"""
Simple test script to verify that the logging fix resolves the visibility issue.
This script tests the logging configuration without requiring database access.
"""

import logging
import time
import threading
from concurrent.futures import ProcessPoolExecutor
from multiprocessing import Event, current_process
import signal

# Test configuration
TEST_MAX_WORKERS = 8
TEST_TASK_COUNT = 20

# Initialize logging for main test process
logging.basicConfig(
    level=logging.INFO,
    format='[%(asctime)s] %(levelname)s [Test-Main]: %(message)s',
    datefmt='%Y-%m-%d %H:%M:%S',
    force=True
)

# Ensure no buffering
for handler in logging.getLogger().handlers:
    handler.setFormatter(logging.Formatter('[%(asctime)s] %(levelname)s [Test-Main]: %(message)s'))
    if hasattr(handler, 'flush'):
        handler.flush()

# Global shutdown event
shutdown_event = Event()

def test_worker_init(shutdown_event_flag):
    """Initialize test worker process with proper logging configuration."""
    signal.signal(signal.SIGINT, signal.SIG_IGN)
    
    global SHUTDOWN_EVENT
    SHUTDOWN_EVENT = shutdown_event_flag
    
    # Configure logging for this worker process
    logging.basicConfig(
        level=logging.INFO,
        format='[%(asctime)s] %(levelname)s [Test-Worker-%(process)d]: %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S',
        force=True
    )
    
    # Ensure no buffering for immediate log visibility
    for handler in logging.getLogger().handlers:
        handler.setFormatter(logging.Formatter('[%(asctime)s] %(levelname)s [Test-Worker-%(process)d]: %(message)s'))
        if hasattr(handler, 'flush'):
            handler.flush()
    
    # Log worker process initialization
    logging.info(f"Test worker process {current_process().pid} initialized")

def simulate_task_processing(task_id):
    """Simulate task processing with logging."""
    logging.info(f"Starting processing for test task {task_id}")
    
    # Simulate some work with periodic logging
    for i in range(5):
        if SHUTDOWN_EVENT and SHUTDOWN_EVENT.is_set():
            logging.info(f"Shutdown signal received, stopping test task {task_id}")
            return
        
        logging.info(f"Test task {task_id} - processing step {i+1}/5")
        time.sleep(0.5)  # Simulate work
    
    logging.info(f"Test task {task_id} completed successfully")
    
    # Ensure logs are flushed immediately
    for handler in logging.getLogger().handlers:
        if hasattr(handler, 'flush'):
            handler.flush()

def run_logging_test():
    """Run the main logging visibility test."""
    logging.info("=" * 80)
    logging.info("STARTING SIMPLE LOGGING VISIBILITY TEST")
    logging.info("=" * 80)
    
    active_workers = 0
    active_workers_lock = threading.Lock()
    
    def task_done_callback(future, task_id):
        nonlocal active_workers
        try:
            future.result()
            logging.info(f"Test task {task_id} worker completed")
        except Exception as exc:
            logging.error(f"Test task {task_id} generated an exception: {exc}")
        finally:
            with active_workers_lock:
                active_workers -= 1
                logging.info(f"Test task {task_id} finished. Active workers: {active_workers}/{TEST_MAX_WORKERS}")
    
    logging.info(f"Starting test with {TEST_MAX_WORKERS} workers processing {TEST_TASK_COUNT} tasks")
    
    try:
        with ProcessPoolExecutor(
            max_workers=TEST_MAX_WORKERS,
            initializer=test_worker_init,
            initargs=(shutdown_event,)
        ) as executor:
            
            futures = {}
            
            # Submit all tasks
            for task_id in range(1, TEST_TASK_COUNT + 1):
                with active_workers_lock:
                    active_workers += 1
                    
                logging.info(f"Submitting test task {task_id}. Active workers: {active_workers}/{TEST_MAX_WORKERS}")
                
                future = executor.submit(simulate_task_processing, task_id)
                futures[task_id] = future
                
                future.add_done_callback(
                    lambda fut, tid=task_id: task_done_callback(fut, tid)
                )
                
                # Brief pause to stagger submissions
                time.sleep(0.1)
            
            # Wait for all tasks to complete
            logging.info("Waiting for all test tasks to complete...")
            for task_id, future in futures.items():
                try:
                    future.result(timeout=30)
                except Exception as e:
                    logging.error(f"Task {task_id} failed: {e}")
            
            logging.info("All test tasks completed")
            
    except Exception as e:
        logging.error(f"Error during test execution: {e}", exc_info=True)
        return False
    
    logging.info("=" * 80)
    logging.info("SIMPLE LOGGING VISIBILITY TEST COMPLETED")
    logging.info("=" * 80)
    
    return True

if __name__ == "__main__":
    try:
        print("Starting logging visibility test...")
        print("This test will create multiple worker processes and verify that logs from all workers are visible.")
        print("Look for logs with [Test-Worker-XXXXX] format - these should be visible from all worker processes.")
        print("If the fix works, you should see logs from all worker processes, not just the first few.")
        print()
        
        success = run_logging_test()
        
        print("\n" + "="*80)
        print("TEST RESULTS:")
        if success:
            print("✅ Logging test completed successfully!")
            print("✅ Check the output above - you should see logs from multiple worker processes")
            print("✅ Each worker process should have a unique process ID in the log format")
            print("✅ All tasks should show start, progress, and completion logs")
            print("✅ If you see logs from worker processes beyond the first few, the fix is working!")
        else:
            print("❌ Logging test failed!")
        print("="*80)
        
    except KeyboardInterrupt:
        logging.info("Test interrupted by user")
        shutdown_event.set()
    except Exception as e:
        logging.error(f"Test failed with exception: {e}", exc_info=True)
        exit(1)
