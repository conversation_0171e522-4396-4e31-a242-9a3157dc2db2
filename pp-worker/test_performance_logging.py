#!/usr/bin/env python3
"""
Test script to verify that performance timing logs are correctly written
to both console and individual task log files.
"""

import logging
import sys
import time
import os
import shutil
import multiprocessing as mp
from logging.handlers import <PERSON><PERSON><PERSON><PERSON><PERSON>, QueueListener
from concurrent.futures import ProcessPoolExecutor

class DualOutputHandler(logging.Handler):
    """
    Custom logging handler that sends logs to both:
    1. Central queue for console output (process-safe)
    2. Individual task log file (when task context is available)
    """
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue
        self.queue_handler = QueueHandler(log_queue)
        self.current_task_id = None
        self.current_file_handler = None
        
    def set_task_context(self, task_id):
        """Set the current task context for file logging"""
        if self.current_task_id == task_id:
            return  # Already set to this task
            
        # Close previous file handler if exists
        if self.current_file_handler:
            self.current_file_handler.close()
            self.current_file_handler = None
            
        self.current_task_id = task_id
        
        if task_id:
            # Create file handler for this task
            try:
                log_dir = f"./test_performance/{task_id}/devoutput"
                os.makedirs(log_dir, exist_ok=True)
                log_file_path = f"{log_dir}/worker.log"
                
                self.current_file_handler = logging.FileHandler(log_file_path, mode='a')
                self.current_file_handler.setFormatter(
                    logging.Formatter('[%(asctime)s] %(levelname)s [%(name)s]: %(message)s')
                )
            except Exception as e:
                # If file handler creation fails, log to console only
                print(f"Warning: Could not create log file for task {task_id}: {e}", file=sys.stderr)
                self.current_file_handler = None
    
    def emit(self, record):
        """Emit log record to both console queue and task file"""
        # Always send to central queue for console output
        self.queue_handler.emit(record)
        
        # Also write to task file if available
        if self.current_file_handler:
            try:
                self.current_file_handler.emit(record)
            except Exception:
                # If file writing fails, continue with console logging only
                pass
    
    def close(self):
        """Clean up handlers"""
        if self.current_file_handler:
            self.current_file_handler.close()
            self.current_file_handler = None
        super().close()

def setup_logging():
    """Set up the centralized logging system"""
    log_queue = mp.Manager().Queue(-1)
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter('[%(asctime)s] %(levelname)s [%(name)s]: %(message)s'))

    queue_listener = QueueListener(log_queue, console_handler)
    queue_listener.start()

    root_logger.handlers = [QueueHandler(log_queue)]

    return log_queue, queue_listener

def worker_init(log_q):
    """Initialize worker process with dual logging"""
    global DUAL_HANDLER
    DUAL_HANDLER = DualOutputHandler(log_q)
    worker_root = logging.getLogger()
    worker_root.handlers = [DUAL_HANDLER]
    worker_root.setLevel(logging.INFO)

class MockTaskProcessor:
    """Mock TaskProcessor that simulates timing measurements"""
    def __init__(self):
        self.timing_data = {
            'download_time': 0.0,
            'processing_time': 0.0,
            'upload_time': 0.0,
            'database_time': 0.0,
            'total_start_time': None
        }
        self.task = None
    
    def _start_timing(self, operation_type):
        """Start timing for a specific operation type"""
        return time.time()
    
    def _end_timing(self, operation_type, start_time):
        """End timing for a specific operation type and accumulate the duration"""
        duration = time.time() - start_time
        self.timing_data[operation_type] += duration
        return duration
    
    def _get_dataset_name(self):
        """Extract dataset name from task configuration"""
        return f"TestDataset-{self.task.get('id', 'Unknown')}"
    
    def _log_performance_summary(self):
        """Log consolidated performance summary for the completed task"""
        if self.timing_data['total_start_time'] is None:
            return

        total_time = time.time() - self.timing_data['total_start_time']
        task_id = self.task.get('id', 'UNKNOWN')
        dataset_name = self._get_dataset_name()

        # Format timing data
        download_time = self.timing_data['download_time']
        processing_time = self.timing_data['processing_time']
        upload_time = self.timing_data['upload_time']
        database_time = self.timing_data['database_time']

        # Log the consolidated performance summary
        logging.info(
            f"Task [{task_id}] Dataset [{dataset_name}] completed - "
            f"Download: {download_time:.1f}s, Processing: {processing_time:.1f}s, "
            f"Upload: {upload_time:.1f}s, Database: {database_time:.1f}s, "
            f"Total: {total_time:.1f}s"
        )
    
    def process_task(self, task_data):
        """Mock task processing with timing"""
        self.task = task_data
        self.timing_data['total_start_time'] = time.time()
        
        try:
            # Simulate download
            download_start = self._start_timing('download_time')
            time.sleep(0.05)  # Simulate download time
            self._end_timing('download_time', download_start)
            
            # Simulate processing
            processing_start = self._start_timing('processing_time')
            time.sleep(0.1)  # Simulate processing time
            self._end_timing('processing_time', processing_start)
            
            # Simulate upload
            upload_start = self._start_timing('upload_time')
            time.sleep(0.03)  # Simulate upload time
            self._end_timing('upload_time', upload_start)
            
            # Simulate database operations
            db_start = self._start_timing('database_time')
            time.sleep(0.02)  # Simulate database time
            self._end_timing('database_time', db_start)
            
        finally:
            # Log performance summary (like in real TaskProcessor)
            self._log_performance_summary()

def mock_run_task(task_data, log_q):
    """Mock version of run_task function"""
    global DUAL_HANDLER
    task_id = task_data.get('id', 'UNKNOWN')

    # Set task context for dual logging
    if 'DUAL_HANDLER' in globals() and DUAL_HANDLER:
        DUAL_HANDLER.set_task_context(task_id)

    logger = logging.getLogger(f"Worker-{mp.current_process().pid}")
    logger.info(f"Starting task processing for task {task_id}")

    processor = None
    try:
        processor = MockTaskProcessor()
        processor.process_task(task_data)
        logger.info(f"Task {task_id} processing completed successfully")
    except Exception as e:
        logger.error(f"Task {task_id} processing failed with exception: {e}", exc_info=True)
        raise
    finally:
        # Ensure performance summary is logged to task file before clearing context
        if processor is not None:
            try:
                # Small delay to ensure all logs from TaskProcessor are flushed to file
                time.sleep(0.01)  # 10ms delay to ensure log flushing
            except Exception:
                pass

        # Clear task context when task is done
        try:
            if 'DUAL_HANDLER' in globals() and DUAL_HANDLER:
                DUAL_HANDLER.set_task_context(None)
        except Exception:
            pass

    return f"Task {task_id} completed"

def test_performance_logging():
    """Test that performance timing logs appear in both console and task files"""
    print("Testing performance timing logs in dual logging system...")
    
    # Clean up any existing test directories
    if os.path.exists("./test_performance"):
        shutil.rmtree("./test_performance")
    
    log_queue, queue_listener = setup_logging()
    
    main_logger = logging.getLogger("Main")
    main_logger.info("Performance logging test initialized")
    
    try:
        # Test with multiple worker processes
        with ProcessPoolExecutor(
            max_workers=2,
            initializer=worker_init,
            initargs=(log_queue,)
        ) as executor:
            
            main_logger.info("Submitting tasks with performance timing...")
            
            # Submit multiple tasks
            futures = []
            for task_id in range(1001, 1004):  # Tasks 1001, 1002, 1003
                task_data = {'id': task_id}
                future = executor.submit(mock_run_task, task_data, log_queue)
                futures.append((task_id, future))
            
            # Wait for all tasks to complete
            for task_id, future in futures:
                result = future.result()
                main_logger.info(f"Received result: {result}")
        
        main_logger.info("All tasks completed")
        main_logger.info("Verifying performance logs in task files...")
        
        # Verify that performance summary logs are in task files
        for task_id in range(1001, 1004):
            log_file_path = f"./test_performance/{task_id}/devoutput/worker.log"
            if os.path.exists(log_file_path):
                with open(log_file_path, 'r') as f:
                    content = f.read()
                    lines = [line for line in content.strip().split('\n') if line.strip()]
                    
                    # Look for performance summary line
                    performance_lines = [line for line in lines if 'completed -' in line and 'Download:' in line]
                    
                    if performance_lines:
                        main_logger.info(f"✅ Task {task_id}: Performance summary found in log file")
                        main_logger.info(f"   Performance line: {performance_lines[0].split('] ', 1)[1]}")
                    else:
                        main_logger.error(f"❌ Task {task_id}: Performance summary NOT found in log file")
                        main_logger.info(f"   Log content ({len(lines)} lines):")
                        for i, line in enumerate(lines, 1):
                            main_logger.info(f"   {i}: {line}")
            else:
                main_logger.error(f"❌ Task {task_id}: Log file not found")
        
        main_logger.info("Performance logging test completed")
        
    finally:
        try:
            queue_listener.stop()
            print("Logging system shutdown complete.", file=sys.stderr)
        except Exception as e:
            print(f"Error during logging shutdown: {e}", file=sys.stderr)

if __name__ == "__main__":
    test_performance_logging()
