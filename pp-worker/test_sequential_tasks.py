#!/usr/bin/env python3
"""
Test script to verify that the dual logging system correctly handles
sequential tasks processed by the same worker process.
"""

import logging
import sys
import time
import os
import shutil
import multiprocessing as mp
from logging.handlers import Que<PERSON><PERSON><PERSON><PERSON>, QueueListener
from concurrent.futures import ProcessPoolExecutor

class DualOutputHandler(logging.Handler):
    """
    Custom logging handler that sends logs to both:
    1. Central queue for console output (process-safe)
    2. Individual task log file (when task context is available)
    """
    def __init__(self, log_queue):
        super().__init__()
        self.log_queue = log_queue
        self.queue_handler = QueueHandler(log_queue)
        self.current_task_id = None
        self.current_file_handler = None
        
    def set_task_context(self, task_id):
        """Set the current task context for file logging"""
        if self.current_task_id == task_id:
            return  # Already set to this task
            
        # Close previous file handler if exists
        if self.current_file_handler:
            self.current_file_handler.close()
            self.current_file_handler = None
            
        self.current_task_id = task_id
        
        if task_id:
            # Create file handler for this task
            try:
                log_dir = f"./test_sequential/{task_id}/devoutput"
                os.makedirs(log_dir, exist_ok=True)
                log_file_path = f"{log_dir}/worker.log"
                
                self.current_file_handler = logging.FileHandler(log_file_path, mode='a')
                self.current_file_handler.setFormatter(
                    logging.Formatter('[%(asctime)s] %(levelname)s [%(name)s]: %(message)s')
                )
            except Exception as e:
                # If file handler creation fails, log to console only
                print(f"Warning: Could not create log file for task {task_id}: {e}", file=sys.stderr)
                self.current_file_handler = None
    
    def emit(self, record):
        """Emit log record to both console queue and task file"""
        # Always send to central queue for console output
        self.queue_handler.emit(record)
        
        # Also write to task file if available
        if self.current_file_handler:
            try:
                self.current_file_handler.emit(record)
            except Exception:
                # If file writing fails, continue with console logging only
                pass
    
    def close(self):
        """Clean up handlers"""
        if self.current_file_handler:
            self.current_file_handler.close()
            self.current_file_handler = None
        super().close()

def setup_logging():
    """Set up the centralized logging system"""
    log_queue = mp.Manager().Queue(-1)
    root_logger = logging.getLogger()
    root_logger.setLevel(logging.INFO)

    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setFormatter(logging.Formatter('[%(asctime)s] %(levelname)s [%(name)s]: %(message)s'))

    queue_listener = QueueListener(log_queue, console_handler)
    queue_listener.start()

    root_logger.handlers = [QueueHandler(log_queue)]

    return log_queue, queue_listener

def worker_init(log_q):
    """Initialize worker process with dual logging"""
    global DUAL_HANDLER
    DUAL_HANDLER = DualOutputHandler(log_q)
    worker_root = logging.getLogger()
    worker_root.handlers = [DUAL_HANDLER]
    worker_root.setLevel(logging.INFO)

def sequential_worker_task(task_list, log_q):
    """Process multiple tasks sequentially in the same worker"""
    global DUAL_HANDLER
    
    worker_pid = mp.current_process().pid
    logger = logging.getLogger(f"Worker-{worker_pid}")
    
    results = []
    
    for task_id in task_list:
        # Set task context for file logging
        if 'DUAL_HANDLER' in globals() and DUAL_HANDLER:
            DUAL_HANDLER.set_task_context(task_id)
        
        logger.info(f"Worker {worker_pid} starting task {task_id}")
        
        # Simulate task processing
        for i in range(2):
            logger.info(f"Task {task_id} - Processing step {i+1}/2")
            time.sleep(0.05)  # Shorter sleep for faster test
        
        logger.info(f"Worker {worker_pid} completed task {task_id}")
        results.append(f"Task {task_id} completed")
        
        # Small delay between tasks
        time.sleep(0.02)
    
    # Clear task context when all tasks are done
    if 'DUAL_HANDLER' in globals() and DUAL_HANDLER:
        DUAL_HANDLER.set_task_context(None)
    
    return results

def test_sequential_tasks():
    """Test sequential task processing with dual logging"""
    print("Testing sequential task processing with dual logging...")
    
    # Clean up any existing test directories
    if os.path.exists("./test_sequential"):
        shutil.rmtree("./test_sequential")
    
    log_queue, queue_listener = setup_logging()
    
    main_logger = logging.getLogger("Main")
    main_logger.info("Sequential task test initialized")
    
    try:
        # Use only 1 worker to ensure sequential processing
        with ProcessPoolExecutor(
            max_workers=1,
            initializer=worker_init,
            initargs=(log_queue,)
        ) as executor:
            
            main_logger.info("Submitting sequential tasks to single worker...")
            
            # Submit tasks that will be processed sequentially by the same worker
            task_groups = [
                [101, 102, 103],  # First batch
                [201, 202, 203]   # Second batch
            ]
            
            futures = []
            for task_group in task_groups:
                future = executor.submit(sequential_worker_task, task_group, log_queue)
                futures.append(future)
            
            # Wait for all task groups to complete
            for i, future in enumerate(futures):
                results = future.result()
                main_logger.info(f"Batch {i+1} results: {results}")
        
        main_logger.info("All sequential tasks completed")
        main_logger.info("Verifying individual log files...")
        
        # Verify that separate log files were created for each task
        all_task_ids = [101, 102, 103, 201, 202, 203]
        for task_id in all_task_ids:
            log_file_path = f"./test_sequential/{task_id}/devoutput/worker.log"
            if os.path.exists(log_file_path):
                with open(log_file_path, 'r') as f:
                    content = f.read()
                    lines = [line for line in content.strip().split('\n') if line.strip()]
                    main_logger.info(f"✅ Task {task_id}: {len(lines)} log lines")
                    
                    # Verify that only logs for this specific task are in the file
                    task_specific_lines = [line for line in lines if f"task {task_id}" in line.lower()]
                    if len(task_specific_lines) >= 3:  # Start + 2 steps + completion
                        main_logger.info(f"✅ Task {task_id}: Contains task-specific logs")
                    else:
                        main_logger.warning(f"⚠️  Task {task_id}: May contain mixed logs")
            else:
                main_logger.error(f"❌ Task {task_id}: Log file not found")
        
        main_logger.info("Sequential task test completed successfully")
        
    finally:
        try:
            queue_listener.stop()
            print("Logging system shutdown complete.", file=sys.stderr)
        except Exception as e:
            print(f"Error during logging shutdown: {e}", file=sys.stderr)

if __name__ == "__main__":
    test_sequential_tasks()
