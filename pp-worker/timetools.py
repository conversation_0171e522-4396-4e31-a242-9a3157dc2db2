import math

# Namespace StarlingTools
class StarlingTools:

    LookUp_Epochs_of_new_UTCLeapSeconds_in_Nanoseconds_sinceGPSEpoch0 = [
        18,
        1167177600000000000,           # introduced on 2016/12/31
        1119657600000000000,           # introduced on 2015/06/30
        1025049600000000000,           # introduced on 2012/06/30
        914716800000000000,            # introduced on 2008/12/31
        820022400000000000,            # introduced on 2005/12/31
        599097600000000000,            # introduced on 1998/12/31
        551664000000000000,            # introduced on 1997/06/30
        504403200000000000,            # introduced on 1995/12/31
        456969600000000000,            # introduced on 1994/06/30
        425433600000000000,            # introduced on 1993/06/30
        393897600000000000,            # introduced on 1992/06/30
        346636800000000000,            # introduced on 1990/12/31
        315100800000000000,            # introduced on 1989/12/31
        251942400000000000,            # introduced on 1987/12/31
        172972800000000000,            # introduced on 1985/06/30
        109814400000000000,            # introduced on 1983/06/30
        78278400000000000,             # introduced on 1982/06/30
        46742400000000000              # introduced on 1981/06/30
    ]

    MJD_of_GPS_Epoch_Zero  = 44244
    MJD_of_Unix_Epoch_Zero = 40587

    LookUp_GPSDaysSince1980UntilYear_From1980To2100 = [
                    0,   366,   731,  1096,  1461,  1827,  2192,  2557,  2922,  3288,
        3653,  4018,  4383,  4749,  5114,  5479,  5844,  6210,  6575,  6940,
        7305,  7671,  8036,  8401,  8766,  9132,  9497,  9862, 10227, 10593,
        10958, 11323, 11688, 12054, 12419, 12784, 13149, 13515, 13880, 14245,
        14610, 14976, 15341, 15706, 16071, 16437, 16802, 17167, 17532, 17898,
        18263, 18628, 18993, 19359, 19724, 20089, 20454, 20820, 21185, 21550,
        21915, 22281, 22646, 23011, 23376, 23742, 24107, 24472, 24837, 25203,
        25568, 25933, 26298, 26664, 27029, 27394, 27759, 28125, 28490, 28855,
        29220, 29586, 29951, 30316, 30681, 31047, 31412, 31777, 32142, 32508,
        32873, 33238, 33603, 33969, 34334, 34699, 35064, 35430, 35795, 36160,
        36525, 36891, 37256, 37621, 37986, 38352, 38717, 39082, 39447, 39813,
        40178, 40543, 40908, 41274, 41639, 42004, 42369, 42735, 43100, 43465,
        43830
    ]

    class TimeTools:

        @staticmethod
        def getGPSLeapSeconds(t):
            out = StarlingTools.LookUp_Epochs_of_new_UTCLeapSeconds_in_Nanoseconds_sinceGPSEpoch0[0]
            index = 1
            while out >= 1 and t < StarlingTools.LookUp_Epochs_of_new_UTCLeapSeconds_in_Nanoseconds_sinceGPSEpoch0[index]:
                out -= 1
                index += 1
            return out

        @staticmethod
        def getGPSDay(year, month, day):
            return StarlingTools.TimeTools.getDoY(year, month, day) + StarlingTools.TimeTools.daysOfFullYears(year) - 6

        @staticmethod
        def getGPSWeek(year, month, day):
            return StarlingTools.TimeTools.getGPSDay(year, month, day) // 7

        @staticmethod
        def getGPSDayOfWeek(year, month, day):
            return StarlingTools.TimeTools.getGPSDay(year, month, day) % 7

        @staticmethod
        def isLeapYear(year):
            if year % 4 != 0:
                return False
            if year % 400 == 0:
                return True
            if year % 100 == 0:
                return False
            return True

        @staticmethod
        def getDoY(year, month, day):
            if month < 1 or month > 12:
                raise Exception("getDoY(): Counting months from 1 (=January). Month must be >= 1 (Jan) and <= 12 (Dec).")
            daysAfterMonth = [0,31,59,90,120,151,181,212,243,273,304,334,365]
            out = daysAfterMonth[month-1] + day
            if StarlingTools.TimeTools.isLeapYear(year) and month >= 3:
                out += 1
            return out

        @staticmethod
        def daysOfFullYears(untilYear):
            if untilYear < 1980:
                raise Exception("daysOfFullYears(): min year is 1980.")
            if untilYear <= 2100:
                return StarlingTools.LookUp_GPSDaysSince1980UntilYear_From1980To2100[untilYear-1980]
            out = StarlingTools.LookUp_GPSDaysSince1980UntilYear_From1980To2100[120]
            for y in range(2100, untilYear):
                out += 366 if StarlingTools.TimeTools.isLeapYear(y) else 365
            return out

        @staticmethod
        def HMS2decimalDays(hour, minute, sec):
            return hour/24.0 + minute/1440.0 + sec/86400.0

        @staticmethod
        def JD2MJD(jd):
            return jd - 2400000.5

        @staticmethod
        def MJD2JD(mjd):
            return mjd + 2400000.5

        @staticmethod
        def UnixTime2MJD_int(uxTimeMillis):
            return (uxTimeMillis // 86400000) + StarlingTools.MJD_of_Unix_Epoch_Zero

        @staticmethod
        def UnixTime2MJD_dbl(uxTimeMillis):
            return uxTimeMillis / 86400000.0 + StarlingTools.MJD_of_Unix_Epoch_Zero

        @staticmethod
        def MJD2UnixTimeMillis(mjd):
            return (mjd - StarlingTools.MJD_of_Unix_Epoch_Zero) * 86400000

        @staticmethod
        def YMD2JD(year, month, day):
            notGregorian = (year<1582 or (year == 1582 and (month < 10 or (month==10 and day < 5))))
            if month < 3:
                year -= 1
                month += 12
            b = 0.0
            if not notGregorian:
                a = year // 100
                b = 2.0 - a + math.floor(a/4.0)
            return math.floor(365.25 * (year + 4716.0)) + math.floor(30.6001 * (month + 1)) + day + b - 1524.5

        @staticmethod
        def JD2YMD(jd):
            temp = jd + 0.5
            Z = math.trunc(temp)
            F = temp - Z
            A = Z
            if Z >= 2299161.0:
                alpha = math.floor((Z-1867216.25)/36524.25)
                A += 1.0 + alpha - math.floor(alpha/4.0)

            B = A + 1524.0
            C = math.floor((B-122.1)/365.25)
            D = math.floor(365.25*C)
            E = math.floor((B-D)/30.6001)

            day = B - D - math.floor(30.6001 * E) + F
            month = E - 1.0
            if E > 13:
                month -= 12.0
            year = C - 4716
            if month < 3:
                year += 1
            return [int(year), int(month), int(day)]

        @staticmethod
        def YMD2MJD(year, month, day):
            return StarlingTools.TimeTools.JD2MJD(StarlingTools.TimeTools.YMD2JD(year, month, day))