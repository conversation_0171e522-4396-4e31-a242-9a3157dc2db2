// alles was in dieser Datei steht ist die Ausgabe der Logging-GUI-Komponente, also das was dann im Feld "data" erscheinen muss!

{

    "label"  : "AlgoNav's Tighly-Coupling solution",



    // Variante 1: Time-triggered log
    "trigger": "RTS_TIME",       // fest
    "dt"     : 0.1,              // Log-Rate, ausgedrückt in Sekunden (also 1 / Lograte in Hertz)


    // // Variante 2: Sensor-triggered log
    // "trigger": "RTS_MEAS",       // fest
    // "sensor" : "dgnssX"  ,       // name des Sensors, zu dessen Mess-Epochen geloggt werden soll - entnommen aus dem "logprefix" des vom user gewählten Sensors [aus "available_sensors" im template]
    // "step"   : 1,                // mit step können die geloggten Epochen dezimiert werden - z.B. 10 --> nur jede 10. Epoche. Default wäre 1 (jede <PERSON>).
    

    // // Variante 3: IMU-triggered log   [allgemeiner: der Predictor triggert ein Logging. Die IMU finguert als (ein möglicher) Predictor.]
    // "trigger": "RTS_PREDICTOR",  // fest
    // "step"   : 1,                // mit step können die geloggten Epochen dezimiert werden - z.B. 10 --> nur jede 10. Epoche. Default wäre 1 (jede Epoche).


    // Weitere Log-Trigger-Typen (externe Zeitstempel, IMU-Epochen, Distanz-getriggert, ...) könnten später mal dazukommen...



    "csv_settings" : {
        "delim"                   : ",   ",      // delimiter between fields
        "delim_sub"               : ", ",        // delimiter between columns of the same field
        "eol"                     : "\n",        // end of line string (usually: simply a newline)
        "delimiter_after_last_col": false,       // use regular "delim" at line end (default: off)
        "output_full_header"      : true,        // output a long header to the csv file, with one header line per data column
        "output_header_row"       : true,        // output a header line with the column names just above the first data row
        "header_prefix"           : "# "         // use this prefix for all header lines.
    },




    "fields": [
        {
            "field"       : "T_WEEK_SOW",
            "logprefix"   : "",
            "caption"     : "GPS Week, Seconds of the Week",
            "cols"        : ["GPS_Week","GPS_SOW"],
            "ascii_format": ["%04d","%13.6f"],
            "precision"   : "f64"
        },
        {
            "field"       : "LLH_DEG",
            "logprefix"   : "",
            "caption"     : "Latitude [°] / Longitude [°] / ell. Height [m]",
            "cols_ps"     : ["","","Latitude","Longitude","h_ell"],
            "ecc_FRD"     : [0.0,0.0,0.0],
            "ascii_format": ["%14.9f","%14.9f","%10.4f"],
            "precision"   : "f64"
        },
        {
            "field"       : "LLH_DEG",
            "logprefix"   : "",
            "caption"     : "Latitude [°] / Longitude [°] / ell. Height [m]",
            "cols_ps"        : ["","_mySensor1","Latitude","Longitude","h_ell"],
            "ecc_FRD"     : [0.1,0.2,0.3],
            "ascii_format": ["%14.9f","%14.9f","%10.4f"],
            "precision"   : "f64"
        },    
        {
            "field"       : "LLH_DMS",
            "logprefix"   : "",
            "caption"     : "Latitude[dms] / Longitude[dms] / ell. Height [m]",
            "cols_ps"     : ["","_mySensor2","Lat_deg","Lat_min","Lat_sec","Lon_deg","Lon_min","Lon_sec","h_ell"],
            "ecc_FRD"     : [1.0,2.0,3.0],
            "ascii_format": ["%3d","%2d","%9.6f",   "%4d","%2d","%9.6f",   "%10.4f"],
            "precision"   : "f32"
        },
        {
            "field"       : "SIGMA_FRD",
            "logprefix"   : "",
            "caption"     : "Position Uncertainty in Vehicle-Front/Right/Vertical [m]",
            "cols_ps"     : ["","","Sig_Front","Sig_Right","Sig_Vert"],
            "ecc_FRD"     : [0.0,0.0,0.0],
            "ascii_format": ["%8.4f","%8.4f","%8.4f"],
            "precision"   : "f32"
        },
        {
            "field"       : "V_ENU",
            "logprefix"   : "",
            "caption"     : "East/North/Up velocity [m/s]",
            "cols_ps"     : ["","","vEast","vNorth","vUp"],
            "ecc_FRD"     : [0.0,0.0,0.0],
            "ascii_format": ["%10.4f","%10.4f","%10.4f"],
            "precision"   : "f32"
        },
        {
            "field"       : "SIGMA_VXYZ",
            "logprefix"   : "",
            "caption"     : "Velocity Uncertainty ECEF cartesian [m/s]",
            "cols_ps"     : ["","","Sig_vX","Sig_vY","Sig_vZ"],
            "ecc_FRD"     : [0.0,0.0,0.0],
            "ascii_format": ["%8.4f","%8.4f","%8.4f"],
            "precision"   : "f32"
        },
        {
            "field"       : "RPY_DEG",
            "logprefix"   : "",
            "caption"     : "Roll / Pitch / Heading [°]",
            "cols"        : ["Roll","Pitch","Heading"],
            "ascii_format": ["%9.4f","%9.4f","%9.4f"],
            "precision"   : "f64"
        },
        {
            "field"       : "BL",
            "logprefix"   : "dgnssX",
            "caption"     : "Effective Baseline Length [km]",
            "cols"        : ["Baseline"],
            "ascii_format": ["%7.2f"],
            "precision"   : "f32"
        }, 
        {
            "field"       : "SCF",
            "logprefix"   : "cons",
            "caption"     : "Estimated Scale Factor [ticks/m]",
            "cols"        : ["Odo_Scale"],
            "ascii_format": ["%9g"],
            "precision"   : "f32"
        },
        {
            "field"       : "ACC_BIAS",
            "logprefix"   : "IMUN",
            "caption"     : "Accelerometer Bias in IMU-frame [m/s^2]",
            "cols"        : ["AccB_x","AccB_y","AccB_z"],
            "prefix"      : "",
            "suffix"      : "",
            "ascii_format": ["%9g","%9g","%9g"],
            "precision"   : "f32"
        },
        {
            "field"       : "ACC_BIAS:STDFULL",
            "logprefix"   : "IMUN",
            "caption"     : "Accelerometer Bias uncertainty in IMU-frame [m/s^2]",
            "cols"        : ["Sig_AccB_x","Sig_AccB_y","Sig_AccB_z"],
            "prefix"      : "",
            "suffix"      : "",
            "ascii_format": ["%7g","%7g","%7g"],
            "precision"   : "f32"
        }       
    ]

}